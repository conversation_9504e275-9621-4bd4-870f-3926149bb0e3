version: '3.8'

services:
  # MongoDB
  mongo:
    image: mongo:7
    container_name: legalyard-mongo
    restart: unless-stopped
    ports:
      - "27017:27017"
    environment:
      MONGO_INITDB_ROOT_USERNAME: admin
      MONGO_INITDB_ROOT_PASSWORD: password
      MONGO_INITDB_DATABASE: legalyard
    volumes:
      - mongo_data:/data/db
      - ./docker/mongo-init.js:/docker-entrypoint-initdb.d/mongo-init.js:ro
    networks:
      - legalyard-network

  # PostgreSQL (Optional)
  postgres:
    image: postgres:15
    container_name: legalyard-postgres
    restart: unless-stopped
    ports:
      - "5432:5432"
    environment:
      POSTGRES_DB: legalyard
      POSTGRES_USER: user
      POSTGRES_PASSWORD: password
    volumes:
      - postgres_data:/var/lib/postgresql/data
    networks:
      - legalyard-network

  # Redis (for caching and sessions)
  redis:
    image: redis:7-alpine
    container_name: legalyard-redis
    restart: unless-stopped
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - legalyard-network

  # Core App
  core:
    build:
      context: .
      dockerfile: docker/Dockerfile.core
    container_name: legalyard-core
    restart: unless-stopped
    ports:
      - "8001:8001"
    environment:
      - NODE_ENV=development
      - CORE_PORT=8001
      - MONGO_URI=***************************************************************
      - POSTGRES_URL=**************************************/legalyard
    depends_on:
      - mongo
      - postgres
      - redis
    volumes:
      - .:/app
      - /app/node_modules
    networks:
      - legalyard-network

  # Gateway App
  gateway:
    build:
      context: .
      dockerfile: docker/Dockerfile.gateway
    container_name: legalyard-gateway
    restart: unless-stopped
    ports:
      - "8000:8000"
    environment:
      - NODE_ENV=development
      - GATEWAY_PORT=8000
      - CORE_SERVICE_URL=http://core:8001
    depends_on:
      - core
    volumes:
      - .:/app
      - /app/node_modules
    networks:
      - legalyard-network

volumes:
  mongo_data:
  postgres_data:
  redis_data:

networks:
  legalyard-network:
    driver: bridge
