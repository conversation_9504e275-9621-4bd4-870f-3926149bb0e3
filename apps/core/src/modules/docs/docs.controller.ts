import { Controller, Get, Param, Query } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiParam, ApiQuery } from '@nestjs/swagger';
import { DocsService } from './docs.service';

@ApiTags('docs')
@Controller('docs')
export class DocsController {
  constructor(private readonly docsService: DocsService) {}

  @Get('ai-onboarding')
  @ApiOperation({ summary: 'Get AI onboarding documentation overview' })
  @ApiResponse({ status: 200, description: 'AI onboarding documentation overview' })
  getAiOnboardingOverview() {
    return this.docsService.getAiOnboardingOverview();
  }

  @Get('ai-onboarding/architecture')
  @ApiOperation({ summary: 'Get system architecture documentation for AI agents' })
  @ApiResponse({ status: 200, description: 'System architecture documentation' })
  getArchitectureDoc() {
    return this.docsService.getArchitectureDoc();
  }

  @Get('ai-onboarding/authentication')
  @ApiOperation({ summary: 'Get authentication flow documentation for AI agents' })
  @ApiResponse({ status: 200, description: 'Authentication flow documentation' })
  getAuthenticationDoc() {
    return this.docsService.getAuthenticationDoc();
  }

  @Get('ai-onboarding/api-reference')
  @ApiOperation({ summary: 'Get complete API reference for AI agents' })
  @ApiResponse({ status: 200, description: 'Complete API reference' })
  @ApiQuery({ name: 'module', required: false, description: 'Filter by module (auth, admin, coder)' })
  getApiReference(@Query('module') module?: string) {
    return this.docsService.getApiReference(module);
  }

  @Get('ai-onboarding/integration-patterns')
  @ApiOperation({ summary: 'Get integration patterns for Next.js and other frameworks' })
  @ApiResponse({ status: 200, description: 'Integration patterns documentation' })
  @ApiQuery({ name: 'framework', required: false, description: 'Filter by framework (nextjs, react, vue)' })
  getIntegrationPatterns(@Query('framework') framework?: string) {
    return this.docsService.getIntegrationPatterns(framework);
  }

  @Get('ai-onboarding/typescript-definitions')
  @ApiOperation({ summary: 'Get TypeScript type definitions for AI agents' })
  @ApiResponse({ status: 200, description: 'TypeScript type definitions' })
  getTypeScriptDefinitions() {
    return this.docsService.getTypeScriptDefinitions();
  }

  @Get('ai-onboarding/examples')
  @ApiOperation({ summary: 'Get code examples for common scenarios' })
  @ApiResponse({ status: 200, description: 'Code examples' })
  @ApiQuery({ name: 'scenario', required: false, description: 'Filter by scenario (auth, crud, forms)' })
  getCodeExamples(@Query('scenario') scenario?: string) {
    return this.docsService.getCodeExamples(scenario);
  }

  @Get('ai-onboarding/requirements')
  @ApiOperation({ summary: 'Get system requirements and constraints for AI agents' })
  @ApiResponse({ status: 200, description: 'System requirements and constraints' })
  getRequirements() {
    return this.docsService.getRequirements();
  }

  @Get('ai-onboarding/testing')
  @ApiOperation({ summary: 'Get testing guidelines and procedures for AI agents' })
  @ApiResponse({ status: 200, description: 'Testing guidelines and procedures' })
  getTestingGuidelines() {
    return this.docsService.getTestingGuidelines();
  }

  @Get('ai-onboarding/endpoints/:module')
  @ApiOperation({ summary: 'Get detailed endpoint documentation for specific module' })
  @ApiResponse({ status: 200, description: 'Module-specific endpoint documentation' })
  @ApiParam({ name: 'module', description: 'Module name (auth, admin, coder, docs)' })
  getModuleEndpoints(@Param('module') module: string) {
    return this.docsService.getModuleEndpoints(module);
  }

  @Get('ai-onboarding/schemas')
  @ApiOperation({ summary: 'Get all data schemas and models for AI agents' })
  @ApiResponse({ status: 200, description: 'Data schemas and models' })
  @ApiQuery({ name: 'format', required: false, description: 'Response format (json, typescript, openapi)' })
  getSchemas(@Query('format') format?: string) {
    return this.docsService.getSchemas(format);
  }

  @Get('ai-onboarding/workflow')
  @ApiOperation({ summary: 'Get development workflow and best practices for AI agents' })
  @ApiResponse({ status: 200, description: 'Development workflow and best practices' })
  getWorkflow() {
    return this.docsService.getWorkflow();
  }

  @Get('openapi')
  @ApiOperation({ summary: 'Get OpenAPI specification for the entire system' })
  @ApiResponse({ status: 200, description: 'OpenAPI specification' })
  getOpenApiSpec() {
    return this.docsService.getOpenApiSpec();
  }

  @Get('ai-agent-complete')
  @ApiOperation({ summary: 'Complete AI agent onboarding - Single endpoint with all information' })
  @ApiResponse({ status: 200, description: 'Complete system information for AI agents' })
  getCompleteAiAgentInfo() {
    return this.docsService.getCompleteAiAgentInfo();
  }

  @Get('health')
  @ApiOperation({ summary: 'Check documentation service health' })
  @ApiResponse({ status: 200, description: 'Documentation service health status' })
  getHealth() {
    return {
      status: 'ok',
      service: 'docs',
      timestamp: new Date().toISOString(),
      availableEndpoints: [
        '/api/docs/ai-agent-complete',
        '/api/docs/ai-onboarding',
        '/api/docs/ai-onboarding/architecture',
        '/api/docs/ai-onboarding/authentication',
        '/api/docs/ai-onboarding/api-reference',
        '/api/docs/ai-onboarding/integration-patterns',
        '/api/docs/ai-onboarding/typescript-definitions',
        '/api/docs/ai-onboarding/examples',
        '/api/docs/ai-onboarding/requirements',
        '/api/docs/ai-onboarding/testing',
        '/api/docs/ai-onboarding/endpoints/:module',
        '/api/docs/ai-onboarding/schemas',
        '/api/docs/ai-onboarding/workflow',
        '/api/docs/openapi'
      ]
    };
  }
}
