import { Injectable } from '@nestjs/common';

@Injectable()
export class DocsService {

  getAiOnboardingOverview() {
    return {
      title: 'Legalyard Backend AI Onboarding Documentation',
      version: '1.0.0',
      description: 'Comprehensive documentation for AI agents to understand and integrate with Legalyard Backend APIs',
      lastUpdated: new Date().toISOString(),
      architecture: {
        type: 'NestJS Monorepo with 2 Applications',
        flow: 'Client → Gateway (3000) → Core (3001) → MongoDB',
        apps: {
          gateway: {
            port: 3000,
            purpose: 'API proxy, authentication, rate limiting, security',
            technology: 'NestJS + Express'
          },
          core: {
            port: 3001,
            purpose: 'Business logic, database operations, modules',
            technology: 'NestJS + Express + MongoDB'
          }
        }
      },
      modules: {
        auth: 'Authentication and authorization',
        admin: 'User and organization management',
        coder: 'Content management system (CMS)',
        docs: 'AI onboarding documentation'
      },
      keyFeatures: [
        'Bearer token authentication with refresh mechanism',
        'Rate limiting and security headers',
        'MongoDB integration with Mongoose',
        'Swagger API documentation',
        'TypeScript type safety',
        'Modular architecture for microservice extraction'
      ],
      integrationPoints: {
        primaryEndpoint: 'http://localhost:3000 (Gateway)',
        authenticationFlow: 'POST /api/auth/login → Bearer token → API calls',
        proxyPattern: '/api/core/* → Core app endpoints',
        documentation: {
          swagger: {
            gateway: 'http://localhost:3000/api/docs',
            core: 'http://localhost:3001/api/docs'
          },
          aiOnboarding: 'http://localhost:3000/api/core/docs/ai-onboarding'
        }
      },
      quickStart: {
        step1: 'Access documentation: GET /api/docs/ai-onboarding',
        step2: 'Understand architecture: GET /api/docs/ai-onboarding/architecture',
        step3: 'Learn authentication: GET /api/docs/ai-onboarding/authentication',
        step4: 'Get API reference: GET /api/docs/ai-onboarding/api-reference',
        step5: 'View integration patterns: GET /api/docs/ai-onboarding/integration-patterns',
        step6: 'Get TypeScript definitions: GET /api/docs/ai-onboarding/typescript-definitions',
        step7: 'See code examples: GET /api/docs/ai-onboarding/examples'
      },
      supportedFrameworks: ['Next.js', 'React', 'Vue.js', 'Angular', 'Vanilla JavaScript'],
      aiAgentGuidelines: [
        'Always use Gateway endpoints (port 3000) for client integration',
        'Implement proper authentication with token refresh',
        'Handle errors gracefully with consistent patterns',
        'Use TypeScript definitions for type safety',
        'Follow RESTful conventions',
        'Implement proper loading states',
        'Validate user permissions before UI actions',
        'Test API endpoints before implementing UI'
      ]
    };
  }

  getArchitectureDoc() {
    return {
      title: 'System Architecture Documentation',
      overview: {
        pattern: 'API Gateway + Microservice-Ready Monolith',
        flow: 'Client Applications → Gateway App → Core App → Database',
        benefits: [
          'Centralized authentication and security',
          'Rate limiting and request validation',
          'Easy microservice extraction',
          'Consistent API patterns',
          'Scalable architecture'
        ]
      },
      components: {
        gateway: {
          port: 3000,
          responsibilities: [
            'API proxy and routing',
            'Authentication token validation',
            'Rate limiting (100 req/min default)',
            'CORS and security headers',
            'Request/response logging',
            'IP allowlisting for admin access'
          ],
          endpoints: {
            health: 'GET /api/health',
            auth: 'POST /api/auth/* (forwarded to core)',
            proxy: 'ALL /api/core/* (proxied to core)'
          },
          technology: 'NestJS + Express + Swagger'
        },
        core: {
          port: 3001,
          responsibilities: [
            'Business logic processing',
            'Database operations',
            'User management',
            'Content management',
            'Authentication logic',
            'Audit logging'
          ],
          modules: {
            auth: 'Authentication and session management',
            admin: 'User and organization administration',
            coder: 'Content management system',
            shared: 'Common utilities and services',
            docs: 'AI onboarding documentation'
          },
          technology: 'NestJS + Express + MongoDB + Mongoose'
        },
        database: {
          type: 'MongoDB',
          collections: [
            'users - User accounts and profiles',
            'organizations - Tenant organizations',
            'sessions - User sessions and devices',
            'content - CMS content items',
            'audit_logs - System audit trail'
          ],
          features: [
            'Automatic indexing',
            'TTL for session expiration',
            'Full-text search capabilities',
            'Aggregation pipelines'
          ]
        }
      },
      requestFlow: {
        authentication: [
          '1. Client sends credentials to Gateway /api/auth/login',
          '2. Gateway forwards to Core /api/auth/login',
          '3. Core validates credentials and creates session',
          '4. Core returns access + refresh tokens',
          '5. Gateway forwards tokens to client',
          '6. Client stores tokens for future requests'
        ],
        apiCalls: [
          '1. Client sends request to Gateway with Bearer token',
          '2. Gateway validates token with Core',
          '3. Gateway adds user context headers',
          '4. Gateway proxies request to Core',
          '5. Core processes business logic',
          '6. Core returns response to Gateway',
          '7. Gateway forwards response to client'
        ]
      },
      security: {
        authentication: 'Bearer tokens (JWT-like opaque tokens)',
        authorization: 'Role-based access control (RBAC)',
        rateLimit: '100 requests per minute per IP',
        cors: 'Configurable origins',
        headers: 'Helmet security headers',
        ipAllowlist: 'Admin access restriction',
        deviceTracking: 'Maximum 2 devices per user'
      },
      scalability: {
        horizontal: 'Multiple Gateway instances behind load balancer',
        vertical: 'Core app can be scaled independently',
        microservices: 'Modules can be extracted to separate services',
        database: 'MongoDB replica sets and sharding'
      }
    };
  }

  getAuthenticationDoc() {
    return {
      title: 'Authentication Flow Documentation',
      overview: {
        pattern: 'Opaque Token + Refresh Token (Phantom Pattern)',
        tokenTypes: {
          accessToken: {
            purpose: 'API access authorization',
            lifetime: '15 minutes',
            storage: 'Memory or secure storage',
            format: 'Opaque string (not JWT for security)'
          },
          refreshToken: {
            purpose: 'Access token renewal',
            lifetime: '7 days',
            storage: 'Secure HTTP-only cookie or secure storage',
            format: 'Opaque string'
          }
        },
        security: [
          'Opaque tokens prevent token inspection',
          'Short access token lifetime reduces exposure',
          'Refresh tokens enable seamless renewal',
          'Device tracking prevents token sharing',
          'Session invalidation on logout'
        ]
      },
      endpoints: {
        login: {
          method: 'POST',
          url: '/api/auth/login',
          description: 'Authenticate user and receive tokens',
          request: {
            email: 'string (required)',
            password: 'string (required)',
            totpCode: 'string (optional, required if MFA enabled)'
          },
          response: {
            accessToken: 'string',
            refreshToken: 'string',
            user: 'User object',
            expiresIn: 'number (seconds)'
          },
          errors: [
            '400 - Invalid request format',
            '401 - Invalid credentials',
            '429 - Too many login attempts'
          ]
        },
        register: {
          method: 'POST',
          url: '/api/auth/register',
          description: 'Create new user account',
          request: {
            email: 'string (required)',
            password: 'string (required)',
            username: 'string (optional)',
            firstName: 'string (optional)',
            lastName: 'string (optional)'
          },
          response: 'Same as login response',
          errors: [
            '400 - Validation errors',
            '409 - Email already exists'
          ]
        },
        refresh: {
          method: 'POST',
          url: '/api/auth/refresh',
          description: 'Refresh access token',
          request: {
            refreshToken: 'string (required)'
          },
          response: {
            accessToken: 'string',
            refreshToken: 'string (new)',
            expiresIn: 'number'
          },
          errors: [
            '401 - Invalid refresh token',
            '403 - Token expired or revoked'
          ]
        },
        logout: {
          method: 'POST',
          url: '/api/auth/logout',
          description: 'Invalidate user session',
          headers: {
            Authorization: 'Bearer <access_token>'
          },
          response: {
            message: 'Logout successful'
          }
        },
        verify: {
          method: 'GET',
          url: '/api/auth/verify',
          description: 'Verify token and get user info',
          headers: {
            Authorization: 'Bearer <access_token>'
          },
          response: {
            user: 'User object',
            tokenValid: 'boolean'
          }
        }
      },
      implementationPatterns: {
        clientSide: {
          tokenStorage: [
            'localStorage for development/testing',
            'sessionStorage for temporary sessions',
            'Secure HTTP-only cookies for production',
            'React Context or Zustand for state management'
          ],
          refreshLogic: [
            'Automatic refresh on 401 responses',
            'Proactive refresh before expiration',
            'Retry failed requests after refresh',
            'Logout on refresh failure'
          ]
        },
        errorHandling: [
          'Check response status codes',
          'Handle network errors gracefully',
          'Implement exponential backoff for retries',
          'Clear tokens on authentication failures',
          'Redirect to login on session expiry'
        ]
      },
      securityConsiderations: [
        'Never store tokens in plain text',
        'Use HTTPS in production',
        'Implement CSRF protection',
        'Validate token on every request',
        'Log authentication events',
        'Monitor for suspicious activity',
        'Implement account lockout policies'
      ]
    };
  }

  getApiReference(module?: string) {
    const allEndpoints = {
      gateway: {
        baseUrl: 'http://localhost:3000',
        description: 'API Gateway - Central entry point for all client applications',
        endpoints: {
          health: {
            method: 'GET',
            path: '/api/health',
            description: 'Gateway health check',
            authentication: false,
            response: { status: 'string', timestamp: 'string', service: 'string' }
          },
          auth: {
            login: {
              method: 'POST',
              path: '/api/auth/login',
              description: 'User authentication (forwarded to core)',
              authentication: false,
              request: { email: 'string', password: 'string', totpCode: 'string (optional)' },
              response: { accessToken: 'string', refreshToken: 'string', user: 'User', expiresIn: 'number' }
            },
            register: { method: 'POST', path: '/api/auth/register', description: 'User registration (forwarded to core)', authentication: false },
            refresh: { method: 'POST', path: '/api/auth/refresh', description: 'Token refresh (forwarded to core)', authentication: false },
            logout: { method: 'POST', path: '/api/auth/logout', description: 'User logout (forwarded to core)', authentication: true },
            verify: { method: 'GET', path: '/api/auth/verify', description: 'Token verification (forwarded to core)', authentication: true }
          },
          proxy: {
            method: 'ALL',
            path: '/api/core/*',
            description: 'Proxy all requests to Core app',
            authentication: true,
            note: 'Requests are forwarded to Core app with user context headers'
          }
        }
      },
      core: {
        baseUrl: 'http://localhost:3001 (Access via Gateway: http://localhost:3000/api/core)',
        description: 'Core Business Logic - Contains all application modules',
        modules: {
          auth: {
            description: 'Authentication and session management',
            endpoints: {
              login: { method: 'POST', path: '/api/auth/login', description: 'Process user authentication', authentication: false },
              validate: { method: 'POST', path: '/api/auth/validate', description: 'Validate token (internal use)', authentication: false }
            }
          },
          admin: {
            description: 'User and organization administration',
            endpoints: {
              getUsers: { method: 'GET', path: '/api/admin/users', description: 'List all users', authentication: true, permissions: ['admin'] },
              createUser: { method: 'POST', path: '/api/admin/users', description: 'Create new user', authentication: true, permissions: ['admin'] },
              updateUser: { method: 'PUT', path: '/api/admin/users/:id', description: 'Update user', authentication: true, permissions: ['admin'] },
              deleteUser: { method: 'DELETE', path: '/api/admin/users/:id', description: 'Delete user', authentication: true, permissions: ['admin'] },
              getTenants: { method: 'GET', path: '/api/admin/tenants', description: 'List organizations', authentication: true, permissions: ['admin'] },
              createTenant: { method: 'POST', path: '/api/admin/tenants', description: 'Create organization', authentication: true, permissions: ['admin'] },
              getPermissions: { method: 'GET', path: '/api/admin/permissions', description: 'List permissions', authentication: true, permissions: ['admin'] },
              getAuditLogs: { method: 'GET', path: '/api/admin/audit-logs', description: 'View audit logs', authentication: true, permissions: ['admin'] }
            }
          },
          coder: {
            description: 'Content Management System (CMS)',
            endpoints: {
              getContent: { method: 'GET', path: '/api/coder/content', description: 'List content items', authentication: true },
              createContent: { method: 'POST', path: '/api/coder/content', description: 'Create new content', authentication: true, permissions: ['coder', 'admin'] },
              updateContent: { method: 'PUT', path: '/api/coder/content/:id', description: 'Update content', authentication: true, permissions: ['coder', 'admin'] },
              deleteContent: { method: 'DELETE', path: '/api/coder/content/:id', description: 'Delete content', authentication: true, permissions: ['coder', 'admin'] },
              getForms: { method: 'GET', path: '/api/coder/forms', description: 'List forms', authentication: true },
              createForm: { method: 'POST', path: '/api/coder/forms', description: 'Create form', authentication: true, permissions: ['coder', 'admin'] },
              getThemes: { method: 'GET', path: '/api/coder/themes', description: 'List themes', authentication: true },
              createTheme: { method: 'POST', path: '/api/coder/themes', description: 'Create theme', authentication: true, permissions: ['coder', 'admin'] },
              getNewsletters: { method: 'GET', path: '/api/coder/newsletter', description: 'List newsletters', authentication: true },
              createNewsletter: { method: 'POST', path: '/api/coder/newsletter', description: 'Create newsletter', authentication: true, permissions: ['coder', 'admin'] }
            }
          },
          docs: {
            description: 'AI Onboarding Documentation',
            endpoints: {
              overview: { method: 'GET', path: '/api/docs/ai-onboarding', description: 'AI onboarding overview', authentication: false },
              architecture: { method: 'GET', path: '/api/docs/ai-onboarding/architecture', description: 'System architecture', authentication: false },
              authentication: { method: 'GET', path: '/api/docs/ai-onboarding/authentication', description: 'Authentication flow', authentication: false },
              apiReference: { method: 'GET', path: '/api/docs/ai-onboarding/api-reference', description: 'Complete API reference', authentication: false },
              integrationPatterns: { method: 'GET', path: '/api/docs/ai-onboarding/integration-patterns', description: 'Framework integration patterns', authentication: false },
              typeScriptDefinitions: { method: 'GET', path: '/api/docs/ai-onboarding/typescript-definitions', description: 'TypeScript type definitions', authentication: false },
              examples: { method: 'GET', path: '/api/docs/ai-onboarding/examples', description: 'Code examples', authentication: false },
              requirements: { method: 'GET', path: '/api/docs/ai-onboarding/requirements', description: 'System requirements', authentication: false },
              testing: { method: 'GET', path: '/api/docs/ai-onboarding/testing', description: 'Testing guidelines', authentication: false },
              moduleEndpoints: { method: 'GET', path: '/api/docs/ai-onboarding/endpoints/:module', description: 'Module-specific endpoints', authentication: false },
              schemas: { method: 'GET', path: '/api/docs/ai-onboarding/schemas', description: 'Data schemas', authentication: false },
              workflow: { method: 'GET', path: '/api/docs/ai-onboarding/workflow', description: 'Development workflow', authentication: false },
              openapi: { method: 'GET', path: '/api/docs/openapi', description: 'OpenAPI specification', authentication: false }
            }
          }
        }
      }
    };

    if (module) {
      return allEndpoints.core.modules[module] || { error: `Module '${module}' not found`, availableModules: Object.keys(allEndpoints.core.modules) };
    }

    return allEndpoints;
  }

  getIntegrationPatterns(framework?: string) {
    const patterns = {
      nextjs: {
        framework: 'Next.js',
        description: 'React framework with SSR/SSG capabilities',
        setup: {
          environment: {
            'NEXT_PUBLIC_API_URL': 'http://localhost:3000',
            'NEXT_PUBLIC_APP_NAME': 'Legalyard',
            'NEXT_PUBLIC_APP_VERSION': '1.0.0'
          },
          dependencies: ['next', 'react', 'react-dom', 'typescript'],
          devDependencies: ['@types/react', '@types/node']
        },
        patterns: {
          apiClient: 'Centralized API client with token management',
          authentication: 'Context-based auth state with token refresh',
          routing: 'Protected routes with middleware',
          stateManagement: 'React Context or Zustand',
          errorHandling: 'Global error boundary with retry logic'
        },
        examples: {
          apiClient: '/api/docs/ai-onboarding/examples?scenario=nextjs-api-client',
          authHook: '/api/docs/ai-onboarding/examples?scenario=nextjs-auth-hook',
          protectedRoute: '/api/docs/ai-onboarding/examples?scenario=nextjs-protected-route'
        }
      },
      react: {
        framework: 'React',
        description: 'JavaScript library for building user interfaces',
        setup: {
          routing: 'React Router for navigation',
          stateManagement: 'Redux Toolkit or Context API',
          httpClient: 'Axios or Fetch API'
        },
        patterns: {
          authentication: 'Higher-order components or hooks',
          routing: 'Protected routes with React Router',
          stateManagement: 'Redux slices for auth and data'
        }
      },
      vue: {
        framework: 'Vue.js',
        description: 'Progressive JavaScript framework',
        setup: {
          routing: 'Vue Router with navigation guards',
          stateManagement: 'Pinia or Vuex',
          httpClient: 'Axios with interceptors'
        },
        patterns: {
          authentication: 'Composables for auth state',
          routing: 'Route guards for protection',
          stateManagement: 'Pinia stores for data management'
        }
      },
      angular: {
        framework: 'Angular',
        description: 'Platform for building mobile and desktop web applications',
        setup: {
          routing: 'Angular Router with guards',
          stateManagement: 'NgRx or services',
          httpClient: 'HttpClient with interceptors'
        },
        patterns: {
          authentication: 'Guards and interceptors',
          routing: 'Route guards and resolvers',
          stateManagement: 'Services with RxJS'
        }
      }
    };

    if (framework) {
      return patterns[framework] || { error: `Framework '${framework}' not supported`, availableFrameworks: Object.keys(patterns) };
    }

    return patterns;
  }

  getTypeScriptDefinitions() {
    return {
      title: 'TypeScript Definitions for Legalyard Backend',
      description: 'Comprehensive type definitions for all API interactions',
      categories: {
        apiTypes: {
          ApiResponse: 'interface ApiResponse<T = any> { data?: T; error?: string; statusCode?: number; }',
          PaginatedResponse: 'interface PaginatedResponse<T> { data: T[]; pagination: { page: number; limit: number; total: number; pages: number; }; }',
          ErrorResponse: 'interface ErrorResponse { statusCode: number; message: string; error: string; details?: Array<{ field: string; message: string; }>; }'
        },
        authTypes: {
          LoginCredentials: 'interface LoginCredentials { email: string; password: string; totpCode?: string; }',
          AuthResponse: 'interface AuthResponse { accessToken: string; refreshToken: string; user: User; expiresIn: number; }',
          User: 'interface User { id: string; email: string; username?: string; roles: string[]; organizationId?: string; isActive: boolean; createdAt: string; updatedAt: string; }'
        },
        contentTypes: {
          Content: 'interface Content { id: string; title: string; slug: string; body: string; status: "draft" | "published" | "archived"; type: "page" | "post" | "article"; authorId: string; tags: string[]; metadata: Record<string, any>; createdAt: string; updatedAt: string; }',
          CreateContentData: 'interface CreateContentData { title: string; body: string; excerpt?: string; type: "page" | "post" | "article"; status?: "draft" | "published" | "archived"; tags?: string[]; metadata?: Record<string, any>; }'
        },
        serviceTypes: {
          AuthService: 'interface AuthService { login(credentials: LoginCredentials): Promise<ApiResponse<AuthResponse>>; logout(): Promise<ApiResponse<void>>; refreshToken(): Promise<ApiResponse<AuthResponse>>; }',
          ContentService: 'interface ContentService { getContent(): Promise<ApiResponse<PaginatedResponse<Content>>>; createContent(data: CreateContentData): Promise<ApiResponse<Content>>; }'
        }
      },
      fullDefinitionsUrl: '/api/docs/ai-onboarding/schemas?format=typescript',
      usage: {
        import: 'Import types from generated definitions file',
        validation: 'Use with runtime validation libraries like Zod',
        codegen: 'Generate from OpenAPI specification'
      }
    };
  }

  getCodeExamples(scenario?: string) {
    const examples = {
      authentication: {
        title: 'Authentication Examples',
        login: {
          description: 'Complete login implementation with error handling',
          code: `
const login = async (credentials: LoginCredentials): Promise<AuthResponse | null> => {
  try {
    const response = await fetch('http://localhost:3000/api/auth/login', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(credentials)
    });

    const result = await response.json();

    if (response.ok) {
      localStorage.setItem('accessToken', result.accessToken);
      localStorage.setItem('refreshToken', result.refreshToken);
      return result;
    } else {
      throw new Error(result.message || 'Login failed');
    }
  } catch (error) {
    console.error('Login error:', error);
    return null;
  }
};
          `
        },
        tokenRefresh: {
          description: 'Automatic token refresh with retry logic',
          code: `
const refreshToken = async (): Promise<string | null> => {
  const refreshToken = localStorage.getItem('refreshToken');
  if (!refreshToken) return null;

  try {
    const response = await fetch('http://localhost:3000/api/auth/refresh', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ refreshToken })
    });

    if (response.ok) {
      const result = await response.json();
      localStorage.setItem('accessToken', result.accessToken);
      return result.accessToken;
    }
  } catch (error) {
    console.error('Token refresh failed:', error);
  }

  return null;
};
          `
        }
      },
      crud: {
        title: 'CRUD Operations Examples',
        createContent: {
          description: 'Create new content with validation',
          code: `
const createContent = async (contentData: CreateContentData): Promise<Content | null> => {
  const token = localStorage.getItem('accessToken');

  try {
    const response = await fetch('http://localhost:3000/api/core/coder/content', {
      method: 'POST',
      headers: {
        'Authorization': \`Bearer \${token}\`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(contentData)
    });

    if (response.status === 401) {
      const newToken = await refreshToken();
      if (newToken) {
        return createContent(contentData); // Retry with new token
      }
      throw new Error('Authentication failed');
    }

    const result = await response.json();
    return response.ok ? result.data : null;
  } catch (error) {
    console.error('Create content error:', error);
    return null;
  }
};
          `
        }
      },
      forms: {
        title: 'Form Handling Examples',
        genericForm: {
          description: 'Generic form submission with validation',
          code: `
const handleFormSubmit = async <T>(
  endpoint: string,
  formData: any,
  method: 'POST' | 'PUT' = 'POST'
): Promise<{ success: boolean; data?: T; error?: string }> => {
  const token = localStorage.getItem('accessToken');

  try {
    const response = await fetch(\`http://localhost:3000\${endpoint}\`, {
      method,
      headers: {
        'Authorization': \`Bearer \${token}\`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(formData)
    });

    const result = await response.json();

    if (response.ok) {
      return { success: true, data: result.data };
    } else {
      return { success: false, error: result.message };
    }
  } catch (error) {
    return { success: false, error: 'Network error occurred' };
  }
};
          `
        }
      }
    };

    if (scenario) {
      return examples[scenario] || { error: `Scenario '${scenario}' not found`, availableScenarios: Object.keys(examples) };
    }

    return examples;
  }

  getRequirements() {
    return {
      title: 'System Requirements and Constraints',
      systemRequirements: {
        backend: {
          node: '>=20.0.0',
          npm: '>=9.0.0',
          mongodb: '>=7.0.0',
          memory: '>=2GB RAM',
          storage: '>=10GB available space'
        },
        frontend: {
          node: '>=18.0.0',
          browsers: ['Chrome >=90', 'Firefox >=88', 'Safari >=14', 'Edge >=90'],
          frameworks: ['Next.js >=13', 'React >=18', 'Vue >=3', 'Angular >=15']
        }
      },
      apiConstraints: {
        authentication: {
          tokenExpiry: '15 minutes (access), 7 days (refresh)',
          maxDevices: 2,
          sessionTimeout: '1 hour of inactivity',
          mfaRequired: 'For admin accounts'
        },
        rateLimiting: {
          default: '100 requests per minute per IP',
          auth: '10 login attempts per minute per IP',
          admin: '50 requests per minute per user'
        },
        dataLimits: {
          requestSize: '10MB maximum',
          responseSize: '50MB maximum',
          fileUpload: '100MB maximum',
          pagination: '100 items maximum per page'
        }
      },
      securityRequirements: {
        https: 'Required in production',
        cors: 'Configured for specific origins',
        headers: 'Security headers via Helmet',
        validation: 'Input validation on all endpoints',
        sanitization: 'XSS protection and data sanitization'
      },
      performanceRequirements: {
        responseTime: '<200ms for simple queries, <2s for complex operations',
        availability: '99.9% uptime',
        concurrency: '1000 concurrent users',
        throughput: '10,000 requests per minute'
      }
    };
  }

  getTestingGuidelines() {
    return {
      title: 'Testing Guidelines and Procedures',
      apiTesting: {
        tools: ['curl', 'Postman', 'Insomnia', 'Jest', 'Supertest'],
        procedures: {
          authentication: [
            'Test login with valid credentials',
            'Test login with invalid credentials',
            'Test token refresh flow',
            'Test token expiration handling',
            'Test logout functionality'
          ],
          endpoints: [
            'Test all HTTP methods',
            'Test with and without authentication',
            'Test with different user roles',
            'Test input validation',
            'Test error responses'
          ],
          integration: [
            'Test complete user workflows',
            'Test data consistency',
            'Test concurrent operations',
            'Test rate limiting',
            'Test error recovery'
          ]
        }
      },
      frontendTesting: {
        tools: ['Jest', 'React Testing Library', 'Cypress', 'Playwright'],
        procedures: {
          components: [
            'Test component rendering',
            'Test user interactions',
            'Test API integration',
            'Test error states',
            'Test loading states'
          ],
          integration: [
            'Test authentication flows',
            'Test protected routes',
            'Test form submissions',
            'Test data fetching',
            'Test error handling'
          ]
        }
      },
      testCommands: {
        backend: {
          unit: 'npm run test',
          e2e: 'npm run test:e2e',
          coverage: 'npm run test:cov'
        },
        apiEndpoints: {
          health: 'curl http://localhost:3000/api/health',
          login: 'curl -X POST http://localhost:3000/api/auth/login -H "Content-Type: application/json" -d \'{"email":"<EMAIL>","password":"password123"}\'',
          protectedEndpoint: 'curl http://localhost:3000/api/core/admin/users -H "Authorization: Bearer YOUR_TOKEN"'
        }
      }
    };
  }

  getModuleEndpoints(module: string) {
    const moduleEndpoints = {
      auth: {
        module: 'Authentication Module',
        description: 'Handles user authentication and session management',
        endpoints: {
          login: { method: 'POST', path: '/api/auth/login', description: 'User login', public: true },
          register: { method: 'POST', path: '/api/auth/register', description: 'User registration', public: true },
          refresh: { method: 'POST', path: '/api/auth/refresh', description: 'Token refresh', public: true },
          logout: { method: 'POST', path: '/api/auth/logout', description: 'User logout', protected: true },
          verify: { method: 'GET', path: '/api/auth/verify', description: 'Token verification', protected: true },
          validate: { method: 'POST', path: '/api/auth/validate', description: 'Token validation (internal)', internal: true }
        }
      },
      admin: {
        module: 'Admin Module',
        description: 'User and organization management for administrators',
        endpoints: {
          getUsers: { method: 'GET', path: '/api/admin/users', description: 'List users', roles: ['admin'] },
          createUser: { method: 'POST', path: '/api/admin/users', description: 'Create user', roles: ['admin'] },
          updateUser: { method: 'PUT', path: '/api/admin/users/:id', description: 'Update user', roles: ['admin'] },
          deleteUser: { method: 'DELETE', path: '/api/admin/users/:id', description: 'Delete user', roles: ['admin'] },
          getTenants: { method: 'GET', path: '/api/admin/tenants', description: 'List organizations', roles: ['admin'] },
          createTenant: { method: 'POST', path: '/api/admin/tenants', description: 'Create organization', roles: ['admin'] },
          getPermissions: { method: 'GET', path: '/api/admin/permissions', description: 'List permissions', roles: ['admin'] },
          getAuditLogs: { method: 'GET', path: '/api/admin/audit-logs', description: 'View audit logs', roles: ['admin'] }
        }
      },
      coder: {
        module: 'Coder Module (CMS)',
        description: 'Content management system for content creators',
        endpoints: {
          getContent: { method: 'GET', path: '/api/coder/content', description: 'List content', roles: ['coder', 'admin'] },
          createContent: { method: 'POST', path: '/api/coder/content', description: 'Create content', roles: ['coder', 'admin'] },
          updateContent: { method: 'PUT', path: '/api/coder/content/:id', description: 'Update content', roles: ['coder', 'admin'] },
          deleteContent: { method: 'DELETE', path: '/api/coder/content/:id', description: 'Delete content', roles: ['coder', 'admin'] },
          getForms: { method: 'GET', path: '/api/coder/forms', description: 'List forms', roles: ['coder', 'admin'] },
          createForm: { method: 'POST', path: '/api/coder/forms', description: 'Create form', roles: ['coder', 'admin'] },
          getThemes: { method: 'GET', path: '/api/coder/themes', description: 'List themes', roles: ['coder', 'admin'] },
          createTheme: { method: 'POST', path: '/api/coder/themes', description: 'Create theme', roles: ['coder', 'admin'] },
          getNewsletters: { method: 'GET', path: '/api/coder/newsletter', description: 'List newsletters', roles: ['coder', 'admin'] },
          createNewsletter: { method: 'POST', path: '/api/coder/newsletter', description: 'Create newsletter', roles: ['coder', 'admin'] }
        }
      },
      docs: {
        module: 'Documentation Module',
        description: 'AI onboarding and API documentation',
        endpoints: {
          overview: { method: 'GET', path: '/api/docs/ai-onboarding', description: 'AI onboarding overview', public: true },
          architecture: { method: 'GET', path: '/api/docs/ai-onboarding/architecture', description: 'System architecture', public: true },
          authentication: { method: 'GET', path: '/api/docs/ai-onboarding/authentication', description: 'Authentication flow', public: true },
          apiReference: { method: 'GET', path: '/api/docs/ai-onboarding/api-reference', description: 'API reference', public: true },
          integrationPatterns: { method: 'GET', path: '/api/docs/ai-onboarding/integration-patterns', description: 'Integration patterns', public: true },
          typeScriptDefinitions: { method: 'GET', path: '/api/docs/ai-onboarding/typescript-definitions', description: 'TypeScript definitions', public: true },
          examples: { method: 'GET', path: '/api/docs/ai-onboarding/examples', description: 'Code examples', public: true },
          requirements: { method: 'GET', path: '/api/docs/ai-onboarding/requirements', description: 'System requirements', public: true },
          testing: { method: 'GET', path: '/api/docs/ai-onboarding/testing', description: 'Testing guidelines', public: true },
          schemas: { method: 'GET', path: '/api/docs/ai-onboarding/schemas', description: 'Data schemas', public: true },
          workflow: { method: 'GET', path: '/api/docs/ai-onboarding/workflow', description: 'Development workflow', public: true },
          openapi: { method: 'GET', path: '/api/docs/openapi', description: 'OpenAPI specification', public: true }
        }
      }
    };

    return moduleEndpoints[module] || { error: `Module '${module}' not found`, availableModules: Object.keys(moduleEndpoints) };
  }

  getSchemas(format?: string) {
    const schemas = {
      title: 'Data Schemas and Models',
      format: format || 'json',
      schemas: {
        User: {
          type: 'object',
          properties: {
            id: { type: 'string', description: 'Unique user identifier' },
            email: { type: 'string', format: 'email', description: 'User email address' },
            username: { type: 'string', description: 'Optional username' },
            firstName: { type: 'string', description: 'User first name' },
            lastName: { type: 'string', description: 'User last name' },
            roles: { type: 'array', items: { type: 'string' }, description: 'User roles' },
            organizationId: { type: 'string', description: 'Organization ID' },
            isActive: { type: 'boolean', description: 'Account status' },
            isEmailVerified: { type: 'boolean', description: 'Email verification status' },
            createdAt: { type: 'string', format: 'date-time', description: 'Creation timestamp' },
            updatedAt: { type: 'string', format: 'date-time', description: 'Last update timestamp' }
          },
          required: ['id', 'email', 'roles', 'isActive', 'isEmailVerified', 'createdAt', 'updatedAt']
        },
        Content: {
          type: 'object',
          properties: {
            id: { type: 'string', description: 'Unique content identifier' },
            title: { type: 'string', description: 'Content title' },
            slug: { type: 'string', description: 'URL-friendly identifier' },
            body: { type: 'string', description: 'Content body' },
            excerpt: { type: 'string', description: 'Content excerpt' },
            status: { type: 'string', enum: ['draft', 'published', 'archived'], description: 'Content status' },
            type: { type: 'string', enum: ['page', 'post', 'article'], description: 'Content type' },
            authorId: { type: 'string', description: 'Author user ID' },
            tags: { type: 'array', items: { type: 'string' }, description: 'Content tags' },
            metadata: { type: 'object', description: 'Additional metadata' },
            publishedAt: { type: 'string', format: 'date-time', description: 'Publication timestamp' },
            createdAt: { type: 'string', format: 'date-time', description: 'Creation timestamp' },
            updatedAt: { type: 'string', format: 'date-time', description: 'Last update timestamp' }
          },
          required: ['id', 'title', 'slug', 'body', 'status', 'type', 'authorId', 'tags', 'createdAt', 'updatedAt']
        },
        Organization: {
          type: 'object',
          properties: {
            id: { type: 'string', description: 'Unique organization identifier' },
            name: { type: 'string', description: 'Organization name' },
            domain: { type: 'string', description: 'Organization domain' },
            settings: {
              type: 'object',
              properties: {
                allowedDomains: { type: 'array', items: { type: 'string' } },
                maxUsers: { type: 'number' },
                features: { type: 'array', items: { type: 'string' } }
              }
            },
            isActive: { type: 'boolean', description: 'Organization status' },
            createdAt: { type: 'string', format: 'date-time', description: 'Creation timestamp' },
            updatedAt: { type: 'string', format: 'date-time', description: 'Last update timestamp' }
          },
          required: ['id', 'name', 'settings', 'isActive', 'createdAt', 'updatedAt']
        }
      }
    };

    if (format === 'typescript') {
      return {
        ...schemas,
        typeScriptDefinitions: `
interface User {
  id: string;
  email: string;
  username?: string;
  firstName?: string;
  lastName?: string;
  roles: string[];
  organizationId?: string;
  isActive: boolean;
  isEmailVerified: boolean;
  createdAt: string;
  updatedAt: string;
}

interface Content {
  id: string;
  title: string;
  slug: string;
  body: string;
  excerpt?: string;
  status: 'draft' | 'published' | 'archived';
  type: 'page' | 'post' | 'article';
  authorId: string;
  tags: string[];
  metadata: Record<string, any>;
  publishedAt?: string;
  createdAt: string;
  updatedAt: string;
}

interface Organization {
  id: string;
  name: string;
  domain?: string;
  settings: {
    allowedDomains?: string[];
    maxUsers?: number;
    features: string[];
  };
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}
        `
      };
    }

    return schemas;
  }

  getWorkflow() {
    return {
      title: 'Development Workflow and Best Practices',
      aiAgentWorkflow: {
        step1: {
          title: 'Understand Architecture',
          description: 'Get system overview and architecture',
          endpoint: 'GET /api/docs/ai-onboarding/architecture',
          action: 'Study the Gateway → Core → Database flow'
        },
        step2: {
          title: 'Learn Authentication',
          description: 'Understand token-based authentication',
          endpoint: 'GET /api/docs/ai-onboarding/authentication',
          action: 'Implement login/logout and token refresh patterns'
        },
        step3: {
          title: 'Explore API Endpoints',
          description: 'Get complete API reference',
          endpoint: 'GET /api/docs/ai-onboarding/api-reference',
          action: 'Map out required endpoints for your application'
        },
        step4: {
          title: 'Choose Integration Pattern',
          description: 'Select framework-specific patterns',
          endpoint: 'GET /api/docs/ai-onboarding/integration-patterns',
          action: 'Implement API client and authentication service'
        },
        step5: {
          title: 'Implement Type Safety',
          description: 'Use TypeScript definitions',
          endpoint: 'GET /api/docs/ai-onboarding/typescript-definitions',
          action: 'Add type definitions to your project'
        },
        step6: {
          title: 'Build Components',
          description: 'Use code examples as reference',
          endpoint: 'GET /api/docs/ai-onboarding/examples',
          action: 'Implement UI components with API integration'
        },
        step7: {
          title: 'Test Integration',
          description: 'Validate implementation',
          endpoint: 'GET /api/docs/ai-onboarding/testing',
          action: 'Test authentication flows and API calls'
        }
      },
      bestPractices: {
        apiIntegration: [
          'Always use Gateway endpoints (port 3000) for client requests',
          'Implement proper error handling with retry logic',
          'Use TypeScript for type safety',
          'Handle token refresh automatically',
          'Implement loading states for better UX',
          'Validate user permissions before showing UI elements'
        ],
        security: [
          'Store tokens securely (avoid localStorage in production)',
          'Implement CSRF protection',
          'Use HTTPS in production',
          'Validate all user inputs',
          'Log security events',
          'Implement proper session management'
        ],
        performance: [
          'Implement request caching where appropriate',
          'Use pagination for large datasets',
          'Optimize bundle size',
          'Implement lazy loading',
          'Use proper error boundaries',
          'Monitor API response times'
        ]
      },
      troubleshooting: {
        commonIssues: {
          authenticationFailed: {
            symptoms: '401 Unauthorized responses',
            solutions: ['Check token validity', 'Implement token refresh', 'Verify API endpoint URLs']
          },
          corsErrors: {
            symptoms: 'CORS policy errors in browser',
            solutions: ['Configure CORS_ORIGIN environment variable', 'Use proper request headers', 'Check API base URL']
          },
          rateLimitExceeded: {
            symptoms: '429 Too Many Requests',
            solutions: ['Implement request throttling', 'Add retry with exponential backoff', 'Check rate limit headers']
          }
        }
      }
    };
  }

  getOpenApiSpec() {
    return {
      openapi: '3.0.0',
      info: {
        title: 'Legalyard Backend API',
        version: '1.0.0',
        description: 'Comprehensive API for Legalyard Backend with Gateway and Core applications',
        contact: {
          name: 'API Support',
          url: 'http://localhost:3000/api/docs'
        }
      },
      servers: [
        { url: 'http://localhost:3000', description: 'Gateway (Development)' },
        { url: 'http://localhost:3001', description: 'Core (Internal)' }
      ],
      paths: {
        '/api/health': {
          get: {
            summary: 'Health check',
            responses: {
              '200': {
                description: 'Service health status',
                content: {
                  'application/json': {
                    schema: {
                      type: 'object',
                      properties: {
                        status: { type: 'string' },
                        timestamp: { type: 'string' },
                        service: { type: 'string' }
                      }
                    }
                  }
                }
              }
            }
          }
        },
        '/api/auth/login': {
          post: {
            summary: 'User authentication',
            requestBody: {
              required: true,
              content: {
                'application/json': {
                  schema: {
                    type: 'object',
                    properties: {
                      email: { type: 'string', format: 'email' },
                      password: { type: 'string' },
                      totpCode: { type: 'string' }
                    },
                    required: ['email', 'password']
                  }
                }
              }
            },
            responses: {
              '200': {
                description: 'Authentication successful',
                content: {
                  'application/json': {
                    schema: {
                      type: 'object',
                      properties: {
                        accessToken: { type: 'string' },
                        refreshToken: { type: 'string' },
                        user: { $ref: '#/components/schemas/User' },
                        expiresIn: { type: 'number' }
                      }
                    }
                  }
                }
              },
              '401': { description: 'Invalid credentials' }
            }
          }
        }
      },
      components: {
        schemas: {
          User: {
            type: 'object',
            properties: {
              id: { type: 'string' },
              email: { type: 'string', format: 'email' },
              username: { type: 'string' },
              roles: { type: 'array', items: { type: 'string' } },
              isActive: { type: 'boolean' },
              createdAt: { type: 'string', format: 'date-time' },
              updatedAt: { type: 'string', format: 'date-time' }
            }
          }
        },
        securitySchemes: {
          bearerAuth: {
            type: 'http',
            scheme: 'bearer',
            bearerFormat: 'JWT'
          }
        }
      },
      security: [{ bearerAuth: [] }]
    };
  }
}