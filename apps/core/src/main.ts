import { NestFactory } from '@nestjs/core';
import { AppModule } from './app.module';
import { ValidationPipe } from '@nestjs/common';
import { DocumentBuilder, SwaggerModule } from '@nestjs/swagger';
import helmet from 'helmet';
import { HttpExceptionFilter } from './filters/http-exception.filter';

async function bootstrap() {
  const app = await NestFactory.create(AppModule);

  // Security middleware
  app.use(helmet());

  // Enable CORS with comprehensive configuration
  app.enableCors({
    origin: function (origin, callback) {
      // Allow requests with no origin (like mobile apps, Postman, curl)
      if (!origin) return callback(null, true);

      // In development, allow all localhost origins
      if (process.env.NODE_ENV !== 'production') {
        if (origin.includes('localhost') || origin.includes('127.0.0.1') || origin.includes('0.0.0.0')) {
          return callback(null, true);
        }
      }

      // Use environment variable for production
      const allowedOrigins = process.env.CORS_ORIGIN?.split(',') || ['http://localhost:3000', 'http://localhost:8000', 'http://localhost:8001'];

      if (allowedOrigins.includes(origin) || allowedOrigins.includes('*')) {
        callback(null, true);
      } else {
        console.warn(`CORS blocked origin: ${origin}`);
        callback(new Error('Not allowed by CORS'));
      }
    },
    methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS'],
    allowedHeaders: [
      'Origin',
      'X-Requested-With',
      'Content-Type',
      'Accept',
      'Authorization',
      'X-User-ID',
      'X-User-Role',
      'X-User-Org'
    ],
    exposedHeaders: ['X-Total-Count', 'X-Page-Count'],
    credentials: true,
    preflightContinue: false,
    optionsSuccessStatus: 204
  });

  // Global exception filter
  app.useGlobalFilters(new HttpExceptionFilter());

  // Global validation pipe
  app.useGlobalPipes(new ValidationPipe({
    whitelist: true,
    forbidNonWhitelisted: true,
    transform: true,
  }));

  // Global prefix for all routes
  app.setGlobalPrefix('api');

  // Swagger documentation
  if (process.env.NODE_ENV !== 'production') {
    const config = new DocumentBuilder()
      .setTitle('Legalyard Core API')
      .setDescription('Core business logic API for Legalyard application')
      .setVersion('1.0')
      .addBearerAuth()
      .addTag('auth', 'Authentication endpoints')
      .addTag('admin', 'Admin management endpoints')
      .addTag('coder', 'CMS and content management endpoints')
      .build();

    const document = SwaggerModule.createDocument(app, config);
    SwaggerModule.setup('api/docs', app, document);
  }

  const port = process.env.CORE_PORT || 8001;
  await app.listen(port, '0.0.0.0');

  console.log(`🚀 Core app is running on: http://localhost:${port}`);
  console.log(`📚 Swagger docs available at: http://localhost:${port}/api/docs`);
  console.log(`🗄️  Database: ${process.env.MONGO_URI ? 'Connected' : 'Using default connection'}`);
  console.log(`🌐 CORS origins: ${process.env.CORS_ORIGIN || 'localhost development mode'}`);
  console.log(`📋 Available modules: Auth, Admin, Coder, Docs, Shared`);
  console.log(`🤖 AI documentation: http://localhost:${port}/api/docs/ai-agent-complete`);
  console.log(`📥 Postman collection: http://localhost:${port}/api/docs/postman-collection`);

  if (process.env.NODE_ENV === 'development') {
    console.log(`\n🔧 Development mode enabled:`);
    console.log(`   - CORS allows all localhost origins`);
    console.log(`   - Detailed error messages`);
    console.log(`   - Hot reload enabled`);
    console.log(`   - All endpoints accessible`);
  }
}
bootstrap();
