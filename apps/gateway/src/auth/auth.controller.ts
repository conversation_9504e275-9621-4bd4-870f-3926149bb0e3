import { <PERSON>, Post, Body, Req, Res, UseGuards, Get } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse } from '@nestjs/swagger';
import { Request, Response } from 'express';
import { AuthService } from './auth.service';
import { ThrottlerGuard } from '@nestjs/throttler';

@ApiTags('auth')
@Controller('auth')
@UseGuards(ThrottlerGuard)
export class AuthController {
  constructor(private readonly authService: AuthService) {}

  @Post('login')
  @ApiOperation({ summary: 'User login (forwarded to core)' })
  @ApiResponse({ status: 200, description: 'Login successful' })
  @ApiResponse({ status: 401, description: 'Invalid credentials' })
  async login(@Body() loginDto: any, @Req() req: Request, @Res() res: Response) {
    return this.authService.forwardAuthRequest('login', loginDto, req, res);
  }

  @Post('register')
  @ApiOperation({ summary: 'User registration (forwarded to core)' })
  @ApiResponse({ status: 201, description: 'Registration successful' })
  @ApiResponse({ status: 400, description: 'Invalid input data' })
  async register(@Body() registerDto: any, @Req() req: Request, @Res() res: Response) {
    return this.authService.forwardAuthRequest('register', registerDto, req, res);
  }

  @Post('refresh')
  @ApiOperation({ summary: 'Refresh access token (forwarded to core)' })
  @ApiResponse({ status: 200, description: 'Token refreshed successfully' })
  @ApiResponse({ status: 401, description: 'Invalid refresh token' })
  async refresh(@Body() refreshDto: any, @Req() req: Request, @Res() res: Response) {
    return this.authService.forwardAuthRequest('refresh', refreshDto, req, res);
  }

  @Post('logout')
  @ApiOperation({ summary: 'User logout (forwarded to core)' })
  @ApiResponse({ status: 200, description: 'Logout successful' })
  async logout(@Body() logoutDto: any, @Req() req: Request, @Res() res: Response) {
    return this.authService.forwardAuthRequest('logout', logoutDto, req, res);
  }

  @Get('verify')
  @ApiOperation({ summary: 'Verify current user token (forwarded to core)' })
  @ApiResponse({ status: 200, description: 'Token verified successfully' })
  @ApiResponse({ status: 401, description: 'Invalid or expired token' })
  async verify(@Req() req: Request, @Res() res: Response) {
    return this.authService.forwardAuthRequest('verify', {}, req, res);
  }
}
