import { NestFactory } from '@nestjs/core';
import { AppModule } from './app.module';
import { DocumentBuilder, SwaggerModule } from '@nestjs/swagger';

async function bootstrap() {
  const app = await NestFactory.create(AppModule);

  // Enable CORS
  app.enableCors({
    origin: process.env.CORS_ORIGIN?.split(',') || true,
    credentials: true,
  });

  // Global prefix for all routes
  app.setGlobalPrefix('api');

  // Swagger documentation
  if (process.env.NODE_ENV !== 'production') {
    const config = new DocumentBuilder()
      .setTitle('Legalyard Gateway API')
      .setDescription('API Gateway for Legalyard application - handles authentication and proxying')
      .setVersion('1.0')
      .addBearerAuth()
      .addTag('auth', 'Authentication endpoints (forwarded to core)')
      .addTag('proxy', 'Proxy endpoints to core services')
      .build();

    const document = SwaggerModule.createDocument(app, config);
    SwaggerModule.setup('api/docs', app, document);
  }

  const port = process.env.GATEWAY_PORT || 8000;
  await app.listen(port, '0.0.0.0');
  console.log(`🚀 Gateway app is running on: http://localhost:${port}`);
  if (process.env.NODE_ENV !== 'production') {
    console.log(`📚 Swagger docs available at: http://localhost:${port}/api/docs`);
  }
}
bootstrap();
