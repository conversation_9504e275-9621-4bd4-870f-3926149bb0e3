import { Controller, Get } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse } from '@nestjs/swagger';
import { AppService } from './app.service';

@ApiTags('gateway')
@Controller()
export class AppController {
  constructor(private readonly appService: AppService) {}

  @Get()
  @ApiOperation({ summary: 'Gateway status check' })
  @ApiResponse({ status: 200, description: 'Gateway is running' })
  getHello(): string {
    return this.appService.getHello();
  }

  @Get('health')
  @ApiOperation({ summary: 'Gateway health check' })
  @ApiResponse({ status: 200, description: 'Health status information' })
  getHealth(): object {
    return {
      status: 'ok',
      timestamp: new Date().toISOString(),
      service: 'gateway',
      version: '1.0.0',
      environment: process.env.NODE_ENV || 'development',
      cors: {
        enabled: true,
        origins: process.env.CORS_ORIGIN?.split(',') || ['localhost development mode'],
        allowCredentials: true,
        methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS']
      },
      rateLimit: {
        ttl: process.env.THROTTLE_TTL || 60000,
        limit: process.env.THROTTLE_LIMIT || 1000
      },
      endpoints: {
        swagger: '/api/docs',
        postmanCollection: '/api/core/docs/postman-download',
        aiAgent: '/api/core/docs/ai-agent-complete'
      }
    };
  }
}
