import { Injectable, Logger } from '@nestjs/common';
import { HttpService } from '@nestjs/axios';
import { ConfigService } from '@nestjs/config';
import { Request, Response } from 'express';
import { firstValueFrom } from 'rxjs';

@Injectable()
export class ProxyService {
  private readonly logger = new Logger(ProxyService.name);
  private readonly coreServiceUrl: string;

  constructor(
    private readonly httpService: HttpService,
    private readonly configService: ConfigService,
  ) {
    this.coreServiceUrl = this.configService.get<string>('CORE_SERVICE_URL', 'http://localhost:8001');
  }

  async forwardRequest(req: Request, res: Response) {
    try {
      // Remove /api/core prefix and replace with /api for core service
      const coreUrl = req.url.replace(/^\/api\/core/, '/api');
      const targetUrl = `${this.coreServiceUrl}${coreUrl}`;

      this.logger.log(`Proxying ${req.method} ${req.url} to ${targetUrl}`);

      // Forward headers (excluding host)
      const headers = { ...req.headers };
      delete headers.host;
      delete headers['content-length'];

      // Add user context from auth guard
      if (req['user']) {
        headers['x-user-id'] = req['user'].id;
        headers['x-user-role'] = req['user'].role;
        headers['x-user-org'] = req['user'].organizationId;
      }

      const response = await firstValueFrom(
        this.httpService.request({
          method: req.method as any,
          url: targetUrl,
          headers,
          data: req.body,
          params: req.query,
          timeout: 30000, // 30 seconds timeout
        })
      );

      // Forward response headers
      Object.keys(response.headers).forEach(key => {
        res.setHeader(key, response.headers[key]);
      });

      res.status(response.status).send(response.data);
    } catch (error) {
      this.logger.error(`Proxy error: ${error.message}`, error.stack);
      
      if (error.response) {
        res.status(error.response.status).send(error.response.data);
      } else {
        res.status(500).send({
          error: 'Internal Server Error',
          message: 'Failed to proxy request to core service',
        });
      }
    }
  }
}
