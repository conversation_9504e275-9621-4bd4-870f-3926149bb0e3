import { Controller, All, Req, Res, UseGuards } from '@nestjs/common';
import { Request, Response } from 'express';
import { ProxyService } from './proxy.service';
import { ThrottlerGuard } from '@nestjs/throttler';

@Controller('core')
@UseGuards(ThrottlerGuard)
export class ProxyController {
  constructor(private readonly proxyService: ProxyService) {}

  @All('*')
  async proxyToCore(@Req() req: Request, @Res() res: Response) {
    return this.proxyService.forwardRequest(req, res);
  }
}
