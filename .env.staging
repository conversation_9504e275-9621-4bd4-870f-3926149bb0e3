# Staging Environment Configuration
NODE_ENV=staging

# Database Configuration
DATABASE_ENGINE=mongo
MONGO_URI=mongodb://staging-mongo:27017/legalyard_staging
POSTGRES_URL=**************************************************************/legalyard_staging

# Service Ports
GATEWAY_PORT=8000
CORE_PORT=8001

# Service URLs
CORE_SERVICE_URL=http://core-service:8001

# Authentication Secrets (Use secure secrets in actual staging)
ACCESS_TOKEN_SECRET=staging-access-token-secret-change-me
REFRESH_TOKEN_SECRET=staging-refresh-token-secret-change-me
TOTP_SECRET=staging-totp-secret-change-me

# Security Configuration
ALLOWED_IPS=10.0.0.0/8,**********/12,***********/16

# Rate Limiting
THROTTLE_TTL=60000
THROTTLE_LIMIT=200

# CORS Configuration
CORS_ORIGIN=https://staging.yourapp.com

# Logging
LOG_LEVEL=info
