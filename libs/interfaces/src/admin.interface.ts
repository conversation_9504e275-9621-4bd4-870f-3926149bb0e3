export interface Organization {
  id: string;
  name: string;
  domain?: string;
  settings: {
    allowedDomains?: string[];
    maxUsers?: number;
    features: string[];
  };
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
}

export interface CreateOrganizationDto {
  name: string;
  domain?: string;
  settings?: {
    allowedDomains?: string[];
    maxUsers?: number;
    features?: string[];
  };
}

export interface Permission {
  id: string;
  name: string;
  resource: string;
  action: string;
  description?: string;
}

export interface Role {
  id: string;
  name: string;
  description?: string;
  permissions: Permission[];
  organizationId?: string;
  isSystemRole: boolean;
}

export interface AuditLog {
  id: string;
  userId: string;
  action: string;
  resource: string;
  resourceId?: string;
  details: Record<string, any>;
  ip: string;
  userAgent: string;
  timestamp: Date;
}

export interface CreateAuditLogDto {
  userId: string;
  action: string;
  resource: string;
  resourceId?: string;
  details?: Record<string, any>;
  ip: string;
  userAgent: string;
}
