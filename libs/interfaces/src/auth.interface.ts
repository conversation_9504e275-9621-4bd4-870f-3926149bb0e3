export interface LoginDto {
  email: string;
  password: string;
  deviceId?: string;
  deviceInfo?: {
    userAgent: string;
    ip: string;
  };
  totpCode?: string;
}

export interface RegisterDto {
  email: string;
  password: string;
  username?: string;
  firstName?: string;
  lastName?: string;
  organizationId?: string;
}

export interface AuthResponse {
  accessToken: string;
  refreshToken: string;
  user: {
    id: string;
    email: string;
    username?: string;
    roles: string[];
    organizationId?: string;
  };
  expiresIn: number;
}

export interface RefreshTokenDto {
  refreshToken: string;
}

export interface TokenPayload {
  sub: string; // user id
  email: string;
  roles: string[];
  organizationId?: string;
  sessionId: string;
  iat: number;
  exp: number;
}

export interface TotpSetupResponse {
  secret: string;
  qrCode: string;
  backupCodes: string[];
}

export interface TotpVerifyDto {
  code: string;
  secret?: string;
}
