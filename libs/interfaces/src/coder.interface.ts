export interface Content {
  id: string;
  title: string;
  slug: string;
  body: string;
  excerpt?: string;
  status: 'draft' | 'published' | 'archived';
  type: 'page' | 'post' | 'article';
  authorId: string;
  tags: string[];
  metadata: Record<string, any>;
  publishedAt?: Date;
  createdAt: Date;
  updatedAt: Date;
}

export interface CreateContentDto {
  title: string;
  slug?: string;
  body: string;
  excerpt?: string;
  status?: 'draft' | 'published' | 'archived';
  type: 'page' | 'post' | 'article';
  tags?: string[];
  metadata?: Record<string, any>;
  publishedAt?: Date;
}

export interface Filter {
  id: string;
  name: string;
  type: 'search' | 'category' | 'tag' | 'date' | 'custom';
  criteria: Record<string, any>;
  isActive: boolean;
  createdAt: Date;
}

export interface Form {
  id: string;
  name: string;
  title: string;
  description?: string;
  fields: FormField[];
  settings: {
    submitText?: string;
    redirectUrl?: string;
    emailNotifications?: string[];
  };
  isActive: boolean;
  createdAt: Date;
}

export interface FormField {
  id: string;
  name: string;
  label: string;
  type: 'text' | 'email' | 'number' | 'textarea' | 'select' | 'checkbox' | 'radio' | 'file';
  required: boolean;
  placeholder?: string;
  options?: string[];
  validation?: Record<string, any>;
  order: number;
}

export interface Theme {
  id: string;
  name: string;
  description?: string;
  version: string;
  config: {
    colors: Record<string, string>;
    fonts: Record<string, string>;
    layout: Record<string, any>;
    components: Record<string, any>;
  };
  isActive: boolean;
  createdAt: Date;
}

export interface Newsletter {
  id: string;
  subject: string;
  content: string;
  recipients: string[];
  status: 'draft' | 'scheduled' | 'sent' | 'failed';
  scheduledAt?: Date;
  sentAt?: Date;
  stats: {
    sent: number;
    opened: number;
    clicked: number;
    bounced: number;
  };
  createdAt: Date;
}
