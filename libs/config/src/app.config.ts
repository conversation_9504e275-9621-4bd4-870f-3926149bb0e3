import { registerAs } from '@nestjs/config';

export default registerAs('app', () => ({
  nodeEnv: process.env.NODE_ENV || 'development',
  gatewayPort: parseInt(process.env.GATEWAY_PORT, 10) || 3000,
  corePort: parseInt(process.env.CORE_PORT, 10) || 3001,
  coreServiceUrl: process.env.CORE_SERVICE_URL || 'http://localhost:3001',
  logLevel: process.env.LOG_LEVEL || 'info',
  cors: {
    origin: process.env.CORS_ORIGIN?.split(',') || ['http://localhost:3000'],
    credentials: true,
  },
  throttle: {
    ttl: parseInt(process.env.THROTTLE_TTL, 10) || 60000,
    limit: parseInt(process.env.THROTTLE_LIMIT, 10) || 100,
  },
}));
