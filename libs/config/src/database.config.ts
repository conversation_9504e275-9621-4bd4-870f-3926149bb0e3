import { registerAs } from '@nestjs/config';

export default registerAs('database', () => ({
  engine: process.env.DATABASE_ENGINE || 'mongo',
  mongo: {
    uri: process.env.MONGO_URI || 'mongodb://localhost:27017/legalyard',
  },
  postgres: {
    url: process.env.POSTGRES_URL || 'postgres://user:password@localhost:5432/legalyard',
    host: process.env.POSTGRES_HOST || 'localhost',
    port: parseInt(process.env.POSTGRES_PORT, 10) || 5432,
    username: process.env.POSTGRES_USERNAME || 'user',
    password: process.env.POSTGRES_PASSWORD || 'password',
    database: process.env.POSTGRES_DATABASE || 'legalyard',
  },
}));
