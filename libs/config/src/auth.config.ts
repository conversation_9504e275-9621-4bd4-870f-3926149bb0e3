import { registerAs } from '@nestjs/config';

export default registerAs('auth', () => ({
  accessTokenSecret: process.env.ACCESS_TOKEN_SECRET || 'your-access-token-secret',
  refreshTokenSecret: process.env.REFRESH_TOKEN_SECRET || 'your-refresh-token-secret',
  totpSecret: process.env.TOTP_SECRET || 'your-totp-secret',
  accessTokenExpiry: process.env.ACCESS_TOKEN_EXPIRY || '15m',
  refreshTokenExpiry: process.env.REFRESH_TOKEN_EXPIRY || '7d',
  allowedIPs: process.env.ALLOWED_IPS?.split(',').map(ip => ip.trim()) || [],
  maxDevices: parseInt(process.env.MAX_DEVICES, 10) || 2,
  sessionTimeout: parseInt(process.env.SESSION_TIMEOUT, 10) || 3600000, // 1 hour
}));
