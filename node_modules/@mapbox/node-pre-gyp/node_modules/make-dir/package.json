{"name": "make-dir", "version": "3.1.0", "description": "Make a directory and its parents if needed - Think `mkdir -p`", "license": "MIT", "repository": "sindresorhus/make-dir", "funding": "https://github.com/sponsors/sindresorhus", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "engines": {"node": ">=8"}, "scripts": {"test": "xo && nyc ava && tsd"}, "files": ["index.js", "index.d.ts"], "keywords": ["mkdir", "mkdirp", "make", "directories", "dir", "dirs", "folders", "directory", "folder", "path", "parent", "parents", "intermediate", "recursively", "recursive", "create", "fs", "filesystem", "file-system"], "dependencies": {"semver": "^6.0.0"}, "devDependencies": {"@types/graceful-fs": "^4.1.3", "@types/node": "^13.7.1", "ava": "^1.4.0", "codecov": "^3.2.0", "graceful-fs": "^4.1.15", "nyc": "^15.0.0", "path-type": "^4.0.0", "tempy": "^0.2.1", "tsd": "^0.11.0", "xo": "^0.25.4"}}