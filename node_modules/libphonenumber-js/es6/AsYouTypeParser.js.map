{"version": 3, "file": "AsYouTypeParser.js", "names": ["extractCountryCallingCode", "extractCountryCallingCodeFromInternationalNumberWithoutPlusSign", "extractNationalNumberFromPossiblyIncompleteNumber", "stripIddPrefix", "parseDigits", "VALID_DIGITS", "VALID_PUNCTUATION", "PLUS_CHARS", "VALID_FORMATTED_PHONE_NUMBER_DIGITS_PART", "VALID_FORMATTED_PHONE_NUMBER_DIGITS_PART_PATTERN", "RegExp", "VALID_FORMATTED_PHONE_NUMBER_PART", "AFTER_PHONE_NUMBER_DIGITS_END_PATTERN", "COMPLEX_NATIONAL_PREFIX", "AsYouType<PERSON><PERSON><PERSON>", "defaultCountry", "defaultCallingCode", "metadata", "onNationalSignificantNumberChange", "text", "state", "extractFormattedDigitsAndPlus", "formattedDigits", "hasPlus", "digits", "justLeadingPlus", "startInternationalNumber", "inputDigits", "nextDigits", "hasReceivedThreeLeadingDigits", "length", "appendDigits", "extractIddPrefix", "isWaitingForCountryCallingCode", "appendNationalSignificantNumberDigits", "international", "hasExtractedNationalSignificantNumber", "extractNationalSignificantNumber", "getNationalDigits", "stateUpdate", "update", "callingCode", "getDigitsWithoutInternationalPrefix", "countryCallingCode", "number", "setCallingCode", "nationalSignificantNumber", "numberingPlan", "hasSelectedNumberingPlan", "nationalPrefixForParsing", "_nationalPrefixForParsing", "couldPossiblyExtractAnotherNationalSignificantNumber", "test", "undefined", "nationalDigits", "setState", "nationalPrefix", "nationalNumber", "carrierCode", "onExtractedNationalNumber", "prevNationalSignificantNumber", "complexPrefixBeforeNationalSignificantNumber", "nationalSignificantNumberMatchesInput", "nationalSignificantNumberIndex", "lastIndexOf", "prefixBeforeNationalNumber", "slice", "extractAnotherNationalSignificantNumber", "extractCallingCodeAndNationalSignificantNumber", "fixMissingPlus", "IDDPrefix", "numberWithoutIDD", "country", "newCallingCode", "missingPlus", "resetNationalSignificantNumber", "extractFormattedPhoneNumber", "startsAt", "search", "replace", "_extractFormattedDigitsAndPlus", "extractedNumber"], "sources": ["../source/AsYouTypeParser.js"], "sourcesContent": ["import extractCountryCallingCode from './helpers/extractCountryCallingCode.js'\r\nimport extractCountryCallingCodeFromInternationalNumberWithoutPlusSign from './helpers/extractCountryCallingCodeFromInternationalNumberWithoutPlusSign.js'\r\nimport extractNationalNumberFromPossiblyIncompleteNumber from './helpers/extractNationalNumberFromPossiblyIncompleteNumber.js'\r\nimport stripIddPrefix from './helpers/stripIddPrefix.js'\r\nimport parseDigits from './helpers/parseDigits.js'\r\n\r\nimport {\r\n\tVALID_DIGITS,\r\n\tVALID_PUNCTUATION,\r\n\tPLUS_CHARS\r\n} from './constants.js'\r\n\r\nconst VALID_FORMATTED_PHONE_NUMBER_DIGITS_PART =\r\n\t'[' +\r\n\t\tVALID_PUNCTUATION +\r\n\t\tVALID_DIGITS +\r\n\t']+'\r\n\r\nconst VALID_FORMATTED_PHONE_NUMBER_DIGITS_PART_PATTERN = new RegExp('^' + VALID_FORMATTED_PHONE_NUMBER_DIGITS_PART + '$', 'i')\r\n\r\nconst VALID_FORMATTED_PHONE_NUMBER_PART =\r\n\t'(?:' +\r\n\t\t'[' + PLUS_CHARS + ']' +\r\n\t\t'[' +\r\n\t\t\tVALID_PUNCTUATION +\r\n\t\t\tVALID_DIGITS +\r\n\t\t']*' +\r\n\t\t'|' +\r\n\t\t'[' +\r\n\t\t\tVALID_PUNCTUATION +\r\n\t\t\tVALID_DIGITS +\r\n\t\t']+' +\r\n\t')'\r\n\r\nconst AFTER_PHONE_NUMBER_DIGITS_END_PATTERN = new RegExp(\r\n\t'[^' +\r\n\t\tVALID_PUNCTUATION +\r\n\t\tVALID_DIGITS +\r\n\t']+' +\r\n\t'.*' +\r\n\t'$'\r\n)\r\n\r\n// Tests whether `national_prefix_for_parsing` could match\r\n// different national prefixes.\r\n// Matches anything that's not a digit or a square bracket.\r\nconst COMPLEX_NATIONAL_PREFIX = /[^\\d\\[\\]]/\r\n\r\nexport default class AsYouTypeParser {\r\n\tconstructor({\r\n\t\tdefaultCountry,\r\n\t\tdefaultCallingCode,\r\n\t\tmetadata,\r\n\t\tonNationalSignificantNumberChange\r\n\t}) {\r\n\t\tthis.defaultCountry = defaultCountry\r\n\t\tthis.defaultCallingCode = defaultCallingCode\r\n\t\tthis.metadata = metadata\r\n\t\tthis.onNationalSignificantNumberChange = onNationalSignificantNumberChange\r\n\t}\r\n\r\n\tinput(text, state) {\r\n\t\tconst [formattedDigits, hasPlus] = extractFormattedDigitsAndPlus(text)\r\n\t\tconst digits = parseDigits(formattedDigits)\r\n\t\t// Checks for a special case: just a leading `+` has been entered.\r\n\t\tlet justLeadingPlus\r\n\t\tif (hasPlus) {\r\n\t\t\tif (!state.digits) {\r\n\t\t\t\tstate.startInternationalNumber()\r\n\t\t\t\tif (!digits) {\r\n\t\t\t\t\tjustLeadingPlus = true\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t\tif (digits) {\r\n\t\t\tthis.inputDigits(digits, state)\r\n\t\t}\r\n\t\treturn {\r\n\t\t\tdigits,\r\n\t\t\tjustLeadingPlus\r\n\t\t}\r\n\t}\r\n\r\n\t/**\r\n\t * Inputs \"next\" phone number digits.\r\n\t * @param  {string} digits\r\n\t * @return {string} [formattedNumber] Formatted national phone number (if it can be formatted at this stage). Returning `undefined` means \"don't format the national phone number at this stage\".\r\n\t */\r\n\tinputDigits(nextDigits, state) {\r\n\t\tconst { digits } = state\r\n\t\tconst hasReceivedThreeLeadingDigits = digits.length < 3 && digits.length + nextDigits.length >= 3\r\n\r\n\t\t// Append phone number digits.\r\n\t\tstate.appendDigits(nextDigits)\r\n\r\n\t\t// Attempt to extract IDD prefix:\r\n\t\t// Some users input their phone number in international format,\r\n\t\t// but in an \"out-of-country\" dialing format instead of using the leading `+`.\r\n\t\t// https://github.com/catamphetamine/libphonenumber-js/issues/185\r\n\t\t// Detect such numbers as soon as there're at least 3 digits.\r\n\t\t// Google's library attempts to extract IDD prefix at 3 digits,\r\n\t\t// so this library just copies that behavior.\r\n\t\t// I guess that's because the most commot IDD prefixes are\r\n\t\t// `00` (Europe) and `011` (US).\r\n\t\t// There exist really long IDD prefixes too:\r\n\t\t// for example, in Australia the default IDD prefix is `0011`,\r\n\t\t// and it could even be as long as `14880011`.\r\n\t\t// An IDD prefix is extracted here, and then every time when\r\n\t\t// there's a new digit and the number couldn't be formatted.\r\n\t\tif (hasReceivedThreeLeadingDigits) {\r\n\t\t\tthis.extractIddPrefix(state)\r\n\t\t}\r\n\r\n\t\tif (this.isWaitingForCountryCallingCode(state)) {\r\n\t\t\tif (!this.extractCountryCallingCode(state)) {\r\n\t\t\t\treturn\r\n\t\t\t}\r\n\t\t} else {\r\n\t\t\tstate.appendNationalSignificantNumberDigits(nextDigits)\r\n\t\t}\r\n\r\n\t\t// If a phone number is being input in international format,\r\n\t\t// then it's not valid for it to have a national prefix.\r\n\t\t// Still, some people incorrectly input such numbers with a national prefix.\r\n\t\t// In such cases, only attempt to strip a national prefix if the number becomes too long.\r\n\t\t// (but that is done later, not here)\r\n\t\tif (!state.international) {\r\n\t\t\tif (!this.hasExtractedNationalSignificantNumber) {\r\n\t\t\t\tthis.extractNationalSignificantNumber(\r\n\t\t\t\t\tstate.getNationalDigits(),\r\n\t\t\t\t\t(stateUpdate) => state.update(stateUpdate)\r\n\t\t\t\t)\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\r\n\tisWaitingForCountryCallingCode({ international, callingCode }) {\r\n\t\treturn international && !callingCode\r\n\t}\r\n\r\n\t// Extracts a country calling code from a number\r\n\t// being entered in internatonal format.\r\n\textractCountryCallingCode(state) {\r\n\t\tconst { countryCallingCode, number } = extractCountryCallingCode(\r\n\t\t\t'+' + state.getDigitsWithoutInternationalPrefix(),\r\n\t\t\tthis.defaultCountry,\r\n\t\t\tthis.defaultCallingCode,\r\n\t\t\tthis.metadata.metadata\r\n\t\t)\r\n\t\tif (countryCallingCode) {\r\n\t\t\tstate.setCallingCode(countryCallingCode)\r\n\t\t\tstate.update({\r\n\t\t\t\tnationalSignificantNumber: number\r\n\t\t\t})\r\n\t\t\treturn true\r\n\t\t}\r\n\t}\r\n\r\n\treset(numberingPlan) {\r\n\t\tif (numberingPlan) {\r\n\t\t\tthis.hasSelectedNumberingPlan = true\r\n\t\t\tconst nationalPrefixForParsing = numberingPlan._nationalPrefixForParsing()\r\n\t\t\tthis.couldPossiblyExtractAnotherNationalSignificantNumber = nationalPrefixForParsing && COMPLEX_NATIONAL_PREFIX.test(nationalPrefixForParsing)\r\n\t\t} else {\r\n\t\t\tthis.hasSelectedNumberingPlan = undefined\r\n\t\t\tthis.couldPossiblyExtractAnotherNationalSignificantNumber = undefined\r\n\t\t}\r\n\t}\r\n\r\n\t/**\r\n\t * Extracts a national (significant) number from user input.\r\n\t * Google's library is different in that it only applies `national_prefix_for_parsing`\r\n\t * and doesn't apply `national_prefix_transform_rule` after that.\r\n\t * https://github.com/google/libphonenumber/blob/a3d70b0487875475e6ad659af404943211d26456/java/libphonenumber/src/com/google/i18n/phonenumbers/AsYouTypeFormatter.java#L539\r\n\t * @return {boolean} [extracted]\r\n\t */\r\n\textractNationalSignificantNumber(nationalDigits, setState) {\r\n\t\tif (!this.hasSelectedNumberingPlan) {\r\n\t\t\treturn\r\n\t\t}\r\n\t\tconst {\r\n\t\t\tnationalPrefix,\r\n\t\t\tnationalNumber,\r\n\t\t\tcarrierCode\r\n\t\t} = extractNationalNumberFromPossiblyIncompleteNumber(\r\n\t\t\tnationalDigits,\r\n\t\t\tthis.metadata\r\n\t\t)\r\n\t\tif (nationalNumber === nationalDigits) {\r\n\t\t\treturn\r\n\t\t}\r\n\t\tthis.onExtractedNationalNumber(\r\n\t\t\tnationalPrefix,\r\n\t\t\tcarrierCode,\r\n\t\t\tnationalNumber,\r\n\t\t\tnationalDigits,\r\n\t\t\tsetState\r\n\t\t)\r\n\t\treturn true\r\n\t}\r\n\r\n\t/**\r\n\t * In Google's code this function is called \"attempt to extract longer NDD\".\r\n\t * \"Some national prefixes are a substring of others\", they say.\r\n\t * @return {boolean} [result] — Returns `true` if extracting a national prefix produced different results from what they were.\r\n\t */\r\n\textractAnotherNationalSignificantNumber(nationalDigits, prevNationalSignificantNumber, setState) {\r\n\t\tif (!this.hasExtractedNationalSignificantNumber) {\r\n\t\t\treturn this.extractNationalSignificantNumber(nationalDigits, setState)\r\n\t\t}\r\n\t\tif (!this.couldPossiblyExtractAnotherNationalSignificantNumber) {\r\n\t\t\treturn\r\n\t\t}\r\n\t\tconst {\r\n\t\t\tnationalPrefix,\r\n\t\t\tnationalNumber,\r\n\t\t\tcarrierCode\r\n\t\t} = extractNationalNumberFromPossiblyIncompleteNumber(\r\n\t\t\tnationalDigits,\r\n\t\t\tthis.metadata\r\n\t\t)\r\n\t\t// If a national prefix has been extracted previously,\r\n\t\t// then it's always extracted as additional digits are added.\r\n\t\t// That's assuming `extractNationalNumberFromPossiblyIncompleteNumber()`\r\n\t\t// doesn't do anything different from what it currently does.\r\n\t\t// So, just in case, here's this check, though it doesn't occur.\r\n\t\t/* istanbul ignore if */\r\n\t\tif (nationalNumber === prevNationalSignificantNumber) {\r\n\t\t\treturn\r\n\t\t}\r\n\t\tthis.onExtractedNationalNumber(\r\n\t\t\tnationalPrefix,\r\n\t\t\tcarrierCode,\r\n\t\t\tnationalNumber,\r\n\t\t\tnationalDigits,\r\n\t\t\tsetState\r\n\t\t)\r\n\t\treturn true\r\n\t}\r\n\r\n\tonExtractedNationalNumber(\r\n\t\tnationalPrefix,\r\n\t\tcarrierCode,\r\n\t\tnationalSignificantNumber,\r\n\t\tnationalDigits,\r\n\t\tsetState\r\n\t) {\r\n\t\tlet complexPrefixBeforeNationalSignificantNumber\r\n\t\tlet nationalSignificantNumberMatchesInput\r\n\t\t// This check also works with empty `this.nationalSignificantNumber`.\r\n\t\tconst nationalSignificantNumberIndex = nationalDigits.lastIndexOf(nationalSignificantNumber)\r\n\t\t// If the extracted national (significant) number is the\r\n\t\t// last substring of the `digits`, then it means that it hasn't been altered:\r\n\t\t// no digits have been removed from the national (significant) number\r\n\t\t// while applying `national_prefix_transform_rule`.\r\n\t\t// https://gitlab.com/catamphetamine/libphonenumber-js/-/blob/master/METADATA.md#national_prefix_for_parsing--national_prefix_transform_rule\r\n\t\tif (nationalSignificantNumberIndex >= 0 &&\r\n\t\t\tnationalSignificantNumberIndex === nationalDigits.length - nationalSignificantNumber.length) {\r\n\t\t\tnationalSignificantNumberMatchesInput = true\r\n\t\t\t// If a prefix of a national (significant) number is not as simple\r\n\t\t\t// as just a basic national prefix, then such prefix is stored in\r\n\t\t\t// `this.complexPrefixBeforeNationalSignificantNumber` property and will be\r\n\t\t\t// prepended \"as is\" to the national (significant) number to produce\r\n\t\t\t// a formatted result.\r\n\t\t\tconst prefixBeforeNationalNumber = nationalDigits.slice(0, nationalSignificantNumberIndex)\r\n\t\t\t// `prefixBeforeNationalNumber` is always non-empty,\r\n\t\t\t// because `onExtractedNationalNumber()` isn't called\r\n\t\t\t// when a national (significant) number hasn't been actually \"extracted\":\r\n\t\t\t// when a national (significant) number is equal to the national part of `digits`,\r\n\t\t\t// then `onExtractedNationalNumber()` doesn't get called.\r\n\t\t\tif (prefixBeforeNationalNumber !== nationalPrefix) {\r\n\t\t\t\tcomplexPrefixBeforeNationalSignificantNumber = prefixBeforeNationalNumber\r\n\t\t\t}\r\n\t\t}\r\n\t\tsetState({\r\n\t\t\tnationalPrefix,\r\n\t\t\tcarrierCode,\r\n\t\t\tnationalSignificantNumber,\r\n\t\t\tnationalSignificantNumberMatchesInput,\r\n\t\t\tcomplexPrefixBeforeNationalSignificantNumber\r\n\t\t})\r\n\t\t// `onExtractedNationalNumber()` is only called when\r\n\t\t// the national (significant) number actually did change.\r\n\t\tthis.hasExtractedNationalSignificantNumber = true\r\n\t\tthis.onNationalSignificantNumberChange()\r\n\t}\r\n\r\n\treExtractNationalSignificantNumber(state) {\r\n\t\t// Attempt to extract a national prefix.\r\n\t\t//\r\n\t\t// Some people incorrectly input national prefix\r\n\t\t// in an international phone number.\r\n\t\t// For example, some people write British phone numbers as `+44(0)...`.\r\n\t\t//\r\n\t\t// Also, in some rare cases, it is valid for a national prefix\r\n\t\t// to be a part of an international phone number.\r\n\t\t// For example, mobile phone numbers in Mexico are supposed to be\r\n\t\t// dialled internationally using a `1` national prefix,\r\n\t\t// so the national prefix will be part of an international number.\r\n\t\t//\r\n\t\t// Quote from:\r\n\t\t// https://www.mexperience.com/dialing-cell-phones-in-mexico/\r\n\t\t//\r\n\t\t// \"Dialing a Mexican cell phone from abroad\r\n\t\t// When you are calling a cell phone number in Mexico from outside Mexico,\r\n\t\t// it’s necessary to dial an additional “1” after Mexico’s country code\r\n\t\t// (which is “52”) and before the area code.\r\n\t\t// You also ignore the 045, and simply dial the area code and the\r\n\t\t// cell phone’s number.\r\n\t\t//\r\n\t\t// If you don’t add the “1”, you’ll receive a recorded announcement\r\n\t\t// asking you to redial using it.\r\n\t\t//\r\n\t\t// For example, if you are calling from the USA to a cell phone\r\n\t\t// in Mexico City, you would dial +52 – 1 – 55 – 1234 5678.\r\n\t\t// (Note that this is different to calling a land line in Mexico City\r\n\t\t// from abroad, where the number dialed would be +52 – 55 – 1234 5678)\".\r\n\t\t//\r\n\t\t// Google's demo output:\r\n\t\t// https://libphonenumber.appspot.com/phonenumberparser?number=%2b5215512345678&country=MX\r\n\t\t//\r\n\t\tif (this.extractAnotherNationalSignificantNumber(\r\n\t\t\tstate.getNationalDigits(),\r\n\t\t\tstate.nationalSignificantNumber,\r\n\t\t\t(stateUpdate) => state.update(stateUpdate)\r\n\t\t)) {\r\n\t\t\treturn true\r\n\t\t}\r\n\t\t// If no format matches the phone number, then it could be\r\n\t\t// \"a really long IDD\" (quote from a comment in Google's library).\r\n\t\t// An IDD prefix is first extracted when the user has entered at least 3 digits,\r\n\t\t// and then here — every time when there's a new digit and the number\r\n\t\t// couldn't be formatted.\r\n\t\t// For example, in Australia the default IDD prefix is `0011`,\r\n\t\t// and it could even be as long as `14880011`.\r\n\t\t//\r\n\t\t// Could also check `!hasReceivedThreeLeadingDigits` here\r\n\t\t// to filter out the case when this check duplicates the one\r\n\t\t// already performed when there're 3 leading digits,\r\n\t\t// but it's not a big deal, and in most cases there\r\n\t\t// will be a suitable `format` when there're 3 leading digits.\r\n\t\t//\r\n\t\tif (this.extractIddPrefix(state)) {\r\n\t\t\tthis.extractCallingCodeAndNationalSignificantNumber(state)\r\n\t\t\treturn true\r\n\t\t}\r\n\t\t// Google's AsYouType formatter supports sort of an \"autocorrection\" feature\r\n\t\t// when it \"autocorrects\" numbers that have been input for a country\r\n\t\t// with that country's calling code.\r\n\t\t// Such \"autocorrection\" feature looks weird, but different people have been requesting it:\r\n\t\t// https://github.com/catamphetamine/libphonenumber-js/issues/376\r\n\t\t// https://github.com/catamphetamine/libphonenumber-js/issues/375\r\n\t\t// https://github.com/catamphetamine/libphonenumber-js/issues/316\r\n\t\tif (this.fixMissingPlus(state)) {\r\n\t\t\tthis.extractCallingCodeAndNationalSignificantNumber(state)\r\n\t\t\treturn true\r\n\t\t}\r\n\t}\r\n\r\n\textractIddPrefix(state) {\r\n\t\t// An IDD prefix can't be present in a number written with a `+`.\r\n\t\t// Also, don't re-extract an IDD prefix if has already been extracted.\r\n\t\tconst {\r\n\t\t\tinternational,\r\n\t\t\tIDDPrefix,\r\n\t\t\tdigits,\r\n\t\t\tnationalSignificantNumber\r\n\t\t} = state\r\n\t\tif (international || IDDPrefix) {\r\n\t\t\treturn\r\n\t\t}\r\n\t\t// Some users input their phone number in \"out-of-country\"\r\n\t\t// dialing format instead of using the leading `+`.\r\n\t\t// https://github.com/catamphetamine/libphonenumber-js/issues/185\r\n\t\t// Detect such numbers.\r\n\t\tconst numberWithoutIDD = stripIddPrefix(\r\n\t\t\tdigits,\r\n\t\t\tthis.defaultCountry,\r\n\t\t\tthis.defaultCallingCode,\r\n\t\t\tthis.metadata.metadata\r\n\t\t)\r\n\t\tif (numberWithoutIDD !== undefined && numberWithoutIDD !== digits) {\r\n\t\t\t// If an IDD prefix was stripped then convert the IDD-prefixed number\r\n\t\t\t// to international number for subsequent parsing.\r\n\t\t\tstate.update({\r\n\t\t\t\tIDDPrefix: digits.slice(0, digits.length - numberWithoutIDD.length)\r\n\t\t\t})\r\n\t\t\tthis.startInternationalNumber(state, {\r\n\t\t\t\tcountry: undefined,\r\n\t\t\t\tcallingCode: undefined\r\n\t\t\t})\r\n\t\t\treturn true\r\n\t\t}\r\n\t}\r\n\r\n\tfixMissingPlus(state) {\r\n\t\tif (!state.international) {\r\n\t\t\tconst {\r\n\t\t\t\tcountryCallingCode: newCallingCode,\r\n\t\t\t\tnumber\r\n\t\t\t} = extractCountryCallingCodeFromInternationalNumberWithoutPlusSign(\r\n\t\t\t\tstate.digits,\r\n\t\t\t\tthis.defaultCountry,\r\n\t\t\t\tthis.defaultCallingCode,\r\n\t\t\t\tthis.metadata.metadata\r\n\t\t\t)\r\n\t\t\tif (newCallingCode) {\r\n\t\t\t\tstate.update({\r\n\t\t\t\t\tmissingPlus: true\r\n\t\t\t\t})\r\n\t\t\t\tthis.startInternationalNumber(state, {\r\n\t\t\t\t\tcountry: state.country,\r\n\t\t\t\t\tcallingCode: newCallingCode\r\n\t\t\t\t})\r\n\t\t\t\treturn true\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\r\n\tstartInternationalNumber(state, { country, callingCode }) {\r\n\t\tstate.startInternationalNumber(country, callingCode)\r\n\t\t// If a national (significant) number has been extracted before, reset it.\r\n\t\tif (state.nationalSignificantNumber) {\r\n\t\t\tstate.resetNationalSignificantNumber()\r\n\t\t\tthis.onNationalSignificantNumberChange()\r\n\t\t\tthis.hasExtractedNationalSignificantNumber = undefined\r\n\t\t}\r\n\t}\r\n\r\n\textractCallingCodeAndNationalSignificantNumber(state) {\r\n\t\tif (this.extractCountryCallingCode(state)) {\r\n\t\t\t// `this.extractCallingCode()` is currently called when the number\r\n\t\t\t// couldn't be formatted during the standard procedure.\r\n\t\t\t// Normally, the national prefix would be re-extracted\r\n\t\t\t// for an international number if such number couldn't be formatted,\r\n\t\t\t// but since it's already not able to be formatted,\r\n\t\t\t// there won't be yet another retry, so also extract national prefix here.\r\n\t\t\tthis.extractNationalSignificantNumber(\r\n\t\t\t\tstate.getNationalDigits(),\r\n\t\t\t\t(stateUpdate) => state.update(stateUpdate)\r\n\t\t\t)\r\n\t\t}\r\n\t}\r\n}\r\n\r\n/**\r\n * Extracts formatted phone number from text (if there's any).\r\n * @param  {string} text\r\n * @return {string} [formattedPhoneNumber]\r\n */\r\nfunction extractFormattedPhoneNumber(text) {\r\n\t// Attempt to extract a possible number from the string passed in.\r\n\tconst startsAt = text.search(VALID_FORMATTED_PHONE_NUMBER_PART)\r\n\tif (startsAt < 0) {\r\n\t\treturn\r\n\t}\r\n\t// Trim everything to the left of the phone number.\r\n\ttext = text.slice(startsAt)\r\n\t// Trim the `+`.\r\n\tlet hasPlus\r\n\tif (text[0] === '+') {\r\n\t\thasPlus = true\r\n\t\ttext = text.slice('+'.length)\r\n\t}\r\n\t// Trim everything to the right of the phone number.\r\n\ttext = text.replace(AFTER_PHONE_NUMBER_DIGITS_END_PATTERN, '')\r\n\t// Re-add the previously trimmed `+`.\r\n\tif (hasPlus) {\r\n\t\ttext = '+' + text\r\n\t}\r\n\treturn text\r\n}\r\n\r\n/**\r\n * Extracts formatted phone number digits (and a `+`) from text (if there're any).\r\n * @param  {string} text\r\n * @return {any[]}\r\n */\r\nfunction _extractFormattedDigitsAndPlus(text) {\r\n\t// Extract a formatted phone number part from text.\r\n\tconst extractedNumber = extractFormattedPhoneNumber(text) || ''\r\n\t// Trim a `+`.\r\n\tif (extractedNumber[0] === '+') {\r\n\t\treturn [extractedNumber.slice('+'.length), true]\r\n\t}\r\n\treturn [extractedNumber]\r\n}\r\n\r\n/**\r\n * Extracts formatted phone number digits (and a `+`) from text (if there're any).\r\n * @param  {string} text\r\n * @return {any[]}\r\n */\r\nexport function extractFormattedDigitsAndPlus(text) {\r\n\tlet [formattedDigits, hasPlus] = _extractFormattedDigitsAndPlus(text)\r\n\t// If the extracted phone number part\r\n\t// can possibly be a part of some valid phone number\r\n\t// then parse phone number characters from a formatted phone number.\r\n\tif (!VALID_FORMATTED_PHONE_NUMBER_DIGITS_PART_PATTERN.test(formattedDigits)) {\r\n\t\tformattedDigits = ''\r\n\t}\r\n\treturn [formattedDigits, hasPlus]\r\n}"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA,OAAOA,0BAAP,MAAsC,wCAAtC;AACA,OAAOC,+DAAP,MAA4E,8EAA5E;AACA,OAAOC,iDAAP,MAA8D,gEAA9D;AACA,OAAOC,cAAP,MAA2B,6BAA3B;AACA,OAAOC,WAAP,MAAwB,0BAAxB;AAEA,SACCC,YADD,EAECC,iBAFD,EAGCC,UAHD,QAIO,gBAJP;AAMA,IAAMC,wCAAwC,GAC7C,MACCF,iBADD,GAECD,YAFD,GAGA,IAJD;AAMA,IAAMI,gDAAgD,GAAG,IAAIC,MAAJ,CAAW,MAAMF,wCAAN,GAAiD,GAA5D,EAAiE,GAAjE,CAAzD;AAEA,IAAMG,iCAAiC,GACtC,QACC,GADD,GACOJ,UADP,GACoB,GADpB,GAEC,GAFD,GAGED,iBAHF,GAIED,YAJF,GAKC,IALD,GAMC,GAND,GAOC,GAPD,GAQEC,iBARF,GASED,YATF,GAUC,IAVD,GAWA,GAZD;AAcA,IAAMO,qCAAqC,GAAG,IAAIF,MAAJ,CAC7C,OACCJ,iBADD,GAECD,YAFD,GAGA,IAHA,GAIA,IAJA,GAKA,GAN6C,CAA9C,C,CASA;AACA;AACA;;AACA,IAAMQ,uBAAuB,GAAG,WAAhC;;IAEqBC,e;EACpB,+BAKG;IAAA,IAJFC,cAIE,QAJFA,cAIE;IAAA,IAHFC,kBAGE,QAHFA,kBAGE;IAAA,IAFFC,QAEE,QAFFA,QAEE;IAAA,IADFC,iCACE,QADFA,iCACE;;IAAA;;IACF,KAAKH,cAAL,GAAsBA,cAAtB;IACA,KAAKC,kBAAL,GAA0BA,kBAA1B;IACA,KAAKC,QAAL,GAAgBA,QAAhB;IACA,KAAKC,iCAAL,GAAyCA,iCAAzC;EACA;;;;WAED,eAAMC,IAAN,EAAYC,KAAZ,EAAmB;MAClB,4BAAmCC,6BAA6B,CAACF,IAAD,CAAhE;MAAA;MAAA,IAAOG,eAAP;MAAA,IAAwBC,OAAxB;;MACA,IAAMC,MAAM,GAAGpB,WAAW,CAACkB,eAAD,CAA1B,CAFkB,CAGlB;;MACA,IAAIG,eAAJ;;MACA,IAAIF,OAAJ,EAAa;QACZ,IAAI,CAACH,KAAK,CAACI,MAAX,EAAmB;UAClBJ,KAAK,CAACM,wBAAN;;UACA,IAAI,CAACF,MAAL,EAAa;YACZC,eAAe,GAAG,IAAlB;UACA;QACD;MACD;;MACD,IAAID,MAAJ,EAAY;QACX,KAAKG,WAAL,CAAiBH,MAAjB,EAAyBJ,KAAzB;MACA;;MACD,OAAO;QACNI,MAAM,EAANA,MADM;QAENC,eAAe,EAAfA;MAFM,CAAP;IAIA;IAED;AACD;AACA;AACA;AACA;;;;WACC,qBAAYG,UAAZ,EAAwBR,KAAxB,EAA+B;MAC9B,IAAQI,MAAR,GAAmBJ,KAAnB,CAAQI,MAAR;MACA,IAAMK,6BAA6B,GAAGL,MAAM,CAACM,MAAP,GAAgB,CAAhB,IAAqBN,MAAM,CAACM,MAAP,GAAgBF,UAAU,CAACE,MAA3B,IAAqC,CAAhG,CAF8B,CAI9B;;MACAV,KAAK,CAACW,YAAN,CAAmBH,UAAnB,EAL8B,CAO9B;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;;MACA,IAAIC,6BAAJ,EAAmC;QAClC,KAAKG,gBAAL,CAAsBZ,KAAtB;MACA;;MAED,IAAI,KAAKa,8BAAL,CAAoCb,KAApC,CAAJ,EAAgD;QAC/C,IAAI,CAAC,KAAKpB,yBAAL,CAA+BoB,KAA/B,CAAL,EAA4C;UAC3C;QACA;MACD,CAJD,MAIO;QACNA,KAAK,CAACc,qCAAN,CAA4CN,UAA5C;MACA,CA/B6B,CAiC9B;MACA;MACA;MACA;MACA;;;MACA,IAAI,CAACR,KAAK,CAACe,aAAX,EAA0B;QACzB,IAAI,CAAC,KAAKC,qCAAV,EAAiD;UAChD,KAAKC,gCAAL,CACCjB,KAAK,CAACkB,iBAAN,EADD,EAEC,UAACC,WAAD;YAAA,OAAiBnB,KAAK,CAACoB,MAAN,CAAaD,WAAb,CAAjB;UAAA,CAFD;QAIA;MACD;IACD;;;WAED,+CAA+D;MAAA,IAA9BJ,aAA8B,SAA9BA,aAA8B;MAAA,IAAfM,WAAe,SAAfA,WAAe;MAC9D,OAAON,aAAa,IAAI,CAACM,WAAzB;IACA,C,CAED;IACA;;;;WACA,mCAA0BrB,KAA1B,EAAiC;MAChC,4BAAuCpB,0BAAyB,CAC/D,MAAMoB,KAAK,CAACsB,mCAAN,EADyD,EAE/D,KAAK3B,cAF0D,EAG/D,KAAKC,kBAH0D,EAI/D,KAAKC,QAAL,CAAcA,QAJiD,CAAhE;MAAA,IAAQ0B,kBAAR,yBAAQA,kBAAR;MAAA,IAA4BC,MAA5B,yBAA4BA,MAA5B;;MAMA,IAAID,kBAAJ,EAAwB;QACvBvB,KAAK,CAACyB,cAAN,CAAqBF,kBAArB;QACAvB,KAAK,CAACoB,MAAN,CAAa;UACZM,yBAAyB,EAAEF;QADf,CAAb;QAGA,OAAO,IAAP;MACA;IACD;;;WAED,eAAMG,aAAN,EAAqB;MACpB,IAAIA,aAAJ,EAAmB;QAClB,KAAKC,wBAAL,GAAgC,IAAhC;;QACA,IAAMC,wBAAwB,GAAGF,aAAa,CAACG,yBAAd,EAAjC;;QACA,KAAKC,oDAAL,GAA4DF,wBAAwB,IAAIpC,uBAAuB,CAACuC,IAAxB,CAA6BH,wBAA7B,CAAxF;MACA,CAJD,MAIO;QACN,KAAKD,wBAAL,GAAgCK,SAAhC;QACA,KAAKF,oDAAL,GAA4DE,SAA5D;MACA;IACD;IAED;AACD;AACA;AACA;AACA;AACA;AACA;;;;WACC,0CAAiCC,cAAjC,EAAiDC,QAAjD,EAA2D;MAC1D,IAAI,CAAC,KAAKP,wBAAV,EAAoC;QACnC;MACA;;MACD,4BAII9C,iDAAiD,CACpDoD,cADoD,EAEpD,KAAKrC,QAF+C,CAJrD;MAAA,IACCuC,cADD,yBACCA,cADD;MAAA,IAECC,cAFD,yBAECA,cAFD;MAAA,IAGCC,WAHD,yBAGCA,WAHD;;MAQA,IAAID,cAAc,KAAKH,cAAvB,EAAuC;QACtC;MACA;;MACD,KAAKK,yBAAL,CACCH,cADD,EAECE,WAFD,EAGCD,cAHD,EAICH,cAJD,EAKCC,QALD;MAOA,OAAO,IAAP;IACA;IAED;AACD;AACA;AACA;AACA;;;;WACC,iDAAwCD,cAAxC,EAAwDM,6BAAxD,EAAuFL,QAAvF,EAAiG;MAChG,IAAI,CAAC,KAAKnB,qCAAV,EAAiD;QAChD,OAAO,KAAKC,gCAAL,CAAsCiB,cAAtC,EAAsDC,QAAtD,CAAP;MACA;;MACD,IAAI,CAAC,KAAKJ,oDAAV,EAAgE;QAC/D;MACA;;MACD,6BAIIjD,iDAAiD,CACpDoD,cADoD,EAEpD,KAAKrC,QAF+C,CAJrD;MAAA,IACCuC,cADD,0BACCA,cADD;MAAA,IAECC,cAFD,0BAECA,cAFD;MAAA,IAGCC,WAHD,0BAGCA,WAHD,CAPgG,CAehG;MACA;MACA;MACA;MACA;;MACA;;;MACA,IAAID,cAAc,KAAKG,6BAAvB,EAAsD;QACrD;MACA;;MACD,KAAKD,yBAAL,CACCH,cADD,EAECE,WAFD,EAGCD,cAHD,EAICH,cAJD,EAKCC,QALD;MAOA,OAAO,IAAP;IACA;;;WAED,mCACCC,cADD,EAECE,WAFD,EAGCZ,yBAHD,EAICQ,cAJD,EAKCC,QALD,EAME;MACD,IAAIM,4CAAJ;MACA,IAAIC,qCAAJ,CAFC,CAGD;;MACA,IAAMC,8BAA8B,GAAGT,cAAc,CAACU,WAAf,CAA2BlB,yBAA3B,CAAvC,CAJC,CAKD;MACA;MACA;MACA;MACA;;MACA,IAAIiB,8BAA8B,IAAI,CAAlC,IACHA,8BAA8B,KAAKT,cAAc,CAACxB,MAAf,GAAwBgB,yBAAyB,CAAChB,MADtF,EAC8F;QAC7FgC,qCAAqC,GAAG,IAAxC,CAD6F,CAE7F;QACA;QACA;QACA;QACA;;QACA,IAAMG,0BAA0B,GAAGX,cAAc,CAACY,KAAf,CAAqB,CAArB,EAAwBH,8BAAxB,CAAnC,CAP6F,CAQ7F;QACA;QACA;QACA;QACA;;QACA,IAAIE,0BAA0B,KAAKT,cAAnC,EAAmD;UAClDK,4CAA4C,GAAGI,0BAA/C;QACA;MACD;;MACDV,QAAQ,CAAC;QACRC,cAAc,EAAdA,cADQ;QAERE,WAAW,EAAXA,WAFQ;QAGRZ,yBAAyB,EAAzBA,yBAHQ;QAIRgB,qCAAqC,EAArCA,qCAJQ;QAKRD,4CAA4C,EAA5CA;MALQ,CAAD,CAAR,CA5BC,CAmCD;MACA;;MACA,KAAKzB,qCAAL,GAA6C,IAA7C;MACA,KAAKlB,iCAAL;IACA;;;WAED,4CAAmCE,KAAnC,EAA0C;MACzC;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA,IAAI,KAAK+C,uCAAL,CACH/C,KAAK,CAACkB,iBAAN,EADG,EAEHlB,KAAK,CAAC0B,yBAFH,EAGH,UAACP,WAAD;QAAA,OAAiBnB,KAAK,CAACoB,MAAN,CAAaD,WAAb,CAAjB;MAAA,CAHG,CAAJ,EAIG;QACF,OAAO,IAAP;MACA,CAxCwC,CAyCzC;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;;;MACA,IAAI,KAAKP,gBAAL,CAAsBZ,KAAtB,CAAJ,EAAkC;QACjC,KAAKgD,8CAAL,CAAoDhD,KAApD;QACA,OAAO,IAAP;MACA,CA1DwC,CA2DzC;MACA;MACA;MACA;MACA;MACA;MACA;;;MACA,IAAI,KAAKiD,cAAL,CAAoBjD,KAApB,CAAJ,EAAgC;QAC/B,KAAKgD,8CAAL,CAAoDhD,KAApD;QACA,OAAO,IAAP;MACA;IACD;;;WAED,0BAAiBA,KAAjB,EAAwB;MACvB;MACA;MACA,IACCe,aADD,GAKIf,KALJ,CACCe,aADD;MAAA,IAECmC,SAFD,GAKIlD,KALJ,CAECkD,SAFD;MAAA,IAGC9C,MAHD,GAKIJ,KALJ,CAGCI,MAHD;MAAA,IAICsB,yBAJD,GAKI1B,KALJ,CAIC0B,yBAJD;;MAMA,IAAIX,aAAa,IAAImC,SAArB,EAAgC;QAC/B;MACA,CAXsB,CAYvB;MACA;MACA;MACA;;;MACA,IAAMC,gBAAgB,GAAGpE,cAAc,CACtCqB,MADsC,EAEtC,KAAKT,cAFiC,EAGtC,KAAKC,kBAHiC,EAItC,KAAKC,QAAL,CAAcA,QAJwB,CAAvC;;MAMA,IAAIsD,gBAAgB,KAAKlB,SAArB,IAAkCkB,gBAAgB,KAAK/C,MAA3D,EAAmE;QAClE;QACA;QACAJ,KAAK,CAACoB,MAAN,CAAa;UACZ8B,SAAS,EAAE9C,MAAM,CAAC0C,KAAP,CAAa,CAAb,EAAgB1C,MAAM,CAACM,MAAP,GAAgByC,gBAAgB,CAACzC,MAAjD;QADC,CAAb;QAGA,KAAKJ,wBAAL,CAA8BN,KAA9B,EAAqC;UACpCoD,OAAO,EAAEnB,SAD2B;UAEpCZ,WAAW,EAAEY;QAFuB,CAArC;QAIA,OAAO,IAAP;MACA;IACD;;;WAED,wBAAejC,KAAf,EAAsB;MACrB,IAAI,CAACA,KAAK,CAACe,aAAX,EAA0B;QACzB,6BAGIlC,+DAA+D,CAClEmB,KAAK,CAACI,MAD4D,EAElE,KAAKT,cAF6D,EAGlE,KAAKC,kBAH6D,EAIlE,KAAKC,QAAL,CAAcA,QAJoD,CAHnE;QAAA,IACqBwD,cADrB,0BACC9B,kBADD;QAAA,IAECC,MAFD,0BAECA,MAFD;;QASA,IAAI6B,cAAJ,EAAoB;UACnBrD,KAAK,CAACoB,MAAN,CAAa;YACZkC,WAAW,EAAE;UADD,CAAb;UAGA,KAAKhD,wBAAL,CAA8BN,KAA9B,EAAqC;YACpCoD,OAAO,EAAEpD,KAAK,CAACoD,OADqB;YAEpC/B,WAAW,EAAEgC;UAFuB,CAArC;UAIA,OAAO,IAAP;QACA;MACD;IACD;;;WAED,kCAAyBrD,KAAzB,SAA0D;MAAA,IAAxBoD,OAAwB,SAAxBA,OAAwB;MAAA,IAAf/B,WAAe,SAAfA,WAAe;MACzDrB,KAAK,CAACM,wBAAN,CAA+B8C,OAA/B,EAAwC/B,WAAxC,EADyD,CAEzD;;MACA,IAAIrB,KAAK,CAAC0B,yBAAV,EAAqC;QACpC1B,KAAK,CAACuD,8BAAN;QACA,KAAKzD,iCAAL;QACA,KAAKkB,qCAAL,GAA6CiB,SAA7C;MACA;IACD;;;WAED,wDAA+CjC,KAA/C,EAAsD;MACrD,IAAI,KAAKpB,yBAAL,CAA+BoB,KAA/B,CAAJ,EAA2C;QAC1C;QACA;QACA;QACA;QACA;QACA;QACA,KAAKiB,gCAAL,CACCjB,KAAK,CAACkB,iBAAN,EADD,EAEC,UAACC,WAAD;UAAA,OAAiBnB,KAAK,CAACoB,MAAN,CAAaD,WAAb,CAAjB;QAAA,CAFD;MAIA;IACD;;;;;AAGF;AACA;AACA;AACA;AACA;;;SAjZqBzB,e;;AAkZrB,SAAS8D,2BAAT,CAAqCzD,IAArC,EAA2C;EAC1C;EACA,IAAM0D,QAAQ,GAAG1D,IAAI,CAAC2D,MAAL,CAAYnE,iCAAZ,CAAjB;;EACA,IAAIkE,QAAQ,GAAG,CAAf,EAAkB;IACjB;EACA,CALyC,CAM1C;;;EACA1D,IAAI,GAAGA,IAAI,CAAC+C,KAAL,CAAWW,QAAX,CAAP,CAP0C,CAQ1C;;EACA,IAAItD,OAAJ;;EACA,IAAIJ,IAAI,CAAC,CAAD,CAAJ,KAAY,GAAhB,EAAqB;IACpBI,OAAO,GAAG,IAAV;IACAJ,IAAI,GAAGA,IAAI,CAAC+C,KAAL,CAAW,IAAIpC,MAAf,CAAP;EACA,CAbyC,CAc1C;;;EACAX,IAAI,GAAGA,IAAI,CAAC4D,OAAL,CAAanE,qCAAb,EAAoD,EAApD,CAAP,CAf0C,CAgB1C;;EACA,IAAIW,OAAJ,EAAa;IACZJ,IAAI,GAAG,MAAMA,IAAb;EACA;;EACD,OAAOA,IAAP;AACA;AAED;AACA;AACA;AACA;AACA;;;AACA,SAAS6D,8BAAT,CAAwC7D,IAAxC,EAA8C;EAC7C;EACA,IAAM8D,eAAe,GAAGL,2BAA2B,CAACzD,IAAD,CAA3B,IAAqC,EAA7D,CAF6C,CAG7C;;EACA,IAAI8D,eAAe,CAAC,CAAD,CAAf,KAAuB,GAA3B,EAAgC;IAC/B,OAAO,CAACA,eAAe,CAACf,KAAhB,CAAsB,IAAIpC,MAA1B,CAAD,EAAoC,IAApC,CAAP;EACA;;EACD,OAAO,CAACmD,eAAD,CAAP;AACA;AAED;AACA;AACA;AACA;AACA;;;AACA,OAAO,SAAS5D,6BAAT,CAAuCF,IAAvC,EAA6C;EACnD,6BAAiC6D,8BAA8B,CAAC7D,IAAD,CAA/D;EAAA;EAAA,IAAKG,eAAL;EAAA,IAAsBC,OAAtB,6BADmD,CAEnD;EACA;EACA;;;EACA,IAAI,CAACd,gDAAgD,CAAC2C,IAAjD,CAAsD9B,eAAtD,CAAL,EAA6E;IAC5EA,eAAe,GAAG,EAAlB;EACA;;EACD,OAAO,CAACA,eAAD,EAAkBC,OAAlB,CAAP;AACA"}