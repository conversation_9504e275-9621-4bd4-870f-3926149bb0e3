{"version": 3, "file": "validatePhoneNumberLength.js", "names": ["normalizeArguments", "parsePhoneNumberWithError", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "checkNumberLength", "validatePhoneNumberLength", "arguments", "text", "options", "metadata", "extract", "phoneNumber", "selectNumberingPlan", "countryCallingCode", "result", "nationalNumber", "error", "message"], "sources": ["../source/validatePhoneNumberLength.js"], "sourcesContent": ["import normalizeArguments from './normalizeArguments.js'\r\nimport parsePhoneNumberWithError from './parsePhoneNumberWithError_.js'\r\nimport ParseError from './ParseError.js'\r\nimport Metadata from './metadata.js'\r\nimport checkNumberLength from './helpers/checkNumberLength.js'\r\n\r\nexport default function validatePhoneNumberLength() {\r\n\tlet { text, options, metadata } = normalizeArguments(arguments)\r\n\toptions = {\r\n\t\t...options,\r\n\t\textract: false\r\n\t}\r\n\r\n\t// Parse phone number.\r\n\ttry {\r\n\t\tconst phoneNumber = parsePhoneNumberWithError(text, options, metadata)\r\n\t\tmetadata = new Metadata(metadata)\r\n\t\tmetadata.selectNumberingPlan(phoneNumber.countryCallingCode)\r\n\t\tconst result = checkNumberLength(phoneNumber.nationalNumber, metadata)\r\n\t\tif (result !== 'IS_POSSIBLE') {\r\n\t\t\treturn result\r\n\t\t}\r\n\t} catch (error) {\r\n\t\t/* istanbul ignore else */\r\n\t\tif (error instanceof ParseError) {\r\n\t\t\treturn error.message\r\n\t\t} else {\r\n\t\t\tthrow error\r\n\t\t}\r\n\t}\r\n}"], "mappings": ";;;;;;AAAA,OAAOA,kBAAP,MAA+B,yBAA/B;AACA,OAAOC,yBAAP,MAAsC,iCAAtC;AACA,OAAOC,UAAP,MAAuB,iBAAvB;AACA,OAAOC,QAAP,MAAqB,eAArB;AACA,OAAOC,iBAAP,MAA8B,gCAA9B;AAEA,eAAe,SAASC,yBAAT,GAAqC;EACnD,0BAAkCL,kBAAkB,CAACM,SAAD,CAApD;EAAA,IAAMC,IAAN,uBAAMA,IAAN;EAAA,IAAYC,OAAZ,uBAAYA,OAAZ;EAAA,IAAqBC,QAArB,uBAAqBA,QAArB;;EACAD,OAAO,mCACHA,OADG;IAENE,OAAO,EAAE;EAFH,EAAP,CAFmD,CAOnD;;EACA,IAAI;IACH,IAAMC,WAAW,GAAGV,yBAAyB,CAACM,IAAD,EAAOC,OAAP,EAAgBC,QAAhB,CAA7C;IACAA,QAAQ,GAAG,IAAIN,QAAJ,CAAaM,QAAb,CAAX;IACAA,QAAQ,CAACG,mBAAT,CAA6BD,WAAW,CAACE,kBAAzC;IACA,IAAMC,MAAM,GAAGV,iBAAiB,CAACO,WAAW,CAACI,cAAb,EAA6BN,QAA7B,CAAhC;;IACA,IAAIK,MAAM,KAAK,aAAf,EAA8B;MAC7B,OAAOA,MAAP;IACA;EACD,CARD,CAQE,OAAOE,KAAP,EAAc;IACf;IACA,IAAIA,KAAK,YAAYd,UAArB,EAAiC;MAChC,OAAOc,KAAK,CAACC,OAAb;IACA,CAFD,MAEO;MACN,MAAMD,KAAN;IACA;EACD;AACD"}