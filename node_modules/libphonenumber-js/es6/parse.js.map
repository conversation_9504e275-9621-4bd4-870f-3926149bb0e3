{"version": 3, "file": "parse.js", "names": ["VALID_DIGITS", "PLUS_CHARS", "MIN_LENGTH_FOR_NSN", "MAX_LENGTH_FOR_NSN", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "isViablePhoneNumber", "isViablePhoneNumberStart", "extractExtension", "parseIncompletePhoneNumber", "getCountryCallingCode", "isPossibleNumber", "PhoneNumber", "matchesEntirely", "extractCountryCallingCode", "extractNationalNumber", "stripIddPrefix", "getCountryByCallingCode", "extractFormattedPhoneNumberFromPossibleRfc3966NumberUri", "MAX_INPUT_STRING_LENGTH", "PHONE_NUMBER_START_PATTERN", "RegExp", "AFTER_PHONE_NUMBER_END_PATTERN", "USE_NON_GEOGRAPHIC_COUNTRY_CODE", "parse", "text", "options", "metadata", "defaultCountry", "hasCountry", "v2", "Error", "parseInput", "extract", "formattedPhoneNumber", "number", "ext", "error", "parsePhoneNumber", "defaultCallingCode", "country", "nationalNumber", "countryCallingCode", "countryCallingCodeSource", "carrierCode", "hasSelectedNumberingPlan", "length", "phoneNumber", "__countryCallingCodeSource", "valid", "extended", "nationalNumberPattern", "result", "possible", "possibleLengths", "phone", "extractFormattedPhoneNumber", "throwOnError", "startsAt", "search", "slice", "replace", "withExtensionStripped", "selectNumberingPlan", "isNonGeographicCallingCode", "exactCountry"], "sources": ["../source/parse.js"], "sourcesContent": ["// This is a port of Google Android `libphonenumber`'s\r\n// `phonenumberutil.js` of December 31th, 2018.\r\n//\r\n// https://github.com/googlei18n/libphonenumber/commits/master/javascript/i18n/phonenumbers/phonenumberutil.js\r\n\r\nimport {\r\n\tVALID_DIGITS,\r\n\tPLUS_CHARS,\r\n\tMIN_LENGTH_FOR_NSN,\r\n\tMAX_LENGTH_FOR_NSN\r\n} from './constants.js'\r\n\r\nimport ParseError from './ParseError.js'\r\nimport Metadata from './metadata.js'\r\nimport isViablePhoneNumber, { isViablePhoneNumberStart } from './helpers/isViablePhoneNumber.js'\r\nimport extractExtension from './helpers/extension/extractExtension.js'\r\nimport parseIncompletePhoneNumber from './parseIncompletePhoneNumber.js'\r\nimport getCountryCallingCode from './getCountryCallingCode.js'\r\nimport { isPossibleNumber } from './isPossible.js'\r\n// import { parseRFC3966 } from './helpers/RFC3966.js'\r\nimport PhoneNumber from './PhoneNumber.js'\r\nimport matchesEntirely from './helpers/matchesEntirely.js'\r\nimport extractCountryCallingCode from './helpers/extractCountryCallingCode.js'\r\nimport extractNationalNumber from './helpers/extractNationalNumber.js'\r\nimport stripIddPrefix from './helpers/stripIddPrefix.js'\r\nimport getCountryByCallingCode from './helpers/getCountryByCallingCode.js'\r\nimport extractFormattedPhoneNumberFromPossibleRfc3966NumberUri from './helpers/extractFormattedPhoneNumberFromPossibleRfc3966NumberUri.js'\r\n\r\n// We don't allow input strings for parsing to be longer than 250 chars.\r\n// This prevents malicious input from consuming CPU.\r\nconst MAX_INPUT_STRING_LENGTH = 250\r\n\r\n// This consists of the plus symbol, digits, and arabic-indic digits.\r\nconst PHONE_NUMBER_START_PATTERN = new RegExp('[' + PLUS_CHARS + VALID_DIGITS + ']')\r\n\r\n// Regular expression of trailing characters that we want to remove.\r\n// A trailing `#` is sometimes used when writing phone numbers with extensions in US.\r\n// Example: \"+****************-910#\" number has extension \"910\".\r\nconst AFTER_PHONE_NUMBER_END_PATTERN = new RegExp('[^' + VALID_DIGITS + '#' + ']+$')\r\n\r\nconst USE_NON_GEOGRAPHIC_COUNTRY_CODE = false\r\n\r\n// Examples:\r\n//\r\n// ```js\r\n// parse('8 (800) 555-35-35', 'RU')\r\n// parse('8 (800) 555-35-35', 'RU', metadata)\r\n// parse('8 (800) 555-35-35', { country: { default: 'RU' } })\r\n// parse('8 (800) 555-35-35', { country: { default: 'RU' } }, metadata)\r\n// parse('****** 555 35 35')\r\n// parse('****** 555 35 35', metadata)\r\n// ```\r\n//\r\n\r\n/**\r\n * Parses a phone number.\r\n *\r\n * parse('123456789', { defaultCountry: 'RU', v2: true }, metadata)\r\n * parse('123456789', { defaultCountry: 'RU' }, metadata)\r\n * parse('123456789', undefined, metadata)\r\n *\r\n * @param  {string} input\r\n * @param  {object} [options]\r\n * @param  {object} metadata\r\n * @return {object|PhoneNumber?} If `options.v2: true` flag is passed, it returns a `PhoneNumber?` instance. Otherwise, returns an object of shape `{ phone: '...', country: '...' }` (or just `{}` if no phone number was parsed).\r\n */\r\nexport default function parse(text, options, metadata) {\r\n\t// If assigning the `{}` default value is moved to the arguments above,\r\n\t// code coverage would decrease for some weird reason.\r\n\toptions = options || {}\r\n\r\n\tmetadata = new Metadata(metadata)\r\n\r\n\t// Validate `defaultCountry`.\r\n\tif (options.defaultCountry && !metadata.hasCountry(options.defaultCountry)) {\r\n\t\tif (options.v2) {\r\n\t\t\tthrow new ParseError('INVALID_COUNTRY')\r\n\t\t}\r\n\t\tthrow new Error(`Unknown country: ${options.defaultCountry}`)\r\n\t}\r\n\r\n\t// Parse the phone number.\r\n\tconst { number: formattedPhoneNumber, ext, error } = parseInput(text, options.v2, options.extract)\r\n\r\n\t// If the phone number is not viable then return nothing.\r\n\tif (!formattedPhoneNumber) {\r\n\t\tif (options.v2) {\r\n\t\t\tif (error === 'TOO_SHORT') {\r\n\t\t\t\tthrow new ParseError('TOO_SHORT')\r\n\t\t\t}\r\n\t\t\tthrow new ParseError('NOT_A_NUMBER')\r\n\t\t}\r\n\t\treturn {}\r\n\t}\r\n\r\n\tconst {\r\n\t\tcountry,\r\n\t\tnationalNumber,\r\n\t\tcountryCallingCode,\r\n\t\tcountryCallingCodeSource,\r\n\t\tcarrierCode\r\n\t} = parsePhoneNumber(\r\n\t\tformattedPhoneNumber,\r\n\t\toptions.defaultCountry,\r\n\t\toptions.defaultCallingCode,\r\n\t\tmetadata\r\n\t)\r\n\r\n\tif (!metadata.hasSelectedNumberingPlan()) {\r\n\t\tif (options.v2) {\r\n\t\t\tthrow new ParseError('INVALID_COUNTRY')\r\n\t\t}\r\n\t\treturn {}\r\n\t}\r\n\r\n\t// Validate national (significant) number length.\r\n\tif (!nationalNumber || nationalNumber.length < MIN_LENGTH_FOR_NSN) {\r\n\t\t// Won't throw here because the regexp already demands length > 1.\r\n\t\t/* istanbul ignore if */\r\n\t\tif (options.v2) {\r\n\t\t\tthrow new ParseError('TOO_SHORT')\r\n\t\t}\r\n\t\t// Google's demo just throws an error in this case.\r\n\t\treturn {}\r\n\t}\r\n\r\n\t// Validate national (significant) number length.\r\n\t//\r\n\t// A sidenote:\r\n\t//\r\n\t// They say that sometimes national (significant) numbers\r\n\t// can be longer than `MAX_LENGTH_FOR_NSN` (e.g. in Germany).\r\n\t// https://github.com/googlei18n/libphonenumber/blob/7e1748645552da39c4e1ba731e47969d97bdb539/resources/phonenumber.proto#L36\r\n\t// Such numbers will just be discarded.\r\n\t//\r\n\tif (nationalNumber.length > MAX_LENGTH_FOR_NSN) {\r\n\t\tif (options.v2) {\r\n\t\t\tthrow new ParseError('TOO_LONG')\r\n\t\t}\r\n\t\t// Google's demo just throws an error in this case.\r\n\t\treturn {}\r\n\t}\r\n\r\n\tif (options.v2) {\r\n\t\tconst phoneNumber = new PhoneNumber(\r\n\t\t\tcountryCallingCode,\r\n\t\t\tnationalNumber,\r\n\t\t\tmetadata.metadata\r\n\t\t)\r\n\t\tif (country) {\r\n\t\t\tphoneNumber.country = country\r\n\t\t}\r\n\t\tif (carrierCode) {\r\n\t\t\tphoneNumber.carrierCode = carrierCode\r\n\t\t}\r\n\t\tif (ext) {\r\n\t\t\tphoneNumber.ext = ext\r\n\t\t}\r\n\t\tphoneNumber.__countryCallingCodeSource = countryCallingCodeSource\r\n\t\treturn phoneNumber\r\n\t}\r\n\r\n\t// Check if national phone number pattern matches the number.\r\n\t// National number pattern is different for each country,\r\n\t// even for those ones which are part of the \"NANPA\" group.\r\n\tconst valid = (options.extended ? metadata.hasSelectedNumberingPlan() : country) ?\r\n\t\tmatchesEntirely(nationalNumber, metadata.nationalNumberPattern()) :\r\n\t\tfalse\r\n\r\n\tif (!options.extended) {\r\n\t\treturn valid ? result(country, nationalNumber, ext) : {}\r\n\t}\r\n\r\n\t// isInternational: countryCallingCode !== undefined\r\n\r\n\treturn {\r\n\t\tcountry,\r\n\t\tcountryCallingCode,\r\n\t\tcarrierCode,\r\n\t\tvalid,\r\n\t\tpossible: valid ? true : (\r\n\t\t\toptions.extended === true &&\r\n\t\t\tmetadata.possibleLengths() &&\r\n\t\t\tisPossibleNumber(nationalNumber, metadata) ? true : false\r\n\t\t),\r\n\t\tphone: nationalNumber,\r\n\t\text\r\n\t}\r\n}\r\n\r\n/**\r\n * Extracts a formatted phone number from text.\r\n * Doesn't guarantee that the extracted phone number\r\n * is a valid phone number (for example, doesn't validate its length).\r\n * @param  {string} text\r\n * @param  {boolean} [extract] — If `false`, then will parse the entire `text` as a phone number.\r\n * @param  {boolean} [throwOnError] — By default, it won't throw if the text is too long.\r\n * @return {string}\r\n * @example\r\n * // Returns \"(*************\".\r\n * extractFormattedPhoneNumber(\"Call (************* for assistance.\")\r\n */\r\nfunction extractFormattedPhoneNumber(text, extract, throwOnError) {\r\n\tif (!text) {\r\n\t\treturn\r\n\t}\r\n\tif (text.length > MAX_INPUT_STRING_LENGTH) {\r\n\t\tif (throwOnError) {\r\n\t\t\tthrow new ParseError('TOO_LONG')\r\n\t\t}\r\n\t\treturn\r\n\t}\r\n\tif (extract === false) {\r\n\t\treturn text\r\n\t}\r\n\t// Attempt to extract a possible number from the string passed in\r\n\tconst startsAt = text.search(PHONE_NUMBER_START_PATTERN)\r\n\tif (startsAt < 0) {\r\n\t\treturn\r\n\t}\r\n\treturn text\r\n\t\t// Trim everything to the left of the phone number\r\n\t\t.slice(startsAt)\r\n\t\t// Remove trailing non-numerical characters\r\n\t\t.replace(AFTER_PHONE_NUMBER_END_PATTERN, '')\r\n}\r\n\r\n/**\r\n * @param  {string} text - Input.\r\n * @param  {boolean} v2 - Legacy API functions don't pass `v2: true` flag.\r\n * @param  {boolean} [extract] - Whether to extract a phone number from `text`, or attempt to parse the entire text as a phone number.\r\n * @return {object} `{ ?number, ?ext }`.\r\n */\r\nfunction parseInput(text, v2, extract) {\r\n\t// // Parse RFC 3966 phone number URI.\r\n\t// if (text && text.indexOf('tel:') === 0) {\r\n\t// \treturn parseRFC3966(text)\r\n\t// }\r\n\t// let number = extractFormattedPhoneNumber(text, extract, v2)\r\n\tlet number = extractFormattedPhoneNumberFromPossibleRfc3966NumberUri(text, {\r\n\t\textractFormattedPhoneNumber: (text) => extractFormattedPhoneNumber(text, extract, v2)\r\n\t})\r\n\t// If the phone number is not viable, then abort.\r\n\tif (!number) {\r\n\t\treturn {}\r\n\t}\r\n\tif (!isViablePhoneNumber(number)) {\r\n\t\tif (isViablePhoneNumberStart(number)) {\r\n\t\t\treturn { error: 'TOO_SHORT' }\r\n\t\t}\r\n\t\treturn {}\r\n\t}\r\n\t// Attempt to parse extension first, since it doesn't require region-specific\r\n\t// data and we want to have the non-normalised number here.\r\n\tconst withExtensionStripped = extractExtension(number)\r\n\tif (withExtensionStripped.ext) {\r\n\t\treturn withExtensionStripped\r\n\t}\r\n\treturn { number }\r\n}\r\n\r\n/**\r\n * Creates `parse()` result object.\r\n */\r\nfunction result(country, nationalNumber, ext) {\r\n\tconst result = {\r\n\t\tcountry,\r\n\t\tphone: nationalNumber\r\n\t}\r\n\tif (ext) {\r\n\t\tresult.ext = ext\r\n\t}\r\n\treturn result\r\n}\r\n\r\n/**\r\n * Parses a viable phone number.\r\n * @param {string} formattedPhoneNumber — Example: \"(*************\".\r\n * @param {string} [defaultCountry]\r\n * @param {string} [defaultCallingCode]\r\n * @param {Metadata} metadata\r\n * @return {object} Returns `{ country: string?, countryCallingCode: string?, nationalNumber: string? }`.\r\n */\r\nfunction parsePhoneNumber(\r\n\tformattedPhoneNumber,\r\n\tdefaultCountry,\r\n\tdefaultCallingCode,\r\n\tmetadata\r\n) {\r\n\t// Extract calling code from phone number.\r\n\tlet { countryCallingCodeSource, countryCallingCode, number } = extractCountryCallingCode(\r\n\t\tparseIncompletePhoneNumber(formattedPhoneNumber),\r\n\t\tdefaultCountry,\r\n\t\tdefaultCallingCode,\r\n\t\tmetadata.metadata\r\n\t)\r\n\r\n\t// Choose a country by `countryCallingCode`.\r\n\tlet country\r\n\tif (countryCallingCode) {\r\n\t\tmetadata.selectNumberingPlan(countryCallingCode)\r\n\t}\r\n\t// If `formattedPhoneNumber` is passed in \"national\" format\r\n\t// then `number` is defined and `countryCallingCode` is `undefined`.\r\n\telse if (number && (defaultCountry || defaultCallingCode)) {\r\n\t\tmetadata.selectNumberingPlan(defaultCountry, defaultCallingCode)\r\n\t\tif (defaultCountry) {\r\n\t\t\tcountry = defaultCountry\r\n\t\t} else {\r\n\t\t\t/* istanbul ignore if */\r\n\t\t\tif (USE_NON_GEOGRAPHIC_COUNTRY_CODE) {\r\n\t\t\t\tif (metadata.isNonGeographicCallingCode(defaultCallingCode)) {\r\n\t\t\t\t\tcountry = '001'\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t\tcountryCallingCode = defaultCallingCode || getCountryCallingCode(defaultCountry, metadata.metadata)\r\n\t}\r\n\telse return {}\r\n\r\n\tif (!number) {\r\n\t\treturn {\r\n\t\t\tcountryCallingCodeSource,\r\n\t\t\tcountryCallingCode\r\n\t\t}\r\n\t}\r\n\r\n\tconst {\r\n\t\tnationalNumber,\r\n\t\tcarrierCode\r\n\t} = extractNationalNumber(\r\n\t\tparseIncompletePhoneNumber(number),\r\n\t\tmetadata\r\n\t)\r\n\r\n\t// Sometimes there are several countries\r\n\t// corresponding to the same country phone code\r\n\t// (e.g. NANPA countries all having `1` country phone code).\r\n\t// Therefore, to reliably determine the exact country,\r\n\t// national (significant) number should have been parsed first.\r\n\t//\r\n\t// When `metadata.json` is generated, all \"ambiguous\" country phone codes\r\n\t// get their countries populated with the full set of\r\n\t// \"phone number type\" regular expressions.\r\n\t//\r\n\tconst exactCountry = getCountryByCallingCode(countryCallingCode, {\r\n\t\tnationalNumber,\r\n\t\tdefaultCountry,\r\n\t\tmetadata\r\n\t})\r\n\tif (exactCountry) {\r\n\t\tcountry = exactCountry\r\n\t\t/* istanbul ignore if */\r\n\t\tif (exactCountry === '001') {\r\n\t\t\t// Can't happen with `USE_NON_GEOGRAPHIC_COUNTRY_CODE` being `false`.\r\n\t\t\t// If `USE_NON_GEOGRAPHIC_COUNTRY_CODE` is set to `true` for some reason,\r\n\t\t\t// then remove the \"istanbul ignore if\".\r\n\t\t} else {\r\n\t\t\tmetadata.country(country)\r\n\t\t}\r\n\t}\r\n\r\n\treturn {\r\n\t\tcountry,\r\n\t\tcountryCallingCode,\r\n\t\tcountryCallingCodeSource,\r\n\t\tnationalNumber,\r\n\t\tcarrierCode\r\n\t}\r\n}"], "mappings": "AAAA;AACA;AACA;AACA;AAEA,SACCA,YADD,EAECC,UAFD,EAGCC,kBAHD,EAICC,kBAJD,QAKO,gBALP;AAOA,OAAOC,UAAP,MAAuB,iBAAvB;AACA,OAAOC,QAAP,MAAqB,eAArB;AACA,OAAOC,mBAAP,IAA8BC,wBAA9B,QAA8D,kCAA9D;AACA,OAAOC,gBAAP,MAA6B,yCAA7B;AACA,OAAOC,0BAAP,MAAuC,iCAAvC;AACA,OAAOC,qBAAP,MAAkC,4BAAlC;AACA,SAASC,gBAAT,QAAiC,iBAAjC,C,CACA;;AACA,OAAOC,WAAP,MAAwB,kBAAxB;AACA,OAAOC,eAAP,MAA4B,8BAA5B;AACA,OAAOC,yBAAP,MAAsC,wCAAtC;AACA,OAAOC,qBAAP,MAAkC,oCAAlC;AACA,OAAOC,cAAP,MAA2B,6BAA3B;AACA,OAAOC,uBAAP,MAAoC,sCAApC;AACA,OAAOC,uDAAP,MAAoE,sEAApE,C,CAEA;AACA;;AACA,IAAMC,uBAAuB,GAAG,GAAhC,C,CAEA;;AACA,IAAMC,0BAA0B,GAAG,IAAIC,MAAJ,CAAW,MAAMpB,UAAN,GAAmBD,YAAnB,GAAkC,GAA7C,CAAnC,C,CAEA;AACA;AACA;;AACA,IAAMsB,8BAA8B,GAAG,IAAID,MAAJ,CAAW,OAAOrB,YAAP,GAAsB,GAAtB,GAA4B,KAAvC,CAAvC;AAEA,IAAMuB,+BAA+B,GAAG,KAAxC,C,CAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AACA,eAAe,SAASC,KAAT,CAAeC,IAAf,EAAqBC,OAArB,EAA8BC,QAA9B,EAAwC;EACtD;EACA;EACAD,OAAO,GAAGA,OAAO,IAAI,EAArB;EAEAC,QAAQ,GAAG,IAAItB,QAAJ,CAAasB,QAAb,CAAX,CALsD,CAOtD;;EACA,IAAID,OAAO,CAACE,cAAR,IAA0B,CAACD,QAAQ,CAACE,UAAT,CAAoBH,OAAO,CAACE,cAA5B,CAA/B,EAA4E;IAC3E,IAAIF,OAAO,CAACI,EAAZ,EAAgB;MACf,MAAM,IAAI1B,UAAJ,CAAe,iBAAf,CAAN;IACA;;IACD,MAAM,IAAI2B,KAAJ,4BAA8BL,OAAO,CAACE,cAAtC,EAAN;EACA,CAbqD,CAetD;;;EACA,kBAAqDI,UAAU,CAACP,IAAD,EAAOC,OAAO,CAACI,EAAf,EAAmBJ,OAAO,CAACO,OAA3B,CAA/D;EAAA,IAAgBC,oBAAhB,eAAQC,MAAR;EAAA,IAAsCC,GAAtC,eAAsCA,GAAtC;EAAA,IAA2CC,KAA3C,eAA2CA,KAA3C,CAhBsD,CAkBtD;;;EACA,IAAI,CAACH,oBAAL,EAA2B;IAC1B,IAAIR,OAAO,CAACI,EAAZ,EAAgB;MACf,IAAIO,KAAK,KAAK,WAAd,EAA2B;QAC1B,MAAM,IAAIjC,UAAJ,CAAe,WAAf,CAAN;MACA;;MACD,MAAM,IAAIA,UAAJ,CAAe,cAAf,CAAN;IACA;;IACD,OAAO,EAAP;EACA;;EAED,wBAMIkC,gBAAgB,CACnBJ,oBADmB,EAEnBR,OAAO,CAACE,cAFW,EAGnBF,OAAO,CAACa,kBAHW,EAInBZ,QAJmB,CANpB;EAAA,IACCa,OADD,qBACCA,OADD;EAAA,IAECC,cAFD,qBAECA,cAFD;EAAA,IAGCC,kBAHD,qBAGCA,kBAHD;EAAA,IAICC,wBAJD,qBAICA,wBAJD;EAAA,IAKCC,WALD,qBAKCA,WALD;;EAaA,IAAI,CAACjB,QAAQ,CAACkB,wBAAT,EAAL,EAA0C;IACzC,IAAInB,OAAO,CAACI,EAAZ,EAAgB;MACf,MAAM,IAAI1B,UAAJ,CAAe,iBAAf,CAAN;IACA;;IACD,OAAO,EAAP;EACA,CA/CqD,CAiDtD;;;EACA,IAAI,CAACqC,cAAD,IAAmBA,cAAc,CAACK,MAAf,GAAwB5C,kBAA/C,EAAmE;IAClE;;IACA;IACA,IAAIwB,OAAO,CAACI,EAAZ,EAAgB;MACf,MAAM,IAAI1B,UAAJ,CAAe,WAAf,CAAN;IACA,CALiE,CAMlE;;;IACA,OAAO,EAAP;EACA,CA1DqD,CA4DtD;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;EACA,IAAIqC,cAAc,CAACK,MAAf,GAAwB3C,kBAA5B,EAAgD;IAC/C,IAAIuB,OAAO,CAACI,EAAZ,EAAgB;MACf,MAAM,IAAI1B,UAAJ,CAAe,UAAf,CAAN;IACA,CAH8C,CAI/C;;;IACA,OAAO,EAAP;EACA;;EAED,IAAIsB,OAAO,CAACI,EAAZ,EAAgB;IACf,IAAMiB,WAAW,GAAG,IAAInC,WAAJ,CACnB8B,kBADmB,EAEnBD,cAFmB,EAGnBd,QAAQ,CAACA,QAHU,CAApB;;IAKA,IAAIa,OAAJ,EAAa;MACZO,WAAW,CAACP,OAAZ,GAAsBA,OAAtB;IACA;;IACD,IAAII,WAAJ,EAAiB;MAChBG,WAAW,CAACH,WAAZ,GAA0BA,WAA1B;IACA;;IACD,IAAIR,GAAJ,EAAS;MACRW,WAAW,CAACX,GAAZ,GAAkBA,GAAlB;IACA;;IACDW,WAAW,CAACC,0BAAZ,GAAyCL,wBAAzC;IACA,OAAOI,WAAP;EACA,CA9FqD,CAgGtD;EACA;EACA;;;EACA,IAAME,KAAK,GAAG,CAACvB,OAAO,CAACwB,QAAR,GAAmBvB,QAAQ,CAACkB,wBAAT,EAAnB,GAAyDL,OAA1D,IACb3B,eAAe,CAAC4B,cAAD,EAAiBd,QAAQ,CAACwB,qBAAT,EAAjB,CADF,GAEb,KAFD;;EAIA,IAAI,CAACzB,OAAO,CAACwB,QAAb,EAAuB;IACtB,OAAOD,KAAK,GAAGG,MAAM,CAACZ,OAAD,EAAUC,cAAV,EAA0BL,GAA1B,CAAT,GAA0C,EAAtD;EACA,CAzGqD,CA2GtD;;;EAEA,OAAO;IACNI,OAAO,EAAPA,OADM;IAENE,kBAAkB,EAAlBA,kBAFM;IAGNE,WAAW,EAAXA,WAHM;IAINK,KAAK,EAALA,KAJM;IAKNI,QAAQ,EAAEJ,KAAK,GAAG,IAAH,GACdvB,OAAO,CAACwB,QAAR,KAAqB,IAArB,IACAvB,QAAQ,CAAC2B,eAAT,EADA,IAEA3C,gBAAgB,CAAC8B,cAAD,EAAiBd,QAAjB,CAFhB,GAE6C,IAF7C,GAEoD,KAR/C;IAUN4B,KAAK,EAAEd,cAVD;IAWNL,GAAG,EAAHA;EAXM,CAAP;AAaA;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AACA,SAASoB,4BAAT,CAAqC/B,IAArC,EAA2CQ,OAA3C,EAAoDwB,YAApD,EAAkE;EACjE,IAAI,CAAChC,IAAL,EAAW;IACV;EACA;;EACD,IAAIA,IAAI,CAACqB,MAAL,GAAc3B,uBAAlB,EAA2C;IAC1C,IAAIsC,YAAJ,EAAkB;MACjB,MAAM,IAAIrD,UAAJ,CAAe,UAAf,CAAN;IACA;;IACD;EACA;;EACD,IAAI6B,OAAO,KAAK,KAAhB,EAAuB;IACtB,OAAOR,IAAP;EACA,CAZgE,CAajE;;;EACA,IAAMiC,QAAQ,GAAGjC,IAAI,CAACkC,MAAL,CAAYvC,0BAAZ,CAAjB;;EACA,IAAIsC,QAAQ,GAAG,CAAf,EAAkB;IACjB;EACA;;EACD,OAAOjC,IAAI,CACV;EADU,CAETmC,KAFK,CAECF,QAFD,EAGN;EAHM,CAILG,OAJK,CAIGvC,8BAJH,EAImC,EAJnC,CAAP;AAKA;AAED;AACA;AACA;AACA;AACA;AACA;;;AACA,SAASU,UAAT,CAAoBP,IAApB,EAA0BK,EAA1B,EAA8BG,OAA9B,EAAuC;EACtC;EACA;EACA;EACA;EACA;EACA,IAAIE,MAAM,GAAGjB,uDAAuD,CAACO,IAAD,EAAO;IAC1E+B,2BAA2B,EAAE,qCAAC/B,IAAD;MAAA,OAAU+B,4BAA2B,CAAC/B,IAAD,EAAOQ,OAAP,EAAgBH,EAAhB,CAArC;IAAA;EAD6C,CAAP,CAApE,CANsC,CAStC;;EACA,IAAI,CAACK,MAAL,EAAa;IACZ,OAAO,EAAP;EACA;;EACD,IAAI,CAAC7B,mBAAmB,CAAC6B,MAAD,CAAxB,EAAkC;IACjC,IAAI5B,wBAAwB,CAAC4B,MAAD,CAA5B,EAAsC;MACrC,OAAO;QAAEE,KAAK,EAAE;MAAT,CAAP;IACA;;IACD,OAAO,EAAP;EACA,CAlBqC,CAmBtC;EACA;;;EACA,IAAMyB,qBAAqB,GAAGtD,gBAAgB,CAAC2B,MAAD,CAA9C;;EACA,IAAI2B,qBAAqB,CAAC1B,GAA1B,EAA+B;IAC9B,OAAO0B,qBAAP;EACA;;EACD,OAAO;IAAE3B,MAAM,EAANA;EAAF,CAAP;AACA;AAED;AACA;AACA;;;AACA,SAASiB,MAAT,CAAgBZ,OAAhB,EAAyBC,cAAzB,EAAyCL,GAAzC,EAA8C;EAC7C,IAAMgB,MAAM,GAAG;IACdZ,OAAO,EAAPA,OADc;IAEde,KAAK,EAAEd;EAFO,CAAf;;EAIA,IAAIL,GAAJ,EAAS;IACRgB,MAAM,CAAChB,GAAP,GAAaA,GAAb;EACA;;EACD,OAAOgB,MAAP;AACA;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACA,SAASd,gBAAT,CACCJ,oBADD,EAECN,cAFD,EAGCW,kBAHD,EAICZ,QAJD,EAKE;EACD;EACA,4BAA+Db,yBAAyB,CACvFL,0BAA0B,CAACyB,oBAAD,CAD6D,EAEvFN,cAFuF,EAGvFW,kBAHuF,EAIvFZ,QAAQ,CAACA,QAJ8E,CAAxF;EAAA,IAAMgB,wBAAN,yBAAMA,wBAAN;EAAA,IAAgCD,kBAAhC,yBAAgCA,kBAAhC;EAAA,IAAoDP,MAApD,yBAAoDA,MAApD,CAFC,CASD;;;EACA,IAAIK,OAAJ;;EACA,IAAIE,kBAAJ,EAAwB;IACvBf,QAAQ,CAACoC,mBAAT,CAA6BrB,kBAA7B;EACA,CAFD,CAGA;EACA;EAJA,KAKK,IAAIP,MAAM,KAAKP,cAAc,IAAIW,kBAAvB,CAAV,EAAsD;IAC1DZ,QAAQ,CAACoC,mBAAT,CAA6BnC,cAA7B,EAA6CW,kBAA7C;;IACA,IAAIX,cAAJ,EAAoB;MACnBY,OAAO,GAAGZ,cAAV;IACA,CAFD,MAEO;MACN;MACA,IAAIL,+BAAJ,EAAqC;QACpC,IAAII,QAAQ,CAACqC,0BAAT,CAAoCzB,kBAApC,CAAJ,EAA6D;UAC5DC,OAAO,GAAG,KAAV;QACA;MACD;IACD;;IACDE,kBAAkB,GAAGH,kBAAkB,IAAI7B,qBAAqB,CAACkB,cAAD,EAAiBD,QAAQ,CAACA,QAA1B,CAAhE;EACA,CAbI,MAcA,OAAO,EAAP;;EAEL,IAAI,CAACQ,MAAL,EAAa;IACZ,OAAO;MACNQ,wBAAwB,EAAxBA,wBADM;MAEND,kBAAkB,EAAlBA;IAFM,CAAP;EAIA;;EAED,4BAGI3B,qBAAqB,CACxBN,0BAA0B,CAAC0B,MAAD,CADF,EAExBR,QAFwB,CAHzB;EAAA,IACCc,cADD,yBACCA,cADD;EAAA,IAECG,WAFD,yBAECA,WAFD,CAvCC,CA+CD;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;EACA,IAAMqB,YAAY,GAAGhD,uBAAuB,CAACyB,kBAAD,EAAqB;IAChED,cAAc,EAAdA,cADgE;IAEhEb,cAAc,EAAdA,cAFgE;IAGhED,QAAQ,EAARA;EAHgE,CAArB,CAA5C;;EAKA,IAAIsC,YAAJ,EAAkB;IACjBzB,OAAO,GAAGyB,YAAV;IACA;;IACA,IAAIA,YAAY,KAAK,KAArB,EAA4B,CAC3B;MACA;MACA;IACA,CAJD,MAIO;MACNtC,QAAQ,CAACa,OAAT,CAAiBA,OAAjB;IACA;EACD;;EAED,OAAO;IACNA,OAAO,EAAPA,OADM;IAENE,kBAAkB,EAAlBA,kBAFM;IAGNC,wBAAwB,EAAxBA,wBAHM;IAINF,cAAc,EAAdA,cAJM;IAKNG,WAAW,EAAXA;EALM,CAAP;AAOA"}