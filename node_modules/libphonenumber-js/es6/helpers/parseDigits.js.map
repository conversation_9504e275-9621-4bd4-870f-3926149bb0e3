{"version": 3, "file": "parseDigits.js", "names": ["DIGITS", "parseDigit", "character", "parseDigits", "string", "result", "split", "digit"], "sources": ["../../source/helpers/parseDigits.js"], "sourcesContent": ["// These mappings map a character (key) to a specific digit that should\r\n// replace it for normalization purposes. Non-European digits that\r\n// may be used in phone numbers are mapped to a European equivalent.\r\n//\r\n// E.g. in Iraq they don't write `+442323234` but rather `+٤٤٢٣٢٣٢٣٤`.\r\n//\r\nexport const DIGITS = {\r\n\t'0': '0',\r\n\t'1': '1',\r\n\t'2': '2',\r\n\t'3': '3',\r\n\t'4': '4',\r\n\t'5': '5',\r\n\t'6': '6',\r\n\t'7': '7',\r\n\t'8': '8',\r\n\t'9': '9',\r\n\t'\\uFF10': '0', // Fullwidth digit 0\r\n\t'\\uFF11': '1', // Fullwidth digit 1\r\n\t'\\uFF12': '2', // Fullwidth digit 2\r\n\t'\\uFF13': '3', // Fullwidth digit 3\r\n\t'\\uFF14': '4', // Fullwidth digit 4\r\n\t'\\uFF15': '5', // Fullwidth digit 5\r\n\t'\\uFF16': '6', // Fullwidth digit 6\r\n\t'\\uFF17': '7', // Fullwidth digit 7\r\n\t'\\uFF18': '8', // Fullwidth digit 8\r\n\t'\\uFF19': '9', // Fullwidth digit 9\r\n\t'\\u0660': '0', // Arabic-indic digit 0\r\n\t'\\u0661': '1', // Arabic-indic digit 1\r\n\t'\\u0662': '2', // Arabic-indic digit 2\r\n\t'\\u0663': '3', // Arabic-indic digit 3\r\n\t'\\u0664': '4', // Arabic-indic digit 4\r\n\t'\\u0665': '5', // Arabic-indic digit 5\r\n\t'\\u0666': '6', // Arabic-indic digit 6\r\n\t'\\u0667': '7', // Arabic-indic digit 7\r\n\t'\\u0668': '8', // Arabic-indic digit 8\r\n\t'\\u0669': '9', // Arabic-indic digit 9\r\n\t'\\u06F0': '0', // Eastern-Arabic digit 0\r\n\t'\\u06F1': '1', // Eastern-Arabic digit 1\r\n\t'\\u06F2': '2', // Eastern-Arabic digit 2\r\n\t'\\u06F3': '3', // Eastern-Arabic digit 3\r\n\t'\\u06F4': '4', // Eastern-Arabic digit 4\r\n\t'\\u06F5': '5', // Eastern-Arabic digit 5\r\n\t'\\u06F6': '6', // Eastern-Arabic digit 6\r\n\t'\\u06F7': '7', // Eastern-Arabic digit 7\r\n\t'\\u06F8': '8', // Eastern-Arabic digit 8\r\n\t'\\u06F9': '9'  // Eastern-Arabic digit 9\r\n}\r\n\r\nexport function parseDigit(character) {\r\n\treturn DIGITS[character]\r\n}\r\n\r\n/**\r\n * Parses phone number digits from a string.\r\n * Drops all punctuation leaving only digits.\r\n * Also converts wide-ascii and arabic-indic numerals to conventional numerals.\r\n * E.g. in Iraq they don't write `+442323234` but rather `+٤٤٢٣٢٣٢٣٤`.\r\n * @param  {string} string\r\n * @return {string}\r\n * @example\r\n * ```js\r\n * parseDigits('8 (800) 555')\r\n * // Outputs '8800555'.\r\n * ```\r\n */\r\nexport default function parseDigits(string) {\r\n\tlet result = ''\r\n\t// Using `.split('')` here instead of normal `for ... of`\r\n\t// because the importing application doesn't neccessarily include an ES6 polyfill.\r\n\t// The `.split('')` approach discards \"exotic\" UTF-8 characters\r\n\t// (the ones consisting of four bytes) but digits\r\n\t// (including non-European ones) don't fall into that range\r\n\t// so such \"exotic\" characters would be discarded anyway.\r\n\tfor (const character of string.split('')) {\r\n\t\tconst digit = parseDigit(character)\r\n\t\tif (digit) {\r\n\t\t\tresult += digit\r\n\t\t}\r\n\t}\r\n\treturn result\r\n}"], "mappings": ";;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,IAAMA,MAAM,GAAG;EACrB,KAAK,GADgB;EAErB,KAAK,GAFgB;EAGrB,KAAK,GAHgB;EAIrB,KAAK,GAJgB;EAKrB,KAAK,GALgB;EAMrB,KAAK,GANgB;EAOrB,KAAK,GAPgB;EAQrB,KAAK,GARgB;EASrB,KAAK,GATgB;EAUrB,KAAK,GAVgB;EAWrB,UAAU,GAXW;EAWN;EACf,UAAU,GAZW;EAYN;EACf,UAAU,GAbW;EAaN;EACf,UAAU,GAdW;EAcN;EACf,UAAU,GAfW;EAeN;EACf,UAAU,GAhBW;EAgBN;EACf,UAAU,GAjBW;EAiBN;EACf,UAAU,GAlBW;EAkBN;EACf,UAAU,GAnBW;EAmBN;EACf,UAAU,GApBW;EAoBN;EACf,UAAU,GArBW;EAqBN;EACf,UAAU,GAtBW;EAsBN;EACf,UAAU,GAvBW;EAuBN;EACf,UAAU,GAxBW;EAwBN;EACf,UAAU,GAzBW;EAyBN;EACf,UAAU,GA1BW;EA0BN;EACf,UAAU,GA3BW;EA2BN;EACf,UAAU,GA5BW;EA4BN;EACf,UAAU,GA7BW;EA6BN;EACf,UAAU,GA9BW;EA8BN;EACf,UAAU,GA/BW;EA+BN;EACf,UAAU,GAhCW;EAgCN;EACf,UAAU,GAjCW;EAiCN;EACf,UAAU,GAlCW;EAkCN;EACf,UAAU,GAnCW;EAmCN;EACf,UAAU,GApCW;EAoCN;EACf,UAAU,GArCW;EAqCN;EACf,UAAU,GAtCW;EAsCN;EACf,UAAU,GAvCW;EAuCN;EACf,UAAU,GAxCW,CAwCN;;AAxCM,CAAf;AA2CP,OAAO,SAASC,UAAT,CAAoBC,SAApB,EAA+B;EACrC,OAAOF,MAAM,CAACE,SAAD,CAAb;AACA;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AACA,eAAe,SAASC,WAAT,CAAqBC,MAArB,EAA6B;EAC3C,IAAIC,MAAM,GAAG,EAAb,CAD2C,CAE3C;EACA;EACA;EACA;EACA;EACA;;EACA,qDAAwBD,MAAM,CAACE,KAAP,CAAa,EAAb,CAAxB,wCAA0C;IAAA,IAA/BJ,SAA+B;IACzC,IAAMK,KAAK,GAAGN,UAAU,CAACC,SAAD,CAAxB;;IACA,IAAIK,KAAJ,EAAW;MACVF,MAAM,IAAIE,KAAV;IACA;EACD;;EACD,OAAOF,MAAP;AACA"}