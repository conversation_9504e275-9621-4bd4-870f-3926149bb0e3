{"version": 3, "file": "isObject.js", "names": ["objectConstructor", "constructor", "isObject", "object", "undefined"], "sources": ["../../source/helpers/isObject.js"], "sourcesContent": ["const objectConstructor = {}.constructor;\r\n\r\nexport default function isObject(object) {\r\n  return object !== undefined && object !== null && object.constructor === objectConstructor;\r\n}\r\n"], "mappings": "AAAA,IAAMA,iBAAiB,GAAG,GAAGC,WAA7B;AAEA,eAAe,SAASC,QAAT,CAAkBC,MAAlB,EAA0B;EACvC,OAAOA,MAAM,KAAKC,SAAX,IAAwBD,MAAM,KAAK,IAAnC,IAA2CA,MAAM,CAACF,WAAP,KAAuBD,iBAAzE;AACD"}