{"version": 3, "file": "extractNationalNumber.test.js", "names": ["extractNationalNumber", "<PERSON><PERSON><PERSON>", "oldMetadata", "type", "describe", "it", "_oldMetadata", "selectNumberingPlan", "should", "deep", "equal", "nationalNumber", "carrierCode", "undefined"], "sources": ["../../source/helpers/extractNationalNumber.test.js"], "sourcesContent": ["import extractNationalNumber from './extractNationalNumber.js'\r\n\r\nimport Metadata from '../metadata.js'\r\nimport oldMetadata from '../../test/metadata/1.0.0/metadata.min.json' assert { type: 'json' }\r\n\r\ndescribe('extractNationalNumber', function() {\r\n\tit('should extract a national number when using old metadata', function() {\r\n\t\tconst _oldMetadata = new Metadata(oldMetadata)\r\n\t\t_oldMetadata.selectNumberingPlan('RU')\r\n\t\textractNationalNumber('88005553535', _oldMetadata).should.deep.equal({\r\n\t\t\tnationalNumber: '8005553535',\r\n\t\t\tcarrierCode: undefined\r\n\t\t})\r\n\t})\r\n})"], "mappings": "AAAA,OAAOA,qBAAP,MAAkC,4BAAlC;AAEA,OAAOC,QAAP,MAAqB,gBAArB;AACA,OAAOC,WAAP,MAAwB,6CAAxB,UAA+EC,IAAI,EAAE,MAArF;AAEAC,QAAQ,CAAC,uBAAD,EAA0B,YAAW;EAC5CC,EAAE,CAAC,0DAAD,EAA6D,YAAW;IACzE,IAAMC,YAAY,GAAG,IAAIL,QAAJ,CAAaC,WAAb,CAArB;;IACAI,YAAY,CAACC,mBAAb,CAAiC,IAAjC;;IACAP,qBAAqB,CAAC,aAAD,EAAgBM,YAAhB,CAArB,CAAmDE,MAAnD,CAA0DC,IAA1D,CAA+DC,KAA/D,CAAqE;MACpEC,cAAc,EAAE,YADoD;MAEpEC,WAAW,EAAEC;IAFuD,CAArE;EAIA,CAPC,CAAF;AAQA,CATO,CAAR"}