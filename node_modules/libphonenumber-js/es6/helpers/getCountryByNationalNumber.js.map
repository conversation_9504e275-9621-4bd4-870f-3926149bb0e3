{"version": 3, "file": "getCountryByNationalNumber.js", "names": ["<PERSON><PERSON><PERSON>", "getNumberType", "getCountryByNationalNumber", "nationalPhoneNumber", "countries", "defaultCountry", "metadata", "country", "leadingDigits", "search", "phone", "undefined"], "sources": ["../../source/helpers/getCountryByNationalNumber.js"], "sourcesContent": ["import Metadata from '../metadata.js'\r\nimport getNumberType from './getNumberType.js'\r\n\r\nexport default function getCountryByNationalNumber(nationalPhoneNumber, {\r\n\tcountries,\r\n\tdefaultCountry,\r\n\tmetadata\r\n}) {\r\n\t// Re-create `metadata` because it will be selecting a `country`.\r\n\tmetadata = new Metadata(metadata)\r\n\r\n\t// const matchingCountries = []\r\n\r\n\tfor (const country of countries) {\r\n\t\tmetadata.country(country)\r\n\t\t// \"Leading digits\" patterns are only defined for about 20% of all countries.\r\n\t\t// By definition, matching \"leading digits\" is a sufficient but not a necessary\r\n\t\t// condition for a phone number to belong to a country.\r\n\t\t// The point of \"leading digits\" check is that it's the fastest one to get a match.\r\n\t\t// https://gitlab.com/catamphetamine/libphonenumber-js/blob/master/METADATA.md#leading_digits\r\n\t\t// I'd suppose that \"leading digits\" patterns are mutually exclusive for different countries\r\n\t\t// because of the intended use of that feature.\r\n\t\tif (metadata.leadingDigits()) {\r\n\t\t\tif (nationalPhoneNumber &&\r\n\t\t\t\tnationalPhoneNumber.search(metadata.leadingDigits()) === 0) {\r\n\t\t\t\treturn country\r\n\t\t\t}\r\n\t\t}\r\n\t\t// Else perform full validation with all of those\r\n\t\t// fixed-line/mobile/etc regular expressions.\r\n\t\telse if (getNumberType({ phone: nationalPhoneNumber, country }, undefined, metadata.metadata)) {\r\n\t\t\t// If both the `defaultCountry` and the \"main\" one match the phone number,\r\n\t\t\t// don't prefer the `defaultCountry` over the \"main\" one.\r\n\t\t\t// https://gitlab.com/catamphetamine/libphonenumber-js/-/issues/154\r\n\t\t\treturn country\r\n\t\t\t// // If the `defaultCountry` is among the `matchingCountries` then return it.\r\n\t\t\t// if (defaultCountry) {\r\n\t\t\t// \tif (country === defaultCountry) {\r\n\t\t\t// \t\treturn country\r\n\t\t\t// \t}\r\n\t\t\t// \tmatchingCountries.push(country)\r\n\t\t\t// } else {\r\n\t\t\t// \treturn country\r\n\t\t\t// }\r\n\t\t}\r\n\t}\r\n\r\n\t// // Return the first (\"main\") one of the `matchingCountries`.\r\n\t// if (matchingCountries.length > 0) {\r\n\t// \treturn matchingCountries[0]\r\n\t// }\r\n}"], "mappings": ";;;;;;AAAA,OAAOA,QAAP,MAAqB,gBAArB;AACA,OAAOC,aAAP,MAA0B,oBAA1B;AAEA,eAAe,SAASC,0BAAT,CAAoCC,mBAApC,QAIZ;EAAA,IAHFC,SAGE,QAHFA,SAGE;EAAA,IAFFC,cAEE,QAFFA,cAEE;EAAA,IADFC,QACE,QADFA,QACE;EACF;EACAA,QAAQ,GAAG,IAAIN,QAAJ,CAAaM,QAAb,CAAX,CAFE,CAIF;;EAEA,qDAAsBF,SAAtB,wCAAiC;IAAA,IAAtBG,OAAsB;IAChCD,QAAQ,CAACC,OAAT,CAAiBA,OAAjB,EADgC,CAEhC;IACA;IACA;IACA;IACA;IACA;IACA;;IACA,IAAID,QAAQ,CAACE,aAAT,EAAJ,EAA8B;MAC7B,IAAIL,mBAAmB,IACtBA,mBAAmB,CAACM,MAApB,CAA2BH,QAAQ,CAACE,aAAT,EAA3B,MAAyD,CAD1D,EAC6D;QAC5D,OAAOD,OAAP;MACA;IACD,CALD,CAMA;IACA;IAPA,KAQK,IAAIN,aAAa,CAAC;MAAES,KAAK,EAAEP,mBAAT;MAA8BI,OAAO,EAAPA;IAA9B,CAAD,EAA0CI,SAA1C,EAAqDL,QAAQ,CAACA,QAA9D,CAAjB,EAA0F;MAC9F;MACA;MACA;MACA,OAAOC,OAAP,CAJ8F,CAK9F;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;IACA;EACD,CAtCC,CAwCF;EACA;EACA;EACA;;AACA"}