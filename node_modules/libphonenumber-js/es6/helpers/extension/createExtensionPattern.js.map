{"version": 3, "file": "createExtensionPattern.js", "names": ["VALID_DIGITS", "RFC3966_EXTN_PREFIX", "getExtensionDigitsPattern", "max<PERSON><PERSON><PERSON>", "createExtensionPattern", "purpose", "extLimitAfterExplicitLabel", "extLimitAfterLikelyLabel", "extLimitAfterAmbiguousChar", "extLimitWhenNotSure", "possibleSeparatorsBetweenNumberAndExtLabel", "possibleCharsAfterExtLabel", "optionalExtnSuffix", "explicitExtLabels", "ambiguousExtLabels", "ambiguous<PERSON><PERSON><PERSON><PERSON>", "possibleSeparatorsNumberExtLabelNoComma", "autoDiallingAndExtLabelsFound", "rfcExtn", "explicitExtn", "ambiguousExtn", "americanStyleExtnWithSuffix", "autoDiallingExtn", "onlyCommasExtn"], "sources": ["../../../source/helpers/extension/createExtensionPattern.js"], "sourcesContent": ["import { VALID_DIGITS } from '../../constants.js'\r\n\r\n// The RFC 3966 format for extensions.\r\nconst RFC3966_EXTN_PREFIX = ';ext='\r\n\r\n/**\r\n * Helper method for constructing regular expressions for parsing. Creates\r\n * an expression that captures up to max_length digits.\r\n * @return {string} RegEx pattern to capture extension digits.\r\n */\r\nconst getExtensionDigitsPattern = (maxLength) => `([${VALID_DIGITS}]{1,${maxLength}})`\r\n\r\n/**\r\n * Helper initialiser method to create the regular-expression pattern to match\r\n * extensions.\r\n * Copy-pasted from Google's `libphonenumber`:\r\n * https://github.com/google/libphonenumber/blob/55b2646ec9393f4d3d6661b9c82ef9e258e8b829/javascript/i18n/phonenumbers/phonenumberutil.js#L759-L766\r\n * @return {string} RegEx pattern to capture extensions.\r\n */\r\nexport default function createExtensionPattern(purpose) {\r\n\t// We cap the maximum length of an extension based on the ambiguity of the way\r\n\t// the extension is prefixed. As per ITU, the officially allowed length for\r\n\t// extensions is actually 40, but we don't support this since we haven't seen real\r\n\t// examples and this introduces many false interpretations as the extension labels\r\n\t// are not standardized.\r\n\t/** @type {string} */\r\n\tvar extLimitAfterExplicitLabel = '20';\r\n\t/** @type {string} */\r\n\tvar extLimitAfterLikelyLabel = '15';\r\n\t/** @type {string} */\r\n\tvar extLimitAfterAmbiguousChar = '9';\r\n\t/** @type {string} */\r\n\tvar extLimitWhenNotSure = '6';\r\n\r\n\t/** @type {string} */\r\n\tvar possibleSeparatorsBetweenNumberAndExtLabel = \"[ \\u00A0\\\\t,]*\";\r\n\t// Optional full stop (.) or colon, followed by zero or more spaces/tabs/commas.\r\n\t/** @type {string} */\r\n\tvar possibleCharsAfterExtLabel = \"[:\\\\.\\uFF0E]?[ \\u00A0\\\\t,-]*\";\r\n\t/** @type {string} */\r\n\tvar optionalExtnSuffix = \"#?\";\r\n\r\n\t// Here the extension is called out in more explicit way, i.e mentioning it obvious\r\n\t// patterns like \"ext.\".\r\n\t/** @type {string} */\r\n\tvar explicitExtLabels =\r\n\t  \"(?:e?xt(?:ensi(?:o\\u0301?|\\u00F3))?n?|\\uFF45?\\uFF58\\uFF54\\uFF4E?|\\u0434\\u043E\\u0431|anexo)\";\r\n\t// One-character symbols that can be used to indicate an extension, and less\r\n\t// commonly used or more ambiguous extension labels.\r\n\t/** @type {string} */\r\n\tvar ambiguousExtLabels = \"(?:[x\\uFF58#\\uFF03~\\uFF5E]|int|\\uFF49\\uFF4E\\uFF54)\";\r\n\t// When extension is not separated clearly.\r\n\t/** @type {string} */\r\n\tvar ambiguousSeparator = \"[- ]+\";\r\n\t// This is the same as possibleSeparatorsBetweenNumberAndExtLabel, but not matching\r\n\t// comma as extension label may have it.\r\n\t/** @type {string} */\r\n\tvar possibleSeparatorsNumberExtLabelNoComma = \"[ \\u00A0\\\\t]*\";\r\n\t// \",,\" is commonly used for auto dialling the extension when connected. First\r\n\t// comma is matched through possibleSeparatorsBetweenNumberAndExtLabel, so we do\r\n\t// not repeat it here. Semi-colon works in Iphone and Android also to pop up a\r\n\t// button with the extension number following.\r\n\t/** @type {string} */\r\n\tvar autoDiallingAndExtLabelsFound = \"(?:,{2}|;)\";\r\n\r\n\t/** @type {string} */\r\n\tvar rfcExtn = RFC3966_EXTN_PREFIX\r\n\t     + getExtensionDigitsPattern(extLimitAfterExplicitLabel);\r\n\t/** @type {string} */\r\n\tvar explicitExtn = possibleSeparatorsBetweenNumberAndExtLabel + explicitExtLabels\r\n\t     + possibleCharsAfterExtLabel\r\n\t     + getExtensionDigitsPattern(extLimitAfterExplicitLabel)\r\n\t     + optionalExtnSuffix;\r\n\t/** @type {string} */\r\n\tvar ambiguousExtn = possibleSeparatorsBetweenNumberAndExtLabel + ambiguousExtLabels\r\n\t     + possibleCharsAfterExtLabel\r\n\t+ getExtensionDigitsPattern(extLimitAfterAmbiguousChar)\r\n\t+ optionalExtnSuffix;\r\n\t/** @type {string} */\r\n\tvar americanStyleExtnWithSuffix = ambiguousSeparator\r\n\t+ getExtensionDigitsPattern(extLimitWhenNotSure) + \"#\";\r\n\r\n\t/** @type {string} */\r\n\tvar autoDiallingExtn = possibleSeparatorsNumberExtLabelNoComma\r\n\t     + autoDiallingAndExtLabelsFound + possibleCharsAfterExtLabel\r\n\t     + getExtensionDigitsPattern(extLimitAfterLikelyLabel)\r\n\t+ optionalExtnSuffix;\r\n\t/** @type {string} */\r\n\tvar onlyCommasExtn = possibleSeparatorsNumberExtLabelNoComma\r\n\t    + \"(?:,)+\" + possibleCharsAfterExtLabel\r\n\t    + getExtensionDigitsPattern(extLimitAfterAmbiguousChar)\r\n\t    + optionalExtnSuffix;\r\n\r\n\t// The first regular expression covers RFC 3966 format, where the extension is added\r\n\t// using \";ext=\". The second more generic where extension is mentioned with explicit\r\n\t// labels like \"ext:\". In both the above cases we allow more numbers in extension than\r\n\t// any other extension labels. The third one captures when single character extension\r\n\t// labels or less commonly used labels are used. In such cases we capture fewer\r\n\t// extension digits in order to reduce the chance of falsely interpreting two\r\n\t// numbers beside each other as a number + extension. The fourth one covers the\r\n\t// special case of American numbers where the extension is written with a hash\r\n\t// at the end, such as \"- 503#\". The fifth one is exclusively for extension\r\n\t// autodialling formats which are used when dialling and in this case we accept longer\r\n\t// extensions. The last one is more liberal on the number of commas that acts as\r\n\t// extension labels, so we have a strict cap on the number of digits in such extensions.\r\n\treturn rfcExtn + \"|\"\r\n\t       + explicitExtn + \"|\"\r\n\t       + ambiguousExtn + \"|\"\r\n\t       + americanStyleExtnWithSuffix + \"|\"\r\n\t       + autoDiallingExtn + \"|\"\r\n\t       + onlyCommasExtn;\r\n}"], "mappings": "AAAA,SAASA,YAAT,QAA6B,oBAA7B,C,CAEA;;AACA,IAAMC,mBAAmB,GAAG,OAA5B;AAEA;AACA;AACA;AACA;AACA;;AACA,IAAMC,yBAAyB,GAAG,SAA5BA,yBAA4B,CAACC,SAAD;EAAA,mBAAoBH,YAApB,iBAAuCG,SAAvC;AAAA,CAAlC;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACA,eAAe,SAASC,sBAAT,CAAgCC,OAAhC,EAAyC;EACvD;EACA;EACA;EACA;EACA;;EACA;EACA,IAAIC,0BAA0B,GAAG,IAAjC;EACA;;EACA,IAAIC,wBAAwB,GAAG,IAA/B;EACA;;EACA,IAAIC,0BAA0B,GAAG,GAAjC;EACA;;EACA,IAAIC,mBAAmB,GAAG,GAA1B;EAEA;;EACA,IAAIC,0CAA0C,GAAG,cAAjD,CAhBuD,CAiBvD;;EACA;;EACA,IAAIC,0BAA0B,GAAG,4BAAjC;EACA;;EACA,IAAIC,kBAAkB,GAAG,IAAzB,CArBuD,CAuBvD;EACA;;EACA;;EACA,IAAIC,iBAAiB,GACnB,0FADF,CA1BuD,CA4BvD;EACA;;EACA;;EACA,IAAIC,kBAAkB,GAAG,oDAAzB,CA/BuD,CAgCvD;;EACA;;EACA,IAAIC,kBAAkB,GAAG,OAAzB,CAlCuD,CAmCvD;EACA;;EACA;;EACA,IAAIC,uCAAuC,GAAG,aAA9C,CAtCuD,CAuCvD;EACA;EACA;EACA;;EACA;;EACA,IAAIC,6BAA6B,GAAG,YAApC;EAEA;;EACA,IAAIC,OAAO,GAAGjB,mBAAmB,GAC1BC,yBAAyB,CAACI,0BAAD,CADhC;EAEA;;EACA,IAAIa,YAAY,GAAGT,0CAA0C,GAAGG,iBAA7C,GACZF,0BADY,GAEZT,yBAAyB,CAACI,0BAAD,CAFb,GAGZM,kBAHP;EAIA;;EACA,IAAIQ,aAAa,GAAGV,0CAA0C,GAAGI,kBAA7C,GACbH,0BADa,GAElBT,yBAAyB,CAACM,0BAAD,CAFP,GAGlBI,kBAHF;EAIA;;EACA,IAAIS,2BAA2B,GAAGN,kBAAkB,GAClDb,yBAAyB,CAACO,mBAAD,CADO,GACiB,GADnD;EAGA;;EACA,IAAIa,gBAAgB,GAAGN,uCAAuC,GACvDC,6BADgB,GACgBN,0BADhB,GAEhBT,yBAAyB,CAACK,wBAAD,CAFT,GAGrBK,kBAHF;EAIA;;EACA,IAAIW,cAAc,GAAGP,uCAAuC,GACtD,QADe,GACJL,0BADI,GAEfT,yBAAyB,CAACM,0BAAD,CAFV,GAGfI,kBAHN,CArEuD,CA0EvD;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;EACA,OAAOM,OAAO,GAAG,GAAV,GACEC,YADF,GACiB,GADjB,GAEEC,aAFF,GAEkB,GAFlB,GAGEC,2BAHF,GAGgC,GAHhC,GAIEC,gBAJF,GAIqB,GAJrB,GAKEC,cALT;AAMA"}