{"version": 3, "file": "stripIddPrefix.test.js", "names": ["stripIddPrefix", "metadata", "type", "describe", "it", "should", "equal", "expect", "to", "be", "undefined"], "sources": ["../../source/helpers/stripIddPrefix.test.js"], "sourcesContent": ["import stripIddPrefix from './stripIddPrefix.js'\r\n\r\nimport metadata from '../../metadata.min.json' assert { type: 'json' }\r\n\r\ndescribe('stripIddPrefix', () => {\r\n\tit('should strip a valid IDD prefix', () => {\r\n\t\tstripIddPrefix('01178005553535', 'US', '1', metadata).should.equal('78005553535')\r\n\t})\r\n\r\n\tit('should strip a valid IDD prefix (no country calling code)', () => {\r\n\t\tstripIddPrefix('011', 'US', '1', metadata).should.equal('')\r\n\t})\r\n\r\n\tit('should strip a valid IDD prefix (valid country calling code)', () => {\r\n\t\tstripIddPrefix('0117', 'US', '1', metadata).should.equal('7')\r\n\t})\r\n\r\n\tit('should strip a valid IDD prefix (not a valid country calling code)', () => {\r\n\t\texpect(stripIddPrefix('0110', 'US', '1', metadata)).to.be.undefined\r\n\t})\r\n})"], "mappings": "AAAA,OAAOA,cAAP,MAA2B,qBAA3B;AAEA,OAAOC,QAAP,MAAqB,yBAArB,UAAwDC,IAAI,EAAE,MAA9D;AAEAC,QAAQ,CAAC,gBAAD,EAAmB,YAAM;EAChCC,EAAE,CAAC,iCAAD,EAAoC,YAAM;IAC3CJ,cAAc,CAAC,gBAAD,EAAmB,IAAnB,EAAyB,GAAzB,EAA8BC,QAA9B,CAAd,CAAsDI,MAAtD,CAA6DC,KAA7D,CAAmE,aAAnE;EACA,CAFC,CAAF;EAIAF,EAAE,CAAC,2DAAD,EAA8D,YAAM;IACrEJ,cAAc,CAAC,KAAD,EAAQ,IAAR,EAAc,GAAd,EAAmBC,QAAnB,CAAd,CAA2CI,MAA3C,CAAkDC,KAAlD,CAAwD,EAAxD;EACA,CAFC,CAAF;EAIAF,EAAE,CAAC,8DAAD,EAAiE,YAAM;IACxEJ,cAAc,CAAC,MAAD,EAAS,IAAT,EAAe,GAAf,EAAoBC,QAApB,CAAd,CAA4CI,MAA5C,CAAmDC,KAAnD,CAAyD,GAAzD;EACA,CAFC,CAAF;EAIAF,EAAE,CAAC,oEAAD,EAAuE,YAAM;IAC9EG,MAAM,CAACP,cAAc,CAAC,MAAD,EAAS,IAAT,EAAe,GAAf,EAAoBC,QAApB,CAAf,CAAN,CAAoDO,EAApD,CAAuDC,EAAvD,CAA0DC,SAA1D;EACA,CAFC,CAAF;AAGA,CAhBO,CAAR"}