{"version": 3, "file": "parseDigits.test.js", "names": ["parseDigits", "describe", "it", "should", "equal"], "sources": ["../../source/helpers/parseDigits.test.js"], "sourcesContent": ["import parseDigits from './parseDigits.js'\r\n\r\ndescribe('parseDigits', () => {\r\n\tit('should parse digits', () => {\r\n\t\tparseDigits('+٤٤٢٣٢٣٢٣٤').should.equal('442323234')\r\n\t})\r\n})"], "mappings": "AAAA,OAAOA,WAAP,MAAwB,kBAAxB;AAEAC,QAAQ,CAAC,aAAD,EAAgB,YAAM;EAC7BC,EAAE,CAAC,qBAAD,EAAwB,YAAM;IAC/BF,WAAW,CAAC,YAAD,CAAX,CAA0BG,MAA1B,CAAiCC,KAAjC,CAAuC,WAAvC;EACA,CAFC,CAAF;AAGA,CAJO,CAAR"}