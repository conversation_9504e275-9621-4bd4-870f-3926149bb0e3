{"version": 3, "file": "findNumbers.js", "names": ["PhoneNumberMatcher", "normalizeArguments", "findNumbers", "arguments", "text", "options", "metadata", "matcher", "results", "hasNext", "push", "next"], "sources": ["../../source/legacy/findNumbers.js"], "sourcesContent": ["import PhoneNumberMatcher from '../PhoneNumberMatcher.js'\r\nimport normalizeArguments from '../normalizeArguments.js'\r\n\r\nexport default function findNumbers() {\r\n\tconst { text, options, metadata } = normalizeArguments(arguments)\r\n\tconst matcher = new PhoneNumberMatcher(text, options, metadata)\r\n\tconst results = []\r\n\twhile (matcher.hasNext()) {\r\n\t\tresults.push(matcher.next())\r\n\t}\r\n\treturn results\r\n}"], "mappings": "AAAA,OAAOA,kBAAP,MAA+B,0BAA/B;AACA,OAAOC,kBAAP,MAA+B,0BAA/B;AAEA,eAAe,SAASC,WAAT,GAAuB;EACrC,0BAAoCD,kBAAkB,CAACE,SAAD,CAAtD;EAAA,IAAQC,IAAR,uBAAQA,IAAR;EAAA,IAAcC,OAAd,uBAAcA,OAAd;EAAA,IAAuBC,QAAvB,uBAAuBA,QAAvB;;EACA,IAAMC,OAAO,GAAG,IAAIP,kBAAJ,CAAuBI,IAAvB,EAA6BC,OAA7B,EAAsCC,QAAtC,CAAhB;EACA,IAAME,OAAO,GAAG,EAAhB;;EACA,OAAOD,OAAO,CAACE,OAAR,EAAP,EAA0B;IACzBD,OAAO,CAACE,IAAR,CAAaH,OAAO,CAACI,IAAR,EAAb;EACA;;EACD,OAAOH,OAAP;AACA"}