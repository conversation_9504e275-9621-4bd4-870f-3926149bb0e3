{"version": 3, "file": "isValidNumberForRegion.js", "names": ["isViablePhoneNumber", "parseNumber", "_isValidNumberForRegion", "isValidNumberForRegion", "number", "country", "metadata", "TypeError", "input", "defaultCountry", "undefined"], "sources": ["../../source/legacy/isValidNumberForRegion.js"], "sourcesContent": ["import isViablePhoneNumber from '../helpers/isViablePhoneNumber.js'\r\nimport parseNumber from '../parse.js'\r\nimport _isValidNumberForRegion from './isValidNumberForRegion_.js'\r\n\r\n// This function has been deprecated and is not exported as\r\n// `isValidPhoneNumberForCountry()` or `isValidPhoneNumberForRegion()`.\r\n//\r\n// The rationale is:\r\n//\r\n// * We don't use the \"region\" word, so \"country\" would be better.\r\n//\r\n// * It could be substituted with:\r\n//\r\n// ```js\r\n// export default function isValidPhoneNumberForCountry(phoneNumberString, country) {\r\n// \tconst phoneNumber = parsePhoneNumber(phoneNumberString, {\r\n// \t\tdefaultCountry: country,\r\n// \t\t// Demand that the entire input string must be a phone number.\r\n// \t\t// Otherwise, it would \"extract\" a phone number from an input string.\r\n// \t\textract: false\r\n// \t})\r\n// \tif (!phoneNumber) {\r\n// \t\treturn false\r\n// \t}\r\n// \tif (phoneNumber.country !== country) {\r\n// \t\treturn false\r\n// \t}\r\n// \treturn phoneNumber.isValid()\r\n// }\r\n// ```\r\n//\r\n// * Same function could be used for `isPossiblePhoneNumberForCountry()`\r\n//   by replacing `isValid()` with `isPossible()`.\r\n//\r\n// * The reason why this function is not exported is because its result is ambiguous.\r\n//   Suppose `false` is returned. It could mean any of:\r\n//   * Not a phone number.\r\n//   * The phone number is valid but belongs to another country or another calling code.\r\n//   * The phone number belongs to the correct country but is not valid digit-wise.\r\n//   All those three cases should be handled separately from a \"User Experience\" standpoint.\r\n//   Simply showing \"Invalid phone number\" error in all of those cases would be lazy UX.\r\n\r\nexport default function isValidNumberForRegion(number, country, metadata) {\r\n\tif (typeof number !== 'string') {\r\n\t\tthrow new TypeError('number must be a string')\r\n\t}\r\n\tif (typeof country !== 'string') {\r\n\t\tthrow new TypeError('country must be a string')\r\n\t}\r\n\t// `parse` extracts phone numbers from raw text,\r\n\t// therefore it will cut off all \"garbage\" characters,\r\n\t// while this `validate` function needs to verify\r\n\t// that the phone number contains no \"garbage\"\r\n\t// therefore the explicit `isViablePhoneNumber` check.\r\n\tlet input\r\n\tif (isViablePhoneNumber(number)) {\r\n\t\tinput = parseNumber(number, { defaultCountry: country }, metadata)\r\n\t} else {\r\n\t\tinput = {}\r\n\t}\r\n\treturn _isValidNumberForRegion(input, country, undefined, metadata)\r\n}"], "mappings": "AAAA,OAAOA,mBAAP,MAAgC,mCAAhC;AACA,OAAOC,WAAP,MAAwB,aAAxB;AACA,OAAOC,uBAAP,MAAoC,8BAApC,C,CAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,eAAe,SAASC,sBAAT,CAAgCC,MAAhC,EAAwCC,OAAxC,EAAiDC,QAAjD,EAA2D;EACzE,IAAI,OAAOF,MAAP,KAAkB,QAAtB,EAAgC;IAC/B,MAAM,IAAIG,SAAJ,CAAc,yBAAd,CAAN;EACA;;EACD,IAAI,OAAOF,OAAP,KAAmB,QAAvB,EAAiC;IAChC,MAAM,IAAIE,SAAJ,CAAc,0BAAd,CAAN;EACA,CANwE,CAOzE;EACA;EACA;EACA;EACA;;;EACA,IAAIC,KAAJ;;EACA,IAAIR,mBAAmB,CAACI,MAAD,CAAvB,EAAiC;IAChCI,KAAK,GAAGP,WAAW,CAACG,MAAD,EAAS;MAAEK,cAAc,EAAEJ;IAAlB,CAAT,EAAsCC,QAAtC,CAAnB;EACA,CAFD,MAEO;IACNE,KAAK,GAAG,EAAR;EACA;;EACD,OAAON,uBAAuB,CAACM,KAAD,EAAQH,OAAR,EAAiBK,SAAjB,EAA4BJ,QAA5B,CAA9B;AACA"}