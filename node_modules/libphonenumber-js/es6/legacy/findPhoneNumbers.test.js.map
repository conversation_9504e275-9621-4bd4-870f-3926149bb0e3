{"version": 3, "file": "findPhoneNumbers.test.js", "names": ["findNumbers", "searchPhoneNumbers", "PhoneNumberSearch", "metadata", "type", "describe", "it", "should", "deep", "equal", "phone", "country", "startsAt", "endsAt", "leniency", "ext", "expected_numbers", "number", "shift", "length", "thrower", "possibleNumbers", "extended", "finder", "defaultCountry", "hasNext", "next", "undefined", "search"], "sources": ["../../source/legacy/findPhoneNumbers.test.js"], "sourcesContent": ["// This is a legacy function.\r\n// Use `findNumbers()` instead.\r\n\r\nimport findNumbers, { searchPhoneNumbers } from './findPhoneNumbers.js'\r\nimport { PhoneNumberSearch } from './findPhoneNumbersInitialImplementation.js'\r\nimport metadata from '../../metadata.min.json' assert { type: 'json' }\r\n\r\ndescribe('findPhoneNumbers', () => {\r\n\tit('should find numbers', () => {\r\n\t\tfindNumbers('2133734253', 'US', metadata).should.deep.equal([{\r\n\t\t\tphone    : '2133734253',\r\n\t\t\tcountry  : 'US',\r\n\t\t\tstartsAt : 0,\r\n\t\t\tendsAt   : 10\r\n\t\t}])\r\n\r\n\t\tfindNumbers('(*************', 'US', metadata).should.deep.equal([{\r\n\t\t\tphone    : '2133734253',\r\n\t\t\tcountry  : 'US',\r\n\t\t\tstartsAt : 0,\r\n\t\t\tendsAt   : 14\r\n\t\t}])\r\n\r\n\t\tfindNumbers('The number is +7 (800) 555-35-35 and not (************* as written in the document.', 'US', metadata).should.deep.equal([{\r\n\t\t\tphone    : '8005553535',\r\n\t\t\tcountry  : 'RU',\r\n\t\t\tstartsAt : 14,\r\n\t\t\tendsAt   : 32\r\n\t\t}, {\r\n\t\t\tphone    : '2133734253',\r\n\t\t\tcountry  : 'US',\r\n\t\t\tstartsAt : 41,\r\n\t\t\tendsAt   : 55\r\n\t\t}])\r\n\r\n\t\t// Opening parenthesis issue.\r\n\t\t// https://github.com/catamphetamine/libphonenumber-js/issues/252\r\n\t\tfindNumbers('The number is +7 (800) 555-35-35 and not (************* (that\\'s not even in the same country!) as written in the document.', 'US', metadata).should.deep.equal([{\r\n\t\t\tphone    : '8005553535',\r\n\t\t\tcountry  : 'RU',\r\n\t\t\tstartsAt : 14,\r\n\t\t\tendsAt   : 32\r\n\t\t}, {\r\n\t\t\tphone    : '2133734253',\r\n\t\t\tcountry  : 'US',\r\n\t\t\tstartsAt : 41,\r\n\t\t\tendsAt   : 55\r\n\t\t}])\r\n\r\n\t\t// No default country.\r\n\t\tfindNumbers('The number is +7 (800) 555-35-35 as written in the document.', metadata).should.deep.equal([{\r\n\t\t\tphone    : '8005553535',\r\n\t\t\tcountry  : 'RU',\r\n\t\t\tstartsAt : 14,\r\n\t\t\tendsAt   : 32\r\n\t\t}])\r\n\r\n\t\t// Passing `options` and default country.\r\n\t\tfindNumbers('The number is +7 (800) 555-35-35 as written in the document.', 'US', { leniency: 'VALID' }, metadata).should.deep.equal([{\r\n\t\t\tphone    : '8005553535',\r\n\t\t\tcountry  : 'RU',\r\n\t\t\tstartsAt : 14,\r\n\t\t\tendsAt   : 32\r\n\t\t}])\r\n\r\n\t\t// Passing `options`.\r\n\t\tfindNumbers('The number is +7 (800) 555-35-35 as written in the document.', { leniency: 'VALID' }, metadata).should.deep.equal([{\r\n\t\t\tphone    : '8005553535',\r\n\t\t\tcountry  : 'RU',\r\n\t\t\tstartsAt : 14,\r\n\t\t\tendsAt   : 32\r\n\t\t}])\r\n\r\n\t\t// Not a phone number and a phone number.\r\n\t\tfindNumbers('Digits 12 are not a number, but +7 (800) 555-35-35 is.', { leniency: 'VALID' }, metadata).should.deep.equal([{\r\n\t\t\tphone    : '8005553535',\r\n\t\t\tcountry  : 'RU',\r\n\t\t\tstartsAt : 32,\r\n\t\t\tendsAt   : 50\r\n\t\t}])\r\n\r\n\t\t// Phone number extension.\r\n\t\tfindNumbers('Date 02/17/2018 is not a number, but +7 (800) 555-35-35 ext. 123 is.', { leniency: 'VALID' }, metadata).should.deep.equal([{\r\n\t\t\tphone    : '8005553535',\r\n\t\t\tcountry  : 'RU',\r\n\t\t\text      : '123',\r\n\t\t\tstartsAt : 37,\r\n\t\t\tendsAt   : 64\r\n\t\t}])\r\n\t})\r\n\r\n\tit('shouldn\\'t find non-valid numbers', () => {\r\n\t\t// Not a valid phone number for US.\r\n\t\tfindNumbers('1111111111', 'US', metadata).should.deep.equal([])\r\n\t})\r\n\r\n\tit('should find non-European digits', () => {\r\n\t\t// E.g. in Iraq they don't write `+442323234` but rather `+٤٤٢٣٢٣٢٣٤`.\r\n\t\tfindNumbers('العَرَبِيَّة‎ +٤٤٣٣٣٣٣٣٣٣٣٣عَرَبِيّ‎', metadata).should.deep.equal([{\r\n\t\t\tcountry  : 'GB',\r\n\t\t\tphone    : '3333333333',\r\n\t\t\tstartsAt : 14,\r\n\t\t\tendsAt   : 27\r\n\t\t}])\r\n\t})\r\n\r\n\tit('should iterate', () => {\r\n\t\tconst expected_numbers = [{\r\n\t\t\tcountry : 'RU',\r\n\t\t\tphone   : '8005553535',\r\n\t\t\t// number   : '+7 (800) 555-35-35',\r\n\t\t\tstartsAt : 14,\r\n\t\t\tendsAt   : 32\r\n\t\t}, {\r\n\t\t\tcountry : 'US',\r\n\t\t\tphone   : '2133734253',\r\n\t\t\t// number   : '(*************',\r\n\t\t\tstartsAt : 41,\r\n\t\t\tendsAt   : 55\r\n\t\t}]\r\n\r\n\t\tfor (const number of searchPhoneNumbers('The number is +7 (800) 555-35-35 and not (************* as written in the document.', 'US', metadata)) {\r\n\t\t\tnumber.should.deep.equal(expected_numbers.shift())\r\n\t\t}\r\n\r\n\t\texpected_numbers.length.should.equal(0)\r\n\t})\r\n\r\n\tit('should work in edge cases', () => {\r\n\t\tlet thrower\r\n\r\n\t\t// No input\r\n\t\tfindNumbers('', metadata).should.deep.equal([])\r\n\r\n\t\t// No country metadata for this `require` country code\r\n\t\tthrower = () => findNumbers('123', 'ZZ', metadata)\r\n\t\tthrower.should.throw('Unknown country')\r\n\r\n\t\t// Numerical `value`\r\n\t\tthrower = () => findNumbers(2141111111, 'US')\r\n\t\tthrower.should.throw('A text for parsing must be a string.')\r\n\r\n\t\t// // No metadata\r\n\t\t// thrower = () => findNumbers('')\r\n\t\t// thrower.should.throw('`metadata` argument not passed')\r\n\t})\r\n\r\n\tit('shouldn\\'t find phone numbers which are not phone numbers', () => {\r\n\t\t// A timestamp.\r\n\t\tfindNumbers('2012-01-02 08:00', 'US', metadata).should.deep.equal([])\r\n\r\n\t\t// A valid number (not a complete timestamp).\r\n\t\tfindNumbers('2012-01-02 08', 'US', metadata).should.deep.equal([{\r\n\t\t\tcountry  : 'US',\r\n\t\t\tphone    : '2012010208',\r\n\t\t\tstartsAt : 0,\r\n\t\t\tendsAt   : 13\r\n\t\t}])\r\n\r\n\t\t// Invalid parens.\r\n\t\tfindNumbers('213(3734253', 'US', metadata).should.deep.equal([])\r\n\r\n\t\t// Letters after phone number.\r\n\t\tfindNumbers('2133734253a', 'US', metadata).should.deep.equal([])\r\n\r\n\t\t// Valid phone (same as the one found in the UUID below).\r\n\t\tfindNumbers('The phone number is 231354125.', 'FR', metadata).should.deep.equal([{\r\n\t\t\tcountry  : 'FR',\r\n\t\t\tphone    : '231354125',\r\n\t\t\tstartsAt : 20,\r\n\t\t\tendsAt   : 29\r\n\t\t}])\r\n\r\n\t\t// Not a phone number (part of a UUID).\r\n\t\t// Should parse in `{ extended: true }` mode.\r\n\t\tconst possibleNumbers = findNumbers('The UUID is CA801c26f98cd16e231354125ad046e40b.', 'FR', { extended: true }, metadata)\r\n\t\tpossibleNumbers.length.should.equal(3)\r\n\t\tpossibleNumbers[1].country.should.equal('FR')\r\n\t\tpossibleNumbers[1].phone.should.equal('231354125')\r\n\r\n\t\t// Not a phone number (part of a UUID).\r\n\t\t// Shouldn't parse by default.\r\n\t\tfindNumbers('The UUID is CA801c26f98cd16e231354125ad046e40b.', 'FR', metadata).should.deep.equal([])\r\n\t})\r\n})\r\n\r\ndescribe('PhoneNumberSearch', () => {\r\n\tit('should search for phone numbers', () => {\r\n\t\tconst finder = new PhoneNumberSearch('The number is +7 (800) 555-35-35 and not (************* as written in the document.', { defaultCountry: 'US' }, metadata)\r\n\r\n\t\tfinder.hasNext().should.equal(true)\r\n\t\tfinder.next().should.deep.equal({\r\n\t\t\tcountry : 'RU',\r\n\t\t\tphone   : '8005553535',\r\n\t\t\t// number   : '+7 (800) 555-35-35',\r\n\t\t\tstartsAt : 14,\r\n\t\t\tendsAt   : 32\r\n\t\t})\r\n\r\n\t\tfinder.hasNext().should.equal(true)\r\n\t\tfinder.next().should.deep.equal({\r\n\t\t\tcountry : 'US',\r\n\t\t\tphone   : '2133734253',\r\n\t\t\t// number   : '(*************',\r\n\t\t\tstartsAt : 41,\r\n\t\t\tendsAt   : 55\r\n\t\t})\r\n\r\n\t\tfinder.hasNext().should.equal(false)\r\n\t})\r\n\r\n\tit('should search for phone numbers (no options)', () => {\r\n\t\tconst finder = new PhoneNumberSearch('The number is +7 (800) 555-35-35', undefined, metadata)\r\n\t\tfinder.hasNext().should.equal(true)\r\n\t\tfinder.next().should.deep.equal({\r\n\t\t\tcountry : 'RU',\r\n\t\t\tphone   : '8005553535',\r\n\t\t\t// number   : '+7 (800) 555-35-35',\r\n\t\t\tstartsAt : 14,\r\n\t\t\tendsAt   : 32\r\n\t\t})\r\n\t\tfinder.hasNext().should.equal(false)\r\n\t})\r\n\r\n\tit('should work in edge cases', () => {\r\n\t\t// No options\r\n\t\tconst search = new PhoneNumberSearch('', undefined, metadata)\r\n\r\n\t\t// No next element\r\n\t\tlet thrower = () => search.next()\r\n\t\tthrower.should.throw('No next element')\r\n\t})\r\n})"], "mappings": ";;;;;;AAAA;AACA;AAEA,OAAOA,WAAP,IAAsBC,kBAAtB,QAAgD,uBAAhD;AACA,SAASC,iBAAT,QAAkC,4CAAlC;AACA,OAAOC,QAAP,MAAqB,yBAArB,UAAwDC,IAAI,EAAE,MAA9D;AAEAC,QAAQ,CAAC,kBAAD,EAAqB,YAAM;EAClCC,EAAE,CAAC,qBAAD,EAAwB,YAAM;IAC/BN,WAAW,CAAC,YAAD,EAAe,IAAf,EAAqBG,QAArB,CAAX,CAA0CI,MAA1C,CAAiDC,IAAjD,CAAsDC,KAAtD,CAA4D,CAAC;MAC5DC,KAAK,EAAM,YADiD;MAE5DC,OAAO,EAAI,IAFiD;MAG5DC,QAAQ,EAAG,CAHiD;MAI5DC,MAAM,EAAK;IAJiD,CAAD,CAA5D;IAOAb,WAAW,CAAC,gBAAD,EAAmB,IAAnB,EAAyBG,QAAzB,CAAX,CAA8CI,MAA9C,CAAqDC,IAArD,CAA0DC,KAA1D,CAAgE,CAAC;MAChEC,KAAK,EAAM,YADqD;MAEhEC,OAAO,EAAI,IAFqD;MAGhEC,QAAQ,EAAG,CAHqD;MAIhEC,MAAM,EAAK;IAJqD,CAAD,CAAhE;IAOAb,WAAW,CAAC,qFAAD,EAAwF,IAAxF,EAA8FG,QAA9F,CAAX,CAAmHI,MAAnH,CAA0HC,IAA1H,CAA+HC,KAA/H,CAAqI,CAAC;MACrIC,KAAK,EAAM,YAD0H;MAErIC,OAAO,EAAI,IAF0H;MAGrIC,QAAQ,EAAG,EAH0H;MAIrIC,MAAM,EAAK;IAJ0H,CAAD,EAKlI;MACFH,KAAK,EAAM,YADT;MAEFC,OAAO,EAAI,IAFT;MAGFC,QAAQ,EAAG,EAHT;MAIFC,MAAM,EAAK;IAJT,CALkI,CAArI,EAf+B,CA2B/B;IACA;;IACAb,WAAW,CAAC,6HAAD,EAAgI,IAAhI,EAAsIG,QAAtI,CAAX,CAA2JI,MAA3J,CAAkKC,IAAlK,CAAuKC,KAAvK,CAA6K,CAAC;MAC7KC,KAAK,EAAM,YADkK;MAE7KC,OAAO,EAAI,IAFkK;MAG7KC,QAAQ,EAAG,EAHkK;MAI7KC,MAAM,EAAK;IAJkK,CAAD,EAK1K;MACFH,KAAK,EAAM,YADT;MAEFC,OAAO,EAAI,IAFT;MAGFC,QAAQ,EAAG,EAHT;MAIFC,MAAM,EAAK;IAJT,CAL0K,CAA7K,EA7B+B,CAyC/B;;IACAb,WAAW,CAAC,8DAAD,EAAiEG,QAAjE,CAAX,CAAsFI,MAAtF,CAA6FC,IAA7F,CAAkGC,KAAlG,CAAwG,CAAC;MACxGC,KAAK,EAAM,YAD6F;MAExGC,OAAO,EAAI,IAF6F;MAGxGC,QAAQ,EAAG,EAH6F;MAIxGC,MAAM,EAAK;IAJ6F,CAAD,CAAxG,EA1C+B,CAiD/B;;IACAb,WAAW,CAAC,8DAAD,EAAiE,IAAjE,EAAuE;MAAEc,QAAQ,EAAE;IAAZ,CAAvE,EAA8FX,QAA9F,CAAX,CAAmHI,MAAnH,CAA0HC,IAA1H,CAA+HC,KAA/H,CAAqI,CAAC;MACrIC,KAAK,EAAM,YAD0H;MAErIC,OAAO,EAAI,IAF0H;MAGrIC,QAAQ,EAAG,EAH0H;MAIrIC,MAAM,EAAK;IAJ0H,CAAD,CAArI,EAlD+B,CAyD/B;;IACAb,WAAW,CAAC,8DAAD,EAAiE;MAAEc,QAAQ,EAAE;IAAZ,CAAjE,EAAwFX,QAAxF,CAAX,CAA6GI,MAA7G,CAAoHC,IAApH,CAAyHC,KAAzH,CAA+H,CAAC;MAC/HC,KAAK,EAAM,YADoH;MAE/HC,OAAO,EAAI,IAFoH;MAG/HC,QAAQ,EAAG,EAHoH;MAI/HC,MAAM,EAAK;IAJoH,CAAD,CAA/H,EA1D+B,CAiE/B;;IACAb,WAAW,CAAC,wDAAD,EAA2D;MAAEc,QAAQ,EAAE;IAAZ,CAA3D,EAAkFX,QAAlF,CAAX,CAAuGI,MAAvG,CAA8GC,IAA9G,CAAmHC,KAAnH,CAAyH,CAAC;MACzHC,KAAK,EAAM,YAD8G;MAEzHC,OAAO,EAAI,IAF8G;MAGzHC,QAAQ,EAAG,EAH8G;MAIzHC,MAAM,EAAK;IAJ8G,CAAD,CAAzH,EAlE+B,CAyE/B;;IACAb,WAAW,CAAC,sEAAD,EAAyE;MAAEc,QAAQ,EAAE;IAAZ,CAAzE,EAAgGX,QAAhG,CAAX,CAAqHI,MAArH,CAA4HC,IAA5H,CAAiIC,KAAjI,CAAuI,CAAC;MACvIC,KAAK,EAAM,YAD4H;MAEvIC,OAAO,EAAI,IAF4H;MAGvII,GAAG,EAAQ,KAH4H;MAIvIH,QAAQ,EAAG,EAJ4H;MAKvIC,MAAM,EAAK;IAL4H,CAAD,CAAvI;EAOA,CAjFC,CAAF;EAmFAP,EAAE,CAAC,mCAAD,EAAsC,YAAM;IAC7C;IACAN,WAAW,CAAC,YAAD,EAAe,IAAf,EAAqBG,QAArB,CAAX,CAA0CI,MAA1C,CAAiDC,IAAjD,CAAsDC,KAAtD,CAA4D,EAA5D;EACA,CAHC,CAAF;EAKAH,EAAE,CAAC,iCAAD,EAAoC,YAAM;IAC3C;IACAN,WAAW,CAAC,sCAAD,EAAyCG,QAAzC,CAAX,CAA8DI,MAA9D,CAAqEC,IAArE,CAA0EC,KAA1E,CAAgF,CAAC;MAChFE,OAAO,EAAI,IADqE;MAEhFD,KAAK,EAAM,YAFqE;MAGhFE,QAAQ,EAAG,EAHqE;MAIhFC,MAAM,EAAK;IAJqE,CAAD,CAAhF;EAMA,CARC,CAAF;EAUAP,EAAE,CAAC,gBAAD,EAAmB,YAAM;IAC1B,IAAMU,gBAAgB,GAAG,CAAC;MACzBL,OAAO,EAAG,IADe;MAEzBD,KAAK,EAAK,YAFe;MAGzB;MACAE,QAAQ,EAAG,EAJc;MAKzBC,MAAM,EAAK;IALc,CAAD,EAMtB;MACFF,OAAO,EAAG,IADR;MAEFD,KAAK,EAAK,YAFR;MAGF;MACAE,QAAQ,EAAG,EAJT;MAKFC,MAAM,EAAK;IALT,CANsB,CAAzB;;IAcA,qDAAqBZ,kBAAkB,CAAC,qFAAD,EAAwF,IAAxF,EAA8FE,QAA9F,CAAvC,wCAAgJ;MAAA,IAArIc,MAAqI;MAC/IA,MAAM,CAACV,MAAP,CAAcC,IAAd,CAAmBC,KAAnB,CAAyBO,gBAAgB,CAACE,KAAjB,EAAzB;IACA;;IAEDF,gBAAgB,CAACG,MAAjB,CAAwBZ,MAAxB,CAA+BE,KAA/B,CAAqC,CAArC;EACA,CApBC,CAAF;EAsBAH,EAAE,CAAC,2BAAD,EAA8B,YAAM;IACrC,IAAIc,OAAJ,CADqC,CAGrC;;IACApB,WAAW,CAAC,EAAD,EAAKG,QAAL,CAAX,CAA0BI,MAA1B,CAAiCC,IAAjC,CAAsCC,KAAtC,CAA4C,EAA5C,EAJqC,CAMrC;;IACAW,OAAO,GAAG;MAAA,OAAMpB,WAAW,CAAC,KAAD,EAAQ,IAAR,EAAcG,QAAd,CAAjB;IAAA,CAAV;;IACAiB,OAAO,CAACb,MAAR,UAAqB,iBAArB,EARqC,CAUrC;;IACAa,OAAO,GAAG;MAAA,OAAMpB,WAAW,CAAC,UAAD,EAAa,IAAb,CAAjB;IAAA,CAAV;;IACAoB,OAAO,CAACb,MAAR,UAAqB,sCAArB,EAZqC,CAcrC;IACA;IACA;EACA,CAjBC,CAAF;EAmBAD,EAAE,CAAC,2DAAD,EAA8D,YAAM;IACrE;IACAN,WAAW,CAAC,kBAAD,EAAqB,IAArB,EAA2BG,QAA3B,CAAX,CAAgDI,MAAhD,CAAuDC,IAAvD,CAA4DC,KAA5D,CAAkE,EAAlE,EAFqE,CAIrE;;IACAT,WAAW,CAAC,eAAD,EAAkB,IAAlB,EAAwBG,QAAxB,CAAX,CAA6CI,MAA7C,CAAoDC,IAApD,CAAyDC,KAAzD,CAA+D,CAAC;MAC/DE,OAAO,EAAI,IADoD;MAE/DD,KAAK,EAAM,YAFoD;MAG/DE,QAAQ,EAAG,CAHoD;MAI/DC,MAAM,EAAK;IAJoD,CAAD,CAA/D,EALqE,CAYrE;;IACAb,WAAW,CAAC,aAAD,EAAgB,IAAhB,EAAsBG,QAAtB,CAAX,CAA2CI,MAA3C,CAAkDC,IAAlD,CAAuDC,KAAvD,CAA6D,EAA7D,EAbqE,CAerE;;IACAT,WAAW,CAAC,aAAD,EAAgB,IAAhB,EAAsBG,QAAtB,CAAX,CAA2CI,MAA3C,CAAkDC,IAAlD,CAAuDC,KAAvD,CAA6D,EAA7D,EAhBqE,CAkBrE;;IACAT,WAAW,CAAC,gCAAD,EAAmC,IAAnC,EAAyCG,QAAzC,CAAX,CAA8DI,MAA9D,CAAqEC,IAArE,CAA0EC,KAA1E,CAAgF,CAAC;MAChFE,OAAO,EAAI,IADqE;MAEhFD,KAAK,EAAM,WAFqE;MAGhFE,QAAQ,EAAG,EAHqE;MAIhFC,MAAM,EAAK;IAJqE,CAAD,CAAhF,EAnBqE,CA0BrE;IACA;;IACA,IAAMQ,eAAe,GAAGrB,WAAW,CAAC,iDAAD,EAAoD,IAApD,EAA0D;MAAEsB,QAAQ,EAAE;IAAZ,CAA1D,EAA8EnB,QAA9E,CAAnC;IACAkB,eAAe,CAACF,MAAhB,CAAuBZ,MAAvB,CAA8BE,KAA9B,CAAoC,CAApC;IACAY,eAAe,CAAC,CAAD,CAAf,CAAmBV,OAAnB,CAA2BJ,MAA3B,CAAkCE,KAAlC,CAAwC,IAAxC;IACAY,eAAe,CAAC,CAAD,CAAf,CAAmBX,KAAnB,CAAyBH,MAAzB,CAAgCE,KAAhC,CAAsC,WAAtC,EA/BqE,CAiCrE;IACA;;IACAT,WAAW,CAAC,iDAAD,EAAoD,IAApD,EAA0DG,QAA1D,CAAX,CAA+EI,MAA/E,CAAsFC,IAAtF,CAA2FC,KAA3F,CAAiG,EAAjG;EACA,CApCC,CAAF;AAqCA,CAjLO,CAAR;AAmLAJ,QAAQ,CAAC,mBAAD,EAAsB,YAAM;EACnCC,EAAE,CAAC,iCAAD,EAAoC,YAAM;IAC3C,IAAMiB,MAAM,GAAG,IAAIrB,iBAAJ,CAAsB,qFAAtB,EAA6G;MAAEsB,cAAc,EAAE;IAAlB,CAA7G,EAAuIrB,QAAvI,CAAf;IAEAoB,MAAM,CAACE,OAAP,GAAiBlB,MAAjB,CAAwBE,KAAxB,CAA8B,IAA9B;IACAc,MAAM,CAACG,IAAP,GAAcnB,MAAd,CAAqBC,IAArB,CAA0BC,KAA1B,CAAgC;MAC/BE,OAAO,EAAG,IADqB;MAE/BD,KAAK,EAAK,YAFqB;MAG/B;MACAE,QAAQ,EAAG,EAJoB;MAK/BC,MAAM,EAAK;IALoB,CAAhC;IAQAU,MAAM,CAACE,OAAP,GAAiBlB,MAAjB,CAAwBE,KAAxB,CAA8B,IAA9B;IACAc,MAAM,CAACG,IAAP,GAAcnB,MAAd,CAAqBC,IAArB,CAA0BC,KAA1B,CAAgC;MAC/BE,OAAO,EAAG,IADqB;MAE/BD,KAAK,EAAK,YAFqB;MAG/B;MACAE,QAAQ,EAAG,EAJoB;MAK/BC,MAAM,EAAK;IALoB,CAAhC;IAQAU,MAAM,CAACE,OAAP,GAAiBlB,MAAjB,CAAwBE,KAAxB,CAA8B,KAA9B;EACA,CAtBC,CAAF;EAwBAH,EAAE,CAAC,8CAAD,EAAiD,YAAM;IACxD,IAAMiB,MAAM,GAAG,IAAIrB,iBAAJ,CAAsB,kCAAtB,EAA0DyB,SAA1D,EAAqExB,QAArE,CAAf;IACAoB,MAAM,CAACE,OAAP,GAAiBlB,MAAjB,CAAwBE,KAAxB,CAA8B,IAA9B;IACAc,MAAM,CAACG,IAAP,GAAcnB,MAAd,CAAqBC,IAArB,CAA0BC,KAA1B,CAAgC;MAC/BE,OAAO,EAAG,IADqB;MAE/BD,KAAK,EAAK,YAFqB;MAG/B;MACAE,QAAQ,EAAG,EAJoB;MAK/BC,MAAM,EAAK;IALoB,CAAhC;IAOAU,MAAM,CAACE,OAAP,GAAiBlB,MAAjB,CAAwBE,KAAxB,CAA8B,KAA9B;EACA,CAXC,CAAF;EAaAH,EAAE,CAAC,2BAAD,EAA8B,YAAM;IACrC;IACA,IAAMsB,MAAM,GAAG,IAAI1B,iBAAJ,CAAsB,EAAtB,EAA0ByB,SAA1B,EAAqCxB,QAArC,CAAf,CAFqC,CAIrC;;IACA,IAAIiB,OAAO,GAAG,SAAVA,OAAU;MAAA,OAAMQ,MAAM,CAACF,IAAP,EAAN;IAAA,CAAd;;IACAN,OAAO,CAACb,MAAR,UAAqB,iBAArB;EACA,CAPC,CAAF;AAQA,CA9CO,CAAR"}