{"version": 3, "file": "parsePreCandidate.js", "names": ["SECOND_NUMBER_START_PATTERN", "parsePreCandidate", "candidate", "trimAfterFirstMatch"], "sources": ["../../source/findNumbers/parsePreCandidate.js"], "sourcesContent": ["import { trimAfterFirstMatch } from './util.js'\r\n\r\n// Regular expression of characters typically used to start a second phone number for the purposes\r\n// of parsing. This allows us to strip off parts of the number that are actually the start of\r\n// another number, such as for: (************* x302/x2303 -> the second extension here makes this\r\n// actually two phone numbers, (************* x302 and (************* x2303. We remove the second\r\n// extension so that the first number is parsed correctly.\r\n//\r\n// Matches a slash (\\ or /) followed by a space followed by an `x`.\r\n//\r\nconst SECOND_NUMBER_START_PATTERN = /[\\\\/] *x/\r\n\r\nexport default function parsePreCandidate(candidate)\r\n{\r\n\t// Check for extra numbers at the end.\r\n\t// TODO: This is the place to start when trying to support extraction of multiple phone number\r\n\t// from split notations (+41 79 123 45 67 / 68).\r\n\treturn trimAfterFirstMatch(SECOND_NUMBER_START_PATTERN, candidate)\r\n}"], "mappings": ";;;;;;;AAAA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAMA,2BAA2B,GAAG,UAApC;;AAEe,SAASC,iBAAT,CAA2BC,SAA3B,EACf;EACC;EACA;EACA;EACA,OAAO,IAAAC,yBAAA,EAAoBH,2BAApB,EAAiDE,SAAjD,CAAP;AACA"}