{"version": 3, "file": "validatePhoneNumberLength.js", "names": ["validatePhoneNumberLength", "normalizeArguments", "arguments", "text", "options", "metadata", "extract", "phoneNumber", "parsePhoneNumberWithError", "<PERSON><PERSON><PERSON>", "selectNumberingPlan", "countryCallingCode", "result", "checkNumberLength", "nationalNumber", "error", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "message"], "sources": ["../source/validatePhoneNumberLength.js"], "sourcesContent": ["import normalizeArguments from './normalizeArguments.js'\r\nimport parsePhoneNumberWithError from './parsePhoneNumberWithError_.js'\r\nimport ParseError from './ParseError.js'\r\nimport Metadata from './metadata.js'\r\nimport checkNumberLength from './helpers/checkNumberLength.js'\r\n\r\nexport default function validatePhoneNumberLength() {\r\n\tlet { text, options, metadata } = normalizeArguments(arguments)\r\n\toptions = {\r\n\t\t...options,\r\n\t\textract: false\r\n\t}\r\n\r\n\t// Parse phone number.\r\n\ttry {\r\n\t\tconst phoneNumber = parsePhoneNumberWithError(text, options, metadata)\r\n\t\tmetadata = new Metadata(metadata)\r\n\t\tmetadata.selectNumberingPlan(phoneNumber.countryCallingCode)\r\n\t\tconst result = checkNumberLength(phoneNumber.nationalNumber, metadata)\r\n\t\tif (result !== 'IS_POSSIBLE') {\r\n\t\t\treturn result\r\n\t\t}\r\n\t} catch (error) {\r\n\t\t/* istanbul ignore else */\r\n\t\tif (error instanceof ParseError) {\r\n\t\t\treturn error.message\r\n\t\t} else {\r\n\t\t\tthrow error\r\n\t\t}\r\n\t}\r\n}"], "mappings": ";;;;;;;AAAA;;AACA;;AACA;;AACA;;AACA;;;;;;;;;;AAEe,SAASA,yBAAT,GAAqC;EACnD,0BAAkC,IAAAC,+BAAA,EAAmBC,SAAnB,CAAlC;EAAA,IAAMC,IAAN,uBAAMA,IAAN;EAAA,IAAYC,OAAZ,uBAAYA,OAAZ;EAAA,IAAqBC,QAArB,uBAAqBA,QAArB;;EACAD,OAAO,mCACHA,OADG;IAENE,OAAO,EAAE;EAFH,EAAP,CAFmD,CAOnD;;EACA,IAAI;IACH,IAAMC,WAAW,GAAG,IAAAC,sCAAA,EAA0BL,IAA1B,EAAgCC,OAAhC,EAAyCC,QAAzC,CAApB;IACAA,QAAQ,GAAG,IAAII,oBAAJ,CAAaJ,QAAb,CAAX;IACAA,QAAQ,CAACK,mBAAT,CAA6BH,WAAW,CAACI,kBAAzC;IACA,IAAMC,MAAM,GAAG,IAAAC,6BAAA,EAAkBN,WAAW,CAACO,cAA9B,EAA8CT,QAA9C,CAAf;;IACA,IAAIO,MAAM,KAAK,aAAf,EAA8B;MAC7B,OAAOA,MAAP;IACA;EACD,CARD,CAQE,OAAOG,KAAP,EAAc;IACf;IACA,IAAIA,KAAK,YAAYC,sBAArB,EAAiC;MAChC,OAAOD,KAAK,CAACE,OAAb;IACA,CAFD,MAEO;MACN,MAAMF,KAAN;IACA;EACD;AACD"}