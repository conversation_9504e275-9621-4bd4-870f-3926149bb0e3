{"version": 3, "file": "formatIncompletePhoneNumber.js", "names": ["formatIncompletePhoneNumber", "value", "optionsOrDefaultCountry", "metadata", "undefined", "AsYouType", "input"], "sources": ["../source/formatIncompletePhoneNumber.js"], "sourcesContent": ["import AsYouType from './AsYouType.js'\r\n\r\n/**\r\n * Formats a (possibly incomplete) phone number.\r\n * The phone number can be either in E.164 format\r\n * or in a form of national number digits.\r\n * @param {string} value - A possibly incomplete phone number. Either in E.164 format or in a form of national number digits.\r\n * @param {string|object} [optionsOrDefaultCountry] - A two-letter (\"ISO 3166-1 alpha-2\") country code, or an object of shape `{ defaultCountry?: string, defaultCallingCode?: string }`.\r\n * @return {string} Formatted (possibly incomplete) phone number.\r\n */\r\nexport default function formatIncompletePhoneNumber(value, optionsOrDefaultCountry, metadata) {\r\n\tif (!metadata) {\r\n\t\tmetadata = optionsOrDefaultCountry\r\n\t\toptionsOrDefaultCountry = undefined\r\n\t}\r\n\treturn new AsYouType(optionsOrDefaultCountry, metadata).input(value)\r\n}"], "mappings": ";;;;;;;AAAA;;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACe,SAASA,2BAAT,CAAqCC,KAArC,EAA4CC,uBAA5C,EAAqEC,QAArE,EAA+E;EAC7F,IAAI,CAACA,QAAL,EAAe;IACdA,QAAQ,GAAGD,uBAAX;IACAA,uBAAuB,GAAGE,SAA1B;EACA;;EACD,OAAO,IAAIC,qBAAJ,CAAcH,uBAAd,EAAuCC,QAAvC,EAAiDG,KAAjD,CAAuDL,KAAvD,CAAP;AACA"}