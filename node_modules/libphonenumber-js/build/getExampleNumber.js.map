{"version": 3, "file": "getExampleNumber.js", "names": ["getExampleNumber", "country", "examples", "metadata", "PhoneNumber"], "sources": ["../source/getExampleNumber.js"], "sourcesContent": ["import PhoneNumber from './PhoneNumber.js'\r\n\r\nexport default function getExampleNumber(country, examples, metadata) {\r\n\tif (examples[country]) {\r\n\t\treturn new PhoneNumber(country, examples[country], metadata)\r\n\t}\r\n}"], "mappings": ";;;;;;;AAAA;;;;AAEe,SAASA,gBAAT,CAA0BC,OAA1B,EAAmCC,QAAnC,EAA6CC,QAA7C,EAAuD;EACrE,IAAID,QAAQ,CAACD,OAAD,CAAZ,EAAuB;IACtB,OAAO,IAAIG,uBAAJ,CAAgBH,OAAhB,EAAyBC,QAAQ,CAACD,OAAD,CAAjC,EAA4CE,QAA5C,CAAP;EACA;AACD"}