{"version": 3, "file": "isPossibleNumber.js", "names": ["isPossibleNumber", "normalizeArguments", "arguments", "input", "options", "metadata", "phone", "v2", "_isPossibleNumber"], "sources": ["../../source/legacy/isPossibleNumber.js"], "sourcesContent": ["import { normalizeArguments } from './getNumberType.js'\r\nimport _isPossibleNumber from '../isPossible.js'\r\n\r\n/**\r\n * Checks if a given phone number is possible.\r\n * Which means it only checks phone number length\r\n * and doesn't test any regular expressions.\r\n *\r\n * Examples:\r\n *\r\n * ```js\r\n * isPossibleNumber('+78005553535', metadata)\r\n * isPossibleNumber('8005553535', 'RU', metadata)\r\n * isPossibleNumber('88005553535', 'RU', metadata)\r\n * isPossibleNumber({ phone: '8005553535', country: 'RU' }, metadata)\r\n * ```\r\n */\r\nexport default function isPossibleNumber() {\r\n\tconst { input, options, metadata } = normalizeArguments(arguments)\r\n\t// `parseNumber()` would return `{}` when no phone number could be parsed from the input.\r\n\tif (!input.phone && !(options && options.v2)) {\r\n\t\treturn false\r\n\t}\r\n\treturn _isPossibleNumber(input, options, metadata)\r\n}"], "mappings": ";;;;;;;AAAA;;AACA;;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACe,SAASA,gBAAT,GAA4B;EAC1C,0BAAqC,IAAAC,iCAAA,EAAmBC,SAAnB,CAArC;EAAA,IAAQC,KAAR,uBAAQA,KAAR;EAAA,IAAeC,OAAf,uBAAeA,OAAf;EAAA,IAAwBC,QAAxB,uBAAwBA,QAAxB,CAD0C,CAE1C;;;EACA,IAAI,CAACF,KAAK,CAACG,KAAP,IAAgB,EAAEF,OAAO,IAAIA,OAAO,CAACG,EAArB,CAApB,EAA8C;IAC7C,OAAO,KAAP;EACA;;EACD,OAAO,IAAAC,sBAAA,EAAkBL,KAAlB,EAAyBC,OAAzB,EAAkCC,QAAlC,CAAP;AACA"}