{"version": 3, "file": "isValidNumber.test.js", "names": ["isValidNumber", "parameters", "push", "metadata", "_isValidNumber", "apply", "describe", "it", "should", "equal", "undefined", "country", "phone", "console", "log", "phoneNumberTypePatterns", "countries", "UZ", "thrower"], "sources": ["../../source/legacy/isValidNumber.test.js"], "sourcesContent": ["import metadata from '../../metadata.min.json' assert { type: 'json' }\r\nimport _isValidNumber from './isValidNumber.js'\r\n\r\nfunction isValidNumber(...parameters) {\r\n\tparameters.push(metadata)\r\n\treturn _isValidNumber.apply(this, parameters)\r\n}\r\n\r\ndescribe('isValidNumber', () => {\r\n\tit('should validate phone numbers', () => {\r\n\t\tisValidNumber('******-373-4253').should.equal(true)\r\n\t\tisValidNumber('******-373').should.equal(false)\r\n\r\n\t\tisValidNumber('******-373-4253', undefined).should.equal(true)\r\n\r\n\t\tisValidNumber('(*************', 'US').should.equal(true)\r\n\t\tisValidNumber('(213) 37', 'US').should.equal(false)\r\n\r\n\t\tisValidNumber({ country: 'US', phone: '2133734253' }).should.equal(true)\r\n\r\n\t\t// No \"types\" info: should return `true`.\r\n\t\tisValidNumber('+380972423740').should.equal(true)\r\n\r\n\t\tisValidNumber('0912345678', 'TW').should.equal(true)\r\n\r\n\t\t// Moible numbers starting 07624* are Isle of Man\r\n\t\t// which has its own \"country code\" \"IM\"\r\n\t\t// which is in the \"GB\" \"country calling code\" zone.\r\n\t\t// So while this number is for \"IM\" it's still supposed to\r\n\t\t// be valid when passed \"GB\" as a default country.\r\n\t\tisValidNumber('07624369230', 'GB').should.equal(true)\r\n\t})\r\n\r\n\tit('should refine phone number validation in case extended regular expressions are set for a country', () => {\r\n\t\t// Germany general validation must pass\r\n\t\tconsole.log('--------------------------')\r\n\t\tisValidNumber('961111111', 'UZ').should.equal(true)\r\n\r\n\t\tconst phoneNumberTypePatterns = metadata.countries.UZ[11]\r\n\r\n\t\t// Different regular expressions for precise national number validation.\r\n\t\t// `types` index in compressed array is `9` for v1.\r\n\t\t// For v2 it's 10.\r\n\t\t// For v3 it's 11.\r\n\t\tmetadata.countries.UZ[11] =\r\n\t\t[\r\n\t\t\t[\"(?:6(?:1(?:22|3[124]|4[1-4]|5[123578]|64)|2(?:22|3[0-57-9]|41)|5(?:22|3[3-7]|5[024-8])|6\\\\d{2}|7(?:[23]\\\\d|7[69])|9(?:22|4[1-8]|6[135]))|7(?:0(?:5[4-9]|6[0146]|7[12456]|9[135-8])|1[12]\\\\d|2(?:22|3[1345789]|4[123579]|5[14])|3(?:2\\\\d|3[1578]|4[1-35-7]|5[1-57]|61)|4(?:2\\\\d|3[1-579]|7[1-79])|5(?:22|5[1-9]|6[1457])|6(?:22|3[12457]|4[13-8])|9(?:22|5[1-9])))\\\\d{5}\"],\r\n\t\t\t[\"6(?:1(?:2(?:98|2[01])|35[0-4]|50\\\\d|61[23]|7(?:[01][017]|4\\\\d|55|9[5-9]))|2(?:11\\\\d|2(?:[12]1|9[01379])|5(?:[126]\\\\d|3[0-4])|7\\\\d{2})|5(?:19[01]|2(?:27|9[26])|30\\\\d|59\\\\d|7\\\\d{2})|6(?:2(?:1[5-9]|2[0367]|38|41|52|60)|3[79]\\\\d|4(?:56|83)|7(?:[07]\\\\d|1[017]|3[07]|4[047]|5[057]|67|8[0178]|9[79])|9[0-3]\\\\d)|7(?:2(?:24|3[237]|4[5-9]|7[15-8])|5(?:7[12]|8[0589])|7(?:0\\\\d|[39][07])|9(?:0\\\\d|7[079]))|9(?:2(?:1[1267]|5\\\\d|3[01]|7[0-4])|5[67]\\\\d|6(?:2[0-26]|8\\\\d)|7\\\\d{2}))\\\\d{4}|7(?:0\\\\d{3}|1(?:13[01]|6(?:0[47]|1[67]|66)|71[3-69]|98\\\\d)|2(?:2(?:2[79]|95)|3(?:2[5-9]|6[0-6])|57\\\\d|7(?:0\\\\d|1[17]|2[27]|3[37]|44|5[057]|66|88))|3(?:2(?:1[0-6]|21|3[469]|7[159])|33\\\\d|5(?:0[0-4]|5[579]|9\\\\d)|7(?:[0-3579]\\\\d|4[0467]|6[67]|8[078])|9[4-6]\\\\d)|4(?:2(?:29|5[0257]|6[0-7]|7[1-57])|5(?:1[0-4]|8\\\\d|9[5-9])|7(?:0\\\\d|1[024589]|2[0127]|3[0137]|[46][07]|5[01]|7[5-9]|9[079])|9(?:7[015-9]|[89]\\\\d))|5(?:112|2(?:0\\\\d|2[29]|[49]4)|3[1568]\\\\d|52[6-9]|7(?:0[01578]|1[017]|[23]7|4[047]|[5-7]\\\\d|8[78]|9[079]))|6(?:2(?:2[1245]|4[2-4])|39\\\\d|41[179]|5(?:[349]\\\\d|5[0-2])|7(?:0[017]|[13]\\\\d|22|44|55|67|88))|9(?:22[128]|3(?:2[0-4]|7\\\\d)|57[05629]|7(?:2[05-9]|3[37]|4\\\\d|60|7[2579]|87|9[07])))\\\\d{4}|9[0-57-9]\\\\d{7}\"]\r\n\t\t]\r\n\r\n\t\t// Extended validation must not pass for an invalid phone number\r\n\t\tisValidNumber('961111111', 'UZ').should.equal(false)\r\n\r\n\t\t// Extended validation must pass for a valid phone number\r\n\t\tisValidNumber('912345678', 'UZ').should.equal(true)\r\n\r\n\t\tmetadata.countries.UZ[11] = phoneNumberTypePatterns\r\n\t})\r\n\r\n\tit('should work in edge cases', () => {\r\n\t\t// No metadata\r\n\t\tlet thrower = () => _isValidNumber('+78005553535')\r\n\t\tthrower.should.throw('`metadata` argument not passed')\r\n\r\n\t\t// Non-phone-number characters in a phone number\r\n\t\tisValidNumber('+499821958a').should.equal(false)\r\n\t\tisValidNumber('88005553535x', 'RU').should.equal(false)\r\n\r\n\t\t// Doesn't have `types` regexps in default metadata.\r\n\t\tisValidNumber({ country: 'UA', phone: '300000000' }).should.equal(true)\r\n\t\tisValidNumber({ country: 'UA', phone: '200000000' }).should.equal(false)\r\n\r\n\t\t// Numerical `value`\r\n\t\tthrower = () => isValidNumber(88005553535, 'RU')\r\n\t\tthrower.should.throw('A phone number must either be a string or an object of shape { phone, [country] }.')\r\n\r\n\t\t// Long country phone code\r\n\t\tisValidNumber('+3725555555').should.equal(true)\r\n\r\n\t\t// Invalid country\r\n\t\tthrower = () => isValidNumber({ phone: '8005553535', country: 'RUS' })\r\n\t\tthrower.should.throw('Unknown country')\r\n\t})\r\n\r\n\tit('should accept phone number extensions', () => {\r\n\t\t// International\r\n\t\tisValidNumber('+12133734253 ext. 123').should.equal(true)\r\n\t\t// National\r\n\t\tisValidNumber('88005553535 x123', 'RU').should.equal(true)\r\n\t})\r\n})"], "mappings": ";;AAAA;;AACA;;;;AAEA,SAASA,aAAT,GAAsC;EAAA,kCAAZC,UAAY;IAAZA,UAAY;EAAA;;EACrCA,UAAU,CAACC,IAAX,CAAgBC,uBAAhB;EACA,OAAOC,0BAAA,CAAeC,KAAf,CAAqB,IAArB,EAA2BJ,UAA3B,CAAP;AACA;;AAEDK,QAAQ,CAAC,eAAD,EAAkB,YAAM;EAC/BC,EAAE,CAAC,+BAAD,EAAkC,YAAM;IACzCP,aAAa,CAAC,iBAAD,CAAb,CAAiCQ,MAAjC,CAAwCC,KAAxC,CAA8C,IAA9C;IACAT,aAAa,CAAC,YAAD,CAAb,CAA4BQ,MAA5B,CAAmCC,KAAnC,CAAyC,KAAzC;IAEAT,aAAa,CAAC,iBAAD,EAAoBU,SAApB,CAAb,CAA4CF,MAA5C,CAAmDC,KAAnD,CAAyD,IAAzD;IAEAT,aAAa,CAAC,gBAAD,EAAmB,IAAnB,CAAb,CAAsCQ,MAAtC,CAA6CC,KAA7C,CAAmD,IAAnD;IACAT,aAAa,CAAC,UAAD,EAAa,IAAb,CAAb,CAAgCQ,MAAhC,CAAuCC,KAAvC,CAA6C,KAA7C;IAEAT,aAAa,CAAC;MAAEW,OAAO,EAAE,IAAX;MAAiBC,KAAK,EAAE;IAAxB,CAAD,CAAb,CAAsDJ,MAAtD,CAA6DC,KAA7D,CAAmE,IAAnE,EATyC,CAWzC;;IACAT,aAAa,CAAC,eAAD,CAAb,CAA+BQ,MAA/B,CAAsCC,KAAtC,CAA4C,IAA5C;IAEAT,aAAa,CAAC,YAAD,EAAe,IAAf,CAAb,CAAkCQ,MAAlC,CAAyCC,KAAzC,CAA+C,IAA/C,EAdyC,CAgBzC;IACA;IACA;IACA;IACA;;IACAT,aAAa,CAAC,aAAD,EAAgB,IAAhB,CAAb,CAAmCQ,MAAnC,CAA0CC,KAA1C,CAAgD,IAAhD;EACA,CAtBC,CAAF;EAwBAF,EAAE,CAAC,kGAAD,EAAqG,YAAM;IAC5G;IACAM,OAAO,CAACC,GAAR,CAAY,4BAAZ;IACAd,aAAa,CAAC,WAAD,EAAc,IAAd,CAAb,CAAiCQ,MAAjC,CAAwCC,KAAxC,CAA8C,IAA9C;IAEA,IAAMM,uBAAuB,GAAGZ,uBAAA,CAASa,SAAT,CAAmBC,EAAnB,CAAsB,EAAtB,CAAhC,CAL4G,CAO5G;IACA;IACA;IACA;;IACAd,uBAAA,CAASa,SAAT,CAAmBC,EAAnB,CAAsB,EAAtB,IACA,CACC,CAAC,yWAAD,CADD,EAEC,CAAC,mqCAAD,CAFD,CADA,CAX4G,CAiB5G;;IACAjB,aAAa,CAAC,WAAD,EAAc,IAAd,CAAb,CAAiCQ,MAAjC,CAAwCC,KAAxC,CAA8C,KAA9C,EAlB4G,CAoB5G;;IACAT,aAAa,CAAC,WAAD,EAAc,IAAd,CAAb,CAAiCQ,MAAjC,CAAwCC,KAAxC,CAA8C,IAA9C;IAEAN,uBAAA,CAASa,SAAT,CAAmBC,EAAnB,CAAsB,EAAtB,IAA4BF,uBAA5B;EACA,CAxBC,CAAF;EA0BAR,EAAE,CAAC,2BAAD,EAA8B,YAAM;IACrC;IACA,IAAIW,OAAO,GAAG;MAAA,OAAM,IAAAd,0BAAA,EAAe,cAAf,CAAN;IAAA,CAAd;;IACAc,OAAO,CAACV,MAAR,UAAqB,gCAArB,EAHqC,CAKrC;;IACAR,aAAa,CAAC,aAAD,CAAb,CAA6BQ,MAA7B,CAAoCC,KAApC,CAA0C,KAA1C;IACAT,aAAa,CAAC,cAAD,EAAiB,IAAjB,CAAb,CAAoCQ,MAApC,CAA2CC,KAA3C,CAAiD,KAAjD,EAPqC,CASrC;;IACAT,aAAa,CAAC;MAAEW,OAAO,EAAE,IAAX;MAAiBC,KAAK,EAAE;IAAxB,CAAD,CAAb,CAAqDJ,MAArD,CAA4DC,KAA5D,CAAkE,IAAlE;IACAT,aAAa,CAAC;MAAEW,OAAO,EAAE,IAAX;MAAiBC,KAAK,EAAE;IAAxB,CAAD,CAAb,CAAqDJ,MAArD,CAA4DC,KAA5D,CAAkE,KAAlE,EAXqC,CAarC;;IACAS,OAAO,GAAG;MAAA,OAAMlB,aAAa,CAAC,WAAD,EAAc,IAAd,CAAnB;IAAA,CAAV;;IACAkB,OAAO,CAACV,MAAR,UAAqB,oFAArB,EAfqC,CAiBrC;;IACAR,aAAa,CAAC,aAAD,CAAb,CAA6BQ,MAA7B,CAAoCC,KAApC,CAA0C,IAA1C,EAlBqC,CAoBrC;;IACAS,OAAO,GAAG;MAAA,OAAMlB,aAAa,CAAC;QAAEY,KAAK,EAAE,YAAT;QAAuBD,OAAO,EAAE;MAAhC,CAAD,CAAnB;IAAA,CAAV;;IACAO,OAAO,CAACV,MAAR,UAAqB,iBAArB;EACA,CAvBC,CAAF;EAyBAD,EAAE,CAAC,uCAAD,EAA0C,YAAM;IACjD;IACAP,aAAa,CAAC,uBAAD,CAAb,CAAuCQ,MAAvC,CAA8CC,KAA9C,CAAoD,IAApD,EAFiD,CAGjD;;IACAT,aAAa,CAAC,kBAAD,EAAqB,IAArB,CAAb,CAAwCQ,MAAxC,CAA+CC,KAA/C,CAAqD,IAArD;EACA,CALC,CAAF;AAMA,CAlFO,CAAR"}