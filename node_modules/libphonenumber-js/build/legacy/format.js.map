{"version": 3, "file": "format.js", "names": ["formatNumber", "normalizeArguments", "arguments", "input", "format", "options", "metadata", "_formatNumber", "args", "Array", "prototype", "slice", "call", "arg_1", "arg_2", "arg_3", "arg_4", "arg_5", "parse", "defaultCountry", "extended", "Error", "isObject", "TypeError"], "sources": ["../../source/legacy/format.js"], "sourcesContent": ["import _formatNumber from '../format.js'\r\nimport parse from '../parse.js'\r\nimport isObject from '../helpers/isObject.js'\r\n\r\nexport default function formatNumber() {\r\n\tconst {\r\n\t\tinput,\r\n\t\tformat,\r\n\t\toptions,\r\n\t\tmetadata\r\n\t} = normalizeArguments(arguments)\r\n\r\n\treturn _formatNumber(input, format, options, metadata)\r\n}\r\n\r\n// Sort out arguments\r\nfunction normalizeArguments(args)\r\n{\r\n\tconst [arg_1, arg_2, arg_3, arg_4, arg_5] = Array.prototype.slice.call(args)\r\n\r\n\tlet input\r\n\tlet format\r\n\tlet options\r\n\tlet metadata\r\n\r\n\t// Sort out arguments.\r\n\r\n\t// If the phone number is passed as a string.\r\n\t// `format('8005553535', ...)`.\r\n\tif (typeof arg_1 === 'string')\r\n\t{\r\n\t\t// If country code is supplied.\r\n\t\t// `format('8005553535', 'RU', 'NATIONAL', [options], metadata)`.\r\n\t\tif (typeof arg_3 === 'string')\r\n\t\t{\r\n\t\t\tformat = arg_3\r\n\r\n\t\t\tif (arg_5)\r\n\t\t\t{\r\n\t\t\t\toptions  = arg_4\r\n\t\t\t\tmetadata = arg_5\r\n\t\t\t}\r\n\t\t\telse\r\n\t\t\t{\r\n\t\t\t\tmetadata = arg_4\r\n\t\t\t}\r\n\r\n\t\t\tinput = parse(arg_1, { defaultCountry: arg_2, extended: true }, metadata)\r\n\t\t}\r\n\t\t// Just an international phone number is supplied\r\n\t\t// `format('+78005553535', 'NATIONAL', [options], metadata)`.\r\n\t\telse\r\n\t\t{\r\n\t\t\tif (typeof arg_2 !== 'string')\r\n\t\t\t{\r\n\t\t\t\tthrow new Error('`format` argument not passed to `formatNumber(number, format)`')\r\n\t\t\t}\r\n\r\n\t\t\tformat = arg_2\r\n\r\n\t\t\tif (arg_4)\r\n\t\t\t{\r\n\t\t\t\toptions  = arg_3\r\n\t\t\t\tmetadata = arg_4\r\n\t\t\t}\r\n\t\t\telse\r\n\t\t\t{\r\n\t\t\t\tmetadata = arg_3\r\n\t\t\t}\r\n\r\n\t\t\tinput = parse(arg_1, { extended: true }, metadata)\r\n\t\t}\r\n\t}\r\n\t// If the phone number is passed as a parsed number object.\r\n\t// `format({ phone: '8005553535', country: 'RU' }, 'NATIONAL', [options], metadata)`.\r\n\telse if (isObject(arg_1))\r\n\t{\r\n\t\tinput  = arg_1\r\n\t\tformat = arg_2\r\n\r\n\t\tif (arg_4)\r\n\t\t{\r\n\t\t\toptions  = arg_3\r\n\t\t\tmetadata = arg_4\r\n\t\t}\r\n\t\telse\r\n\t\t{\r\n\t\t\tmetadata = arg_3\r\n\t\t}\r\n\t}\r\n\telse throw new TypeError('A phone number must either be a string or an object of shape { phone, [country] }.')\r\n\r\n\t// Legacy lowercase formats.\r\n\tif (format === 'International') {\r\n\t\tformat = 'INTERNATIONAL'\r\n\t} else if (format === 'National') {\r\n\t\tformat = 'NATIONAL'\r\n\t}\r\n\r\n\treturn {\r\n\t\tinput,\r\n\t\tformat,\r\n\t\toptions,\r\n\t\tmetadata\r\n\t}\r\n}"], "mappings": ";;;;;;;AAAA;;AACA;;AACA;;;;;;;;;;;;;;;;AAEe,SAASA,YAAT,GAAwB;EACtC,0BAKIC,kBAAkB,CAACC,SAAD,CALtB;EAAA,IACCC,KADD,uBACCA,KADD;EAAA,IAECC,MAFD,uBAECA,MAFD;EAAA,IAGCC,OAHD,uBAGCA,OAHD;EAAA,IAICC,QAJD,uBAICA,QAJD;;EAOA,OAAO,IAAAC,kBAAA,EAAcJ,KAAd,EAAqBC,MAArB,EAA6BC,OAA7B,EAAsCC,QAAtC,CAAP;AACA,C,CAED;;;AACA,SAASL,kBAAT,CAA4BO,IAA5B,EACA;EACC,4BAA4CC,KAAK,CAACC,SAAN,CAAgBC,KAAhB,CAAsBC,IAAtB,CAA2BJ,IAA3B,CAA5C;EAAA;EAAA,IAAOK,KAAP;EAAA,IAAcC,KAAd;EAAA,IAAqBC,KAArB;EAAA,IAA4BC,KAA5B;EAAA,IAAmCC,KAAnC;;EAEA,IAAId,KAAJ;EACA,IAAIC,MAAJ;EACA,IAAIC,OAAJ;EACA,IAAIC,QAAJ,CAND,CAQC;EAEA;EACA;;EACA,IAAI,OAAOO,KAAP,KAAiB,QAArB,EACA;IACC;IACA;IACA,IAAI,OAAOE,KAAP,KAAiB,QAArB,EACA;MACCX,MAAM,GAAGW,KAAT;;MAEA,IAAIE,KAAJ,EACA;QACCZ,OAAO,GAAIW,KAAX;QACAV,QAAQ,GAAGW,KAAX;MACA,CAJD,MAMA;QACCX,QAAQ,GAAGU,KAAX;MACA;;MAEDb,KAAK,GAAG,IAAAe,iBAAA,EAAML,KAAN,EAAa;QAAEM,cAAc,EAAEL,KAAlB;QAAyBM,QAAQ,EAAE;MAAnC,CAAb,EAAwDd,QAAxD,CAAR;IACA,CAfD,CAgBA;IACA;IAjBA,KAmBA;MACC,IAAI,OAAOQ,KAAP,KAAiB,QAArB,EACA;QACC,MAAM,IAAIO,KAAJ,CAAU,gEAAV,CAAN;MACA;;MAEDjB,MAAM,GAAGU,KAAT;;MAEA,IAAIE,KAAJ,EACA;QACCX,OAAO,GAAIU,KAAX;QACAT,QAAQ,GAAGU,KAAX;MACA,CAJD,MAMA;QACCV,QAAQ,GAAGS,KAAX;MACA;;MAEDZ,KAAK,GAAG,IAAAe,iBAAA,EAAML,KAAN,EAAa;QAAEO,QAAQ,EAAE;MAAZ,CAAb,EAAiCd,QAAjC,CAAR;IACA;EACD,CA3CD,CA4CA;EACA;EA7CA,KA8CK,IAAI,IAAAgB,oBAAA,EAAST,KAAT,CAAJ,EACL;IACCV,KAAK,GAAIU,KAAT;IACAT,MAAM,GAAGU,KAAT;;IAEA,IAAIE,KAAJ,EACA;MACCX,OAAO,GAAIU,KAAX;MACAT,QAAQ,GAAGU,KAAX;IACA,CAJD,MAMA;MACCV,QAAQ,GAAGS,KAAX;IACA;EACD,CAdI,MAeA,MAAM,IAAIQ,SAAJ,CAAc,oFAAd,CAAN,CAzEN,CA2EC;;;EACA,IAAInB,MAAM,KAAK,eAAf,EAAgC;IAC/BA,MAAM,GAAG,eAAT;EACA,CAFD,MAEO,IAAIA,MAAM,KAAK,UAAf,EAA2B;IACjCA,MAAM,GAAG,UAAT;EACA;;EAED,OAAO;IACND,KAAK,EAALA,KADM;IAENC,MAAM,EAANA,MAFM;IAGNC,OAAO,EAAPA,OAHM;IAINC,QAAQ,EAARA;EAJM,CAAP;AAMA"}