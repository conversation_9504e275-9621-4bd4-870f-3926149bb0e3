{"version": 3, "file": "extractNationalNumber.js", "names": ["extractNationalNumber", "number", "metadata", "extractNationalNumberFromPossiblyIncompleteNumber", "carrierCode", "nationalNumber", "shouldHaveExtractedNationalPrefix", "possibleLengths", "isPossibleIncompleteNationalNumber", "nationalNumberBefore", "nationalNumberAfter", "matchesEntirely", "nationalNumberPattern", "checkNumberLength"], "sources": ["../../source/helpers/extractNationalNumber.js"], "sourcesContent": ["import extractNationalNumberFromPossiblyIncompleteNumber from './extractNationalNumberFromPossiblyIncompleteNumber.js'\r\nimport matchesEntirely from './matchesEntirely.js'\r\nimport checkNumberLength from './checkNumberLength.js'\r\n\r\n/**\r\n * Strips national prefix and carrier code from a complete phone number.\r\n * The difference from the non-\"FromCompleteNumber\" function is that\r\n * it won't extract national prefix if the resultant number is too short\r\n * to be a complete number for the selected phone numbering plan.\r\n * @param  {string} number — Complete phone number digits.\r\n * @param  {Metadata} metadata — Metadata with a phone numbering plan selected.\r\n * @return {object} `{ nationalNumber: string, carrierCode: string? }`.\r\n */\r\nexport default function extractNationalNumber(number, metadata) {\r\n\t// Parsing national prefixes and carrier codes\r\n\t// is only required for local phone numbers\r\n\t// but some people don't understand that\r\n\t// and sometimes write international phone numbers\r\n\t// with national prefixes (or maybe even carrier codes).\r\n\t// http://ucken.blogspot.ru/2016/03/trunk-prefixes-in-skype4b.html\r\n\t// Google's original library forgives such mistakes\r\n\t// and so does this library, because it has been requested:\r\n\t// https://github.com/catamphetamine/libphonenumber-js/issues/127\r\n\tconst {\r\n\t\tcarrierCode,\r\n\t\tnationalNumber\r\n\t} = extractNationalNumberFromPossiblyIncompleteNumber(\r\n\t\tnumber,\r\n\t\tmetadata\r\n\t)\r\n\r\n\tif (nationalNumber !== number) {\r\n\t\tif (!shouldHaveExtractedNationalPrefix(number, nationalNumber, metadata)) {\r\n\t\t\t// Don't strip the national prefix.\r\n\t\t\treturn { nationalNumber: number }\r\n\t\t}\r\n\t\t// Check the national (significant) number length after extracting national prefix and carrier code.\r\n\t\t// Legacy generated metadata (before `1.0.18`) didn't support the \"possible lengths\" feature.\r\n\t\tif (metadata.possibleLengths()) {\r\n\t\t\t// The number remaining after stripping the national prefix and carrier code\r\n\t\t\t// should be long enough to have a possible length for the country.\r\n\t\t\t// Otherwise, don't strip the national prefix and carrier code,\r\n\t\t\t// since the original number could be a valid number.\r\n\t\t\t// This check has been copy-pasted \"as is\" from Google's original library:\r\n\t\t\t// https://github.com/google/libphonenumber/blob/876268eb1ad6cdc1b7b5bef17fc5e43052702d57/java/libphonenumber/src/com/google/i18n/phonenumbers/PhoneNumberUtil.java#L3236-L3250\r\n\t\t\t// It doesn't check for the \"possibility\" of the original `number`.\r\n\t\t\t// I guess it's fine not checking that one. It works as is anyway.\r\n\t\t\tif (!isPossibleIncompleteNationalNumber(nationalNumber, metadata)) {\r\n\t\t\t\t// Don't strip the national prefix.\r\n\t\t\t\treturn { nationalNumber: number }\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\r\n\treturn { nationalNumber, carrierCode }\r\n}\r\n\r\n// In some countries, the same digit could be a national prefix\r\n// or a leading digit of a valid phone number.\r\n// For example, in Russia, national prefix is `8`,\r\n// and also `800 555 35 35` is a valid number\r\n// in which `8` is not a national prefix, but the first digit\r\n// of a national (significant) number.\r\n// Same's with Belarus:\r\n// `82004910060` is a valid national (significant) number,\r\n// but `2004910060` is not.\r\n// To support such cases (to prevent the code from always stripping\r\n// national prefix), a condition is imposed: a national prefix\r\n// is not extracted when the original number is \"viable\" and the\r\n// resultant number is not, a \"viable\" national number being the one\r\n// that matches `national_number_pattern`.\r\nfunction shouldHaveExtractedNationalPrefix(nationalNumberBefore, nationalNumberAfter, metadata) {\r\n\t// The equivalent in Google's code is:\r\n\t// https://github.com/google/libphonenumber/blob/e326fa1fc4283bb05eb35cb3c15c18f98a31af33/java/libphonenumber/src/com/google/i18n/phonenumbers/PhoneNumberUtil.java#L2969-L3004\r\n\tif (matchesEntirely(nationalNumberBefore, metadata.nationalNumberPattern()) &&\r\n\t\t!matchesEntirely(nationalNumberAfter, metadata.nationalNumberPattern())) {\r\n\t\treturn false\r\n\t}\r\n\t// This \"is possible\" national number (length) check has been commented out\r\n\t// because it's superceded by the (effectively) same check done in the\r\n\t// `extractNationalNumber()` function after it calls `shouldHaveExtractedNationalPrefix()`.\r\n\t// In other words, why run the same check twice if it could only be run once.\r\n\t// // Check the national (significant) number length after extracting national prefix and carrier code.\r\n\t// // Fixes a minor \"weird behavior\" bug: https://gitlab.com/catamphetamine/libphonenumber-js/-/issues/57\r\n\t// // (Legacy generated metadata (before `1.0.18`) didn't support the \"possible lengths\" feature).\r\n\t// if (metadata.possibleLengths()) {\r\n\t// \tif (isPossibleIncompleteNationalNumber(nationalNumberBefore, metadata) &&\r\n\t// \t\t!isPossibleIncompleteNationalNumber(nationalNumberAfter, metadata)) {\r\n\t// \t\treturn false\r\n\t// \t}\r\n\t// }\r\n\treturn true\r\n}\r\n\r\nfunction isPossibleIncompleteNationalNumber(nationalNumber, metadata) {\r\n\tswitch (checkNumberLength(nationalNumber, metadata)) {\r\n\t\tcase 'TOO_SHORT':\r\n\t\tcase 'INVALID_LENGTH':\r\n\t\t// This library ignores \"local-only\" phone numbers (for simplicity).\r\n\t\t// See the readme for more info on what are \"local-only\" phone numbers.\r\n\t\t// case 'IS_POSSIBLE_LOCAL_ONLY':\r\n\t\t\treturn false\r\n\t\tdefault:\r\n\t\t\treturn true\r\n\t}\r\n}"], "mappings": ";;;;;;;AAAA;;AACA;;AACA;;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACe,SAASA,qBAAT,CAA+BC,MAA/B,EAAuCC,QAAvC,EAAiD;EAC/D;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA,4BAGI,IAAAC,6DAAA,EACHF,MADG,EAEHC,QAFG,CAHJ;EAAA,IACCE,WADD,yBACCA,WADD;EAAA,IAECC,cAFD,yBAECA,cAFD;;EAQA,IAAIA,cAAc,KAAKJ,MAAvB,EAA+B;IAC9B,IAAI,CAACK,iCAAiC,CAACL,MAAD,EAASI,cAAT,EAAyBH,QAAzB,CAAtC,EAA0E;MACzE;MACA,OAAO;QAAEG,cAAc,EAAEJ;MAAlB,CAAP;IACA,CAJ6B,CAK9B;IACA;;;IACA,IAAIC,QAAQ,CAACK,eAAT,EAAJ,EAAgC;MAC/B;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA,IAAI,CAACC,kCAAkC,CAACH,cAAD,EAAiBH,QAAjB,CAAvC,EAAmE;QAClE;QACA,OAAO;UAAEG,cAAc,EAAEJ;QAAlB,CAAP;MACA;IACD;EACD;;EAED,OAAO;IAAEI,cAAc,EAAdA,cAAF;IAAkBD,WAAW,EAAXA;EAAlB,CAAP;AACA,C,CAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACA,SAASE,iCAAT,CAA2CG,oBAA3C,EAAiEC,mBAAjE,EAAsFR,QAAtF,EAAgG;EAC/F;EACA;EACA,IAAI,IAAAS,2BAAA,EAAgBF,oBAAhB,EAAsCP,QAAQ,CAACU,qBAAT,EAAtC,KACH,CAAC,IAAAD,2BAAA,EAAgBD,mBAAhB,EAAqCR,QAAQ,CAACU,qBAAT,EAArC,CADF,EAC0E;IACzE,OAAO,KAAP;EACA,CAN8F,CAO/F;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;EACA,OAAO,IAAP;AACA;;AAED,SAASJ,kCAAT,CAA4CH,cAA5C,EAA4DH,QAA5D,EAAsE;EACrE,QAAQ,IAAAW,6BAAA,EAAkBR,cAAlB,EAAkCH,QAAlC,CAAR;IACC,KAAK,WAAL;IACA,KAAK,gBAAL;MACA;MACA;MACA;MACC,OAAO,KAAP;;IACD;MACC,OAAO,IAAP;EARF;AAUA"}