{"version": 3, "file": "getCountryByCallingCode.js", "names": ["USE_NON_GEOGRAPHIC_COUNTRY_CODE", "getCountryByCallingCode", "callingCode", "nationalPhoneNumber", "nationalNumber", "defaultCountry", "metadata", "isNonGeographicCallingCode", "possibleCountries", "getCountryCodesForCallingCode", "length", "getCountryByNationalNumber", "countries"], "sources": ["../../source/helpers/getCountryByCallingCode.js"], "sourcesContent": ["import getCountryByNationalNumber from './getCountryByNationalNumber.js'\r\n\r\nconst USE_NON_GEOGRAPHIC_COUNTRY_CODE = false\r\n\r\nexport default function getCountryByCallingCode(callingCode, {\r\n\tnationalNumber: nationalPhoneNumber,\r\n\tdefaultCountry,\r\n\tmetadata\r\n}) {\r\n\t/* istanbul ignore if */\r\n\tif (USE_NON_GEOGRAPHIC_COUNTRY_CODE) {\r\n\t\tif (metadata.isNonGeographicCallingCode(callingCode)) {\r\n\t\t\treturn '001'\r\n\t\t}\r\n\t}\r\n\tconst possibleCountries = metadata.getCountryCodesForCallingCode(callingCode)\r\n\tif (!possibleCountries) {\r\n\t\treturn\r\n\t}\r\n\t// If there's just one country corresponding to the country code,\r\n\t// then just return it, without further phone number digits validation.\r\n\tif (possibleCountries.length === 1) {\r\n\t\treturn possibleCountries[0]\r\n\t}\r\n\treturn getCountryByNationalNumber(nationalPhoneNumber, {\r\n\t\tcountries: possibleCountries,\r\n\t\tdefaultCountry,\r\n\t\tmetadata: metadata.metadata\r\n\t})\r\n}"], "mappings": ";;;;;;;AAAA;;;;AAEA,IAAMA,+BAA+B,GAAG,KAAxC;;AAEe,SAASC,uBAAT,CAAiCC,WAAjC,QAIZ;EAAA,IAHcC,mBAGd,QAHFC,cAGE;EAAA,IAFFC,cAEE,QAFFA,cAEE;EAAA,IADFC,QACE,QADFA,QACE;;EACF;EACA,IAAIN,+BAAJ,EAAqC;IACpC,IAAIM,QAAQ,CAACC,0BAAT,CAAoCL,WAApC,CAAJ,EAAsD;MACrD,OAAO,KAAP;IACA;EACD;;EACD,IAAMM,iBAAiB,GAAGF,QAAQ,CAACG,6BAAT,CAAuCP,WAAvC,CAA1B;;EACA,IAAI,CAACM,iBAAL,EAAwB;IACvB;EACA,CAVC,CAWF;EACA;;;EACA,IAAIA,iBAAiB,CAACE,MAAlB,KAA6B,CAAjC,EAAoC;IACnC,OAAOF,iBAAiB,CAAC,CAAD,CAAxB;EACA;;EACD,OAAO,IAAAG,sCAAA,EAA2BR,mBAA3B,EAAgD;IACtDS,SAAS,EAAEJ,iBAD2C;IAEtDH,cAAc,EAAdA,cAFsD;IAGtDC,QAAQ,EAAEA,QAAQ,CAACA;EAHmC,CAAhD,CAAP;AAKA"}