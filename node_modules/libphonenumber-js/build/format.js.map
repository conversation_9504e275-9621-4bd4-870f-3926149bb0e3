{"version": 3, "file": "format.js", "names": ["DEFAULT_OPTIONS", "formatExtension", "formattedNumber", "extension", "metadata", "ext", "formatNumber", "input", "format", "options", "<PERSON><PERSON><PERSON>", "country", "hasCountry", "Error", "countryCallingCode", "selectNumberingPlan", "phone", "nationalNumber", "v2", "number", "formatNationalNumber", "carrierCode", "addExtension", "formatRFC3966", "fromCountry", "formatIDD", "formatAs", "chooseFormatForNumber", "formats", "formatNationalNumberUsingFormat", "useInternationalFormat", "withNationalPrefix", "nationalPrefixIsOptionalWhenFormattingInNationalFormat", "nationalPrefix", "availableFormats", "nationalNnumber", "leadingDigitsPatterns", "length", "lastLeadingDigitsPattern", "search", "matchesEntirely", "pattern", "fromCountryCallingCode", "getCountryCallingCode", "iddPrefix", "getIddPrefix", "undefined"], "sources": ["../source/format.js"], "sourcesContent": ["// This is a port of Google Android `libphonenumber`'s\r\n// `phonenumberutil.js` of December 31th, 2018.\r\n//\r\n// https://github.com/googlei18n/libphonenumber/commits/master/javascript/i18n/phonenumbers/phonenumberutil.js\r\n\r\nimport matchesEntirely from './helpers/matchesEntirely.js'\r\nimport formatNationalNumberUsingFormat from './helpers/formatNationalNumberUsingFormat.js'\r\nimport Metadata, { getCountryCallingCode } from './metadata.js'\r\nimport getIddPrefix from './helpers/getIddPrefix.js'\r\nimport { formatRFC3966 } from './helpers/RFC3966.js'\r\n\r\nconst DEFAULT_OPTIONS = {\r\n\tformatExtension: (formattedNumber, extension, metadata) => `${formattedNumber}${metadata.ext()}${extension}`\r\n}\r\n\r\n/**\r\n * Formats a phone number.\r\n *\r\n * format(phoneNumberInstance, 'INTERNATIONAL', { ..., v2: true }, metadata)\r\n * format(phoneNumberInstance, 'NATIONAL', { ..., v2: true }, metadata)\r\n *\r\n * format({ phone: '8005553535', country: 'RU' }, 'INTERNATIONAL', { ... }, metadata)\r\n * format({ phone: '8005553535', country: 'RU' }, 'NATIONAL', undefined, metadata)\r\n *\r\n * @param  {object|PhoneNumber} input — If `options.v2: true` flag is passed, the `input` should be a `PhoneNumber` instance. Otherwise, it should be an object of shape `{ phone: '...', country: '...' }`.\r\n * @param  {string} format\r\n * @param  {object} [options]\r\n * @param  {object} metadata\r\n * @return {string}\r\n */\r\nexport default function formatNumber(input, format, options, metadata) {\r\n\t// Apply default options.\r\n\tif (options) {\r\n\t\toptions = { ...DEFAULT_OPTIONS, ...options }\r\n\t} else {\r\n\t\toptions = DEFAULT_OPTIONS\r\n\t}\r\n\r\n\tmetadata = new Metadata(metadata)\r\n\r\n\tif (input.country && input.country !== '001') {\r\n\t\t// Validate `input.country`.\r\n\t\tif (!metadata.hasCountry(input.country)) {\r\n\t\t\tthrow new Error(`Unknown country: ${input.country}`)\r\n\t\t}\r\n\t\tmetadata.country(input.country)\r\n\t}\r\n\telse if (input.countryCallingCode) {\r\n\t\tmetadata.selectNumberingPlan(input.countryCallingCode)\r\n\t}\r\n\telse return input.phone || ''\r\n\r\n\tconst countryCallingCode = metadata.countryCallingCode()\r\n\r\n\tconst nationalNumber = options.v2 ? input.nationalNumber : input.phone\r\n\r\n\t// This variable should have been declared inside `case`s\r\n\t// but Babel has a bug and it says \"duplicate variable declaration\".\r\n\tlet number\r\n\r\n\tswitch (format) {\r\n\t\tcase 'NATIONAL':\r\n\t\t\t// Legacy argument support.\r\n\t\t\t// (`{ country: ..., phone: '' }`)\r\n\t\t\tif (!nationalNumber) {\r\n\t\t\t\treturn ''\r\n\t\t\t}\r\n\t\t\tnumber = formatNationalNumber(nationalNumber, input.carrierCode, 'NATIONAL', metadata, options)\r\n\t\t\treturn addExtension(number, input.ext, metadata, options.formatExtension)\r\n\r\n\t\tcase 'INTERNATIONAL':\r\n\t\t\t// Legacy argument support.\r\n\t\t\t// (`{ country: ..., phone: '' }`)\r\n\t\t\tif (!nationalNumber) {\r\n\t\t\t\treturn `+${countryCallingCode}`\r\n\t\t\t}\r\n\t\t\tnumber = formatNationalNumber(nationalNumber, null, 'INTERNATIONAL', metadata, options)\r\n\t\t\tnumber = `+${countryCallingCode} ${number}`\r\n\t\t\treturn addExtension(number, input.ext, metadata, options.formatExtension)\r\n\r\n\t\tcase 'E.164':\r\n\t\t\t// `E.164` doesn't define \"phone number extensions\".\r\n\t\t\treturn `+${countryCallingCode}${nationalNumber}`\r\n\r\n\t\tcase 'RFC3966':\r\n\t\t\treturn formatRFC3966({\r\n\t\t\t\tnumber: `+${countryCallingCode}${nationalNumber}`,\r\n\t\t\t\text: input.ext\r\n\t\t\t})\r\n\r\n\t\t// For reference, here's Google's IDD formatter:\r\n\t\t// https://github.com/google/libphonenumber/blob/32719cf74e68796788d1ca45abc85dcdc63ba5b9/java/libphonenumber/src/com/google/i18n/phonenumbers/PhoneNumberUtil.java#L1546\r\n\t\t// Not saying that this IDD formatter replicates it 1:1, but it seems to work.\r\n\t\t// Who would even need to format phone numbers in IDD format anyway?\r\n\t\tcase 'IDD':\r\n\t\t\tif (!options.fromCountry) {\r\n\t\t\t\treturn\r\n\t\t\t\t// throw new Error('`fromCountry` option not passed for IDD-prefixed formatting.')\r\n\t\t\t}\r\n\t\t\tconst formattedNumber = formatIDD(\r\n\t\t\t\tnationalNumber,\r\n\t\t\t\tinput.carrierCode,\r\n\t\t\t\tcountryCallingCode,\r\n\t\t\t\toptions.fromCountry,\r\n\t\t\t\tmetadata\r\n\t\t\t)\r\n\t\t\treturn addExtension(formattedNumber, input.ext, metadata, options.formatExtension)\r\n\r\n\t\tdefault:\r\n\t\t\tthrow new Error(`Unknown \"format\" argument passed to \"formatNumber()\": \"${format}\"`)\r\n\t}\r\n}\r\n\r\nfunction formatNationalNumber(number, carrierCode, formatAs, metadata, options) {\r\n\tconst format = chooseFormatForNumber(metadata.formats(), number)\r\n\tif (!format) {\r\n\t\treturn number\r\n\t}\r\n\treturn formatNationalNumberUsingFormat(\r\n\t\tnumber,\r\n\t\tformat,\r\n\t\t{\r\n\t\t\tuseInternationalFormat: formatAs === 'INTERNATIONAL',\r\n\t\t\twithNationalPrefix: format.nationalPrefixIsOptionalWhenFormattingInNationalFormat() && (options && options.nationalPrefix === false) ? false : true,\r\n\t\t\tcarrierCode,\r\n\t\t\tmetadata\r\n\t\t}\r\n\t)\r\n}\r\n\r\nexport function chooseFormatForNumber(availableFormats, nationalNnumber) {\r\n\tfor (const format of availableFormats) {\r\n\t\t// Validate leading digits.\r\n\t\t// The test case for \"else path\" could be found by searching for\r\n\t\t// \"format.leadingDigitsPatterns().length === 0\".\r\n\t\tif (format.leadingDigitsPatterns().length > 0) {\r\n\t\t\t// The last leading_digits_pattern is used here, as it is the most detailed\r\n\t\t\tconst lastLeadingDigitsPattern = format.leadingDigitsPatterns()[format.leadingDigitsPatterns().length - 1]\r\n\t\t\t// If leading digits don't match then move on to the next phone number format\r\n\t\t\tif (nationalNnumber.search(lastLeadingDigitsPattern) !== 0) {\r\n\t\t\t\tcontinue\r\n\t\t\t}\r\n\t\t}\r\n\t\t// Check that the national number matches the phone number format regular expression\r\n\t\tif (matchesEntirely(nationalNnumber, format.pattern())) {\r\n\t\t\treturn format\r\n\t\t}\r\n\t}\r\n}\r\n\r\nfunction addExtension(formattedNumber, ext, metadata, formatExtension) {\r\n\treturn ext ? formatExtension(formattedNumber, ext, metadata) : formattedNumber\r\n}\r\n\r\nfunction formatIDD(\r\n\tnationalNumber,\r\n\tcarrierCode,\r\n\tcountryCallingCode,\r\n\tfromCountry,\r\n\tmetadata\r\n) {\r\n\tconst fromCountryCallingCode = getCountryCallingCode(fromCountry, metadata.metadata)\r\n\t// When calling within the same country calling code.\r\n\tif (fromCountryCallingCode === countryCallingCode) {\r\n\t\tconst formattedNumber = formatNationalNumber(nationalNumber, carrierCode, 'NATIONAL', metadata)\r\n\t\t// For NANPA regions, return the national format for these regions\r\n\t\t// but prefix it with the country calling code.\r\n\t\tif (countryCallingCode === '1') {\r\n\t\t\treturn countryCallingCode + ' ' + formattedNumber\r\n\t\t}\r\n\t\t// If regions share a country calling code, the country calling code need\r\n\t\t// not be dialled. This also applies when dialling within a region, so this\r\n\t\t// if clause covers both these cases. Technically this is the case for\r\n\t\t// dialling from La Reunion to other overseas departments of France (French\r\n\t\t// Guiana, Martinique, Guadeloupe), but not vice versa - so we don't cover\r\n\t\t// this edge case for now and for those cases return the version including\r\n\t\t// country calling code. Details here:\r\n\t\t// http://www.petitfute.com/voyage/225-info-pratiques-reunion\r\n\t\t//\r\n\t\treturn formattedNumber\r\n\t}\r\n\tconst iddPrefix = getIddPrefix(fromCountry, undefined, metadata.metadata)\r\n\tif (iddPrefix) {\r\n\t\treturn `${iddPrefix} ${countryCallingCode} ${formatNationalNumber(nationalNumber, null, 'INTERNATIONAL', metadata)}`\r\n\t}\r\n}"], "mappings": ";;;;;;;;;;AAKA;;AACA;;AACA;;AACA;;AACA;;;;;;;;;;;;;;;;;;;;AAEA,IAAMA,eAAe,GAAG;EACvBC,eAAe,EAAE,yBAACC,eAAD,EAAkBC,SAAlB,EAA6BC,QAA7B;IAAA,iBAA6CF,eAA7C,SAA+DE,QAAQ,CAACC,GAAT,EAA/D,SAAgFF,SAAhF;EAAA;AADM,CAAxB;AAIA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AACe,SAASG,YAAT,CAAsBC,KAAtB,EAA6BC,MAA7B,EAAqCC,OAArC,EAA8CL,QAA9C,EAAwD;EACtE;EACA,IAAIK,OAAJ,EAAa;IACZA,OAAO,mCAAQT,eAAR,GAA4BS,OAA5B,CAAP;EACA,CAFD,MAEO;IACNA,OAAO,GAAGT,eAAV;EACA;;EAEDI,QAAQ,GAAG,IAAIM,oBAAJ,CAAaN,QAAb,CAAX;;EAEA,IAAIG,KAAK,CAACI,OAAN,IAAiBJ,KAAK,CAACI,OAAN,KAAkB,KAAvC,EAA8C;IAC7C;IACA,IAAI,CAACP,QAAQ,CAACQ,UAAT,CAAoBL,KAAK,CAACI,OAA1B,CAAL,EAAyC;MACxC,MAAM,IAAIE,KAAJ,4BAA8BN,KAAK,CAACI,OAApC,EAAN;IACA;;IACDP,QAAQ,CAACO,OAAT,CAAiBJ,KAAK,CAACI,OAAvB;EACA,CAND,MAOK,IAAIJ,KAAK,CAACO,kBAAV,EAA8B;IAClCV,QAAQ,CAACW,mBAAT,CAA6BR,KAAK,CAACO,kBAAnC;EACA,CAFI,MAGA,OAAOP,KAAK,CAACS,KAAN,IAAe,EAAtB;;EAEL,IAAMF,kBAAkB,GAAGV,QAAQ,CAACU,kBAAT,EAA3B;EAEA,IAAMG,cAAc,GAAGR,OAAO,CAACS,EAAR,GAAaX,KAAK,CAACU,cAAnB,GAAoCV,KAAK,CAACS,KAAjE,CAxBsE,CA0BtE;EACA;;EACA,IAAIG,MAAJ;;EAEA,QAAQX,MAAR;IACC,KAAK,UAAL;MACC;MACA;MACA,IAAI,CAACS,cAAL,EAAqB;QACpB,OAAO,EAAP;MACA;;MACDE,MAAM,GAAGC,oBAAoB,CAACH,cAAD,EAAiBV,KAAK,CAACc,WAAvB,EAAoC,UAApC,EAAgDjB,QAAhD,EAA0DK,OAA1D,CAA7B;MACA,OAAOa,YAAY,CAACH,MAAD,EAASZ,KAAK,CAACF,GAAf,EAAoBD,QAApB,EAA8BK,OAAO,CAACR,eAAtC,CAAnB;;IAED,KAAK,eAAL;MACC;MACA;MACA,IAAI,CAACgB,cAAL,EAAqB;QACpB,kBAAWH,kBAAX;MACA;;MACDK,MAAM,GAAGC,oBAAoB,CAACH,cAAD,EAAiB,IAAjB,EAAuB,eAAvB,EAAwCb,QAAxC,EAAkDK,OAAlD,CAA7B;MACAU,MAAM,cAAOL,kBAAP,cAA6BK,MAA7B,CAAN;MACA,OAAOG,YAAY,CAACH,MAAD,EAASZ,KAAK,CAACF,GAAf,EAAoBD,QAApB,EAA8BK,OAAO,CAACR,eAAtC,CAAnB;;IAED,KAAK,OAAL;MACC;MACA,kBAAWa,kBAAX,SAAgCG,cAAhC;;IAED,KAAK,SAAL;MACC,OAAO,IAAAM,kBAAA,EAAc;QACpBJ,MAAM,aAAML,kBAAN,SAA2BG,cAA3B,CADc;QAEpBZ,GAAG,EAAEE,KAAK,CAACF;MAFS,CAAd,CAAP;IAKD;IACA;IACA;IACA;;IACA,KAAK,KAAL;MACC,IAAI,CAACI,OAAO,CAACe,WAAb,EAA0B;QACzB,OADyB,CAEzB;MACA;;MACD,IAAMtB,eAAe,GAAGuB,SAAS,CAChCR,cADgC,EAEhCV,KAAK,CAACc,WAF0B,EAGhCP,kBAHgC,EAIhCL,OAAO,CAACe,WAJwB,EAKhCpB,QALgC,CAAjC;MAOA,OAAOkB,YAAY,CAACpB,eAAD,EAAkBK,KAAK,CAACF,GAAxB,EAA6BD,QAA7B,EAAuCK,OAAO,CAACR,eAA/C,CAAnB;;IAED;MACC,MAAM,IAAIY,KAAJ,uEAAoEL,MAApE,QAAN;EAjDF;AAmDA;;AAED,SAASY,oBAAT,CAA8BD,MAA9B,EAAsCE,WAAtC,EAAmDK,QAAnD,EAA6DtB,QAA7D,EAAuEK,OAAvE,EAAgF;EAC/E,IAAMD,MAAM,GAAGmB,qBAAqB,CAACvB,QAAQ,CAACwB,OAAT,EAAD,EAAqBT,MAArB,CAApC;;EACA,IAAI,CAACX,MAAL,EAAa;IACZ,OAAOW,MAAP;EACA;;EACD,OAAO,IAAAU,2CAAA,EACNV,MADM,EAENX,MAFM,EAGN;IACCsB,sBAAsB,EAAEJ,QAAQ,KAAK,eADtC;IAECK,kBAAkB,EAAEvB,MAAM,CAACwB,sDAAP,MAAoEvB,OAAO,IAAIA,OAAO,CAACwB,cAAR,KAA2B,KAA1G,GAAmH,KAAnH,GAA2H,IAFhJ;IAGCZ,WAAW,EAAXA,WAHD;IAICjB,QAAQ,EAARA;EAJD,CAHM,CAAP;AAUA;;AAEM,SAASuB,qBAAT,CAA+BO,gBAA/B,EAAiDC,eAAjD,EAAkE;EACxE,qDAAqBD,gBAArB,wCAAuC;IAAA,IAA5B1B,MAA4B;;IACtC;IACA;IACA;IACA,IAAIA,MAAM,CAAC4B,qBAAP,GAA+BC,MAA/B,GAAwC,CAA5C,EAA+C;MAC9C;MACA,IAAMC,wBAAwB,GAAG9B,MAAM,CAAC4B,qBAAP,GAA+B5B,MAAM,CAAC4B,qBAAP,GAA+BC,MAA/B,GAAwC,CAAvE,CAAjC,CAF8C,CAG9C;;MACA,IAAIF,eAAe,CAACI,MAAhB,CAAuBD,wBAAvB,MAAqD,CAAzD,EAA4D;QAC3D;MACA;IACD,CAXqC,CAYtC;;;IACA,IAAI,IAAAE,2BAAA,EAAgBL,eAAhB,EAAiC3B,MAAM,CAACiC,OAAP,EAAjC,CAAJ,EAAwD;MACvD,OAAOjC,MAAP;IACA;EACD;AACD;;AAED,SAASc,YAAT,CAAsBpB,eAAtB,EAAuCG,GAAvC,EAA4CD,QAA5C,EAAsDH,eAAtD,EAAuE;EACtE,OAAOI,GAAG,GAAGJ,eAAe,CAACC,eAAD,EAAkBG,GAAlB,EAAuBD,QAAvB,CAAlB,GAAqDF,eAA/D;AACA;;AAED,SAASuB,SAAT,CACCR,cADD,EAECI,WAFD,EAGCP,kBAHD,EAICU,WAJD,EAKCpB,QALD,EAME;EACD,IAAMsC,sBAAsB,GAAG,IAAAC,+BAAA,EAAsBnB,WAAtB,EAAmCpB,QAAQ,CAACA,QAA5C,CAA/B,CADC,CAED;;EACA,IAAIsC,sBAAsB,KAAK5B,kBAA/B,EAAmD;IAClD,IAAMZ,eAAe,GAAGkB,oBAAoB,CAACH,cAAD,EAAiBI,WAAjB,EAA8B,UAA9B,EAA0CjB,QAA1C,CAA5C,CADkD,CAElD;IACA;;IACA,IAAIU,kBAAkB,KAAK,GAA3B,EAAgC;MAC/B,OAAOA,kBAAkB,GAAG,GAArB,GAA2BZ,eAAlC;IACA,CANiD,CAOlD;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;;;IACA,OAAOA,eAAP;EACA;;EACD,IAAM0C,SAAS,GAAG,IAAAC,wBAAA,EAAarB,WAAb,EAA0BsB,SAA1B,EAAqC1C,QAAQ,CAACA,QAA9C,CAAlB;;EACA,IAAIwC,SAAJ,EAAe;IACd,iBAAUA,SAAV,cAAuB9B,kBAAvB,cAA6CM,oBAAoB,CAACH,cAAD,EAAiB,IAAjB,EAAuB,eAAvB,EAAwCb,QAAxC,CAAjE;EACA;AACD"}