{"version": 3, "file": "findPhoneNumbersInText.js", "names": ["findPhoneNumbersInText", "normalizeArguments", "arguments", "text", "options", "metadata", "matcher", "PhoneNumberMatcher", "v2", "results", "hasNext", "push", "next"], "sources": ["../source/findPhoneNumbersInText.js"], "sourcesContent": ["import PhoneNumberMatcher from './PhoneNumberMatcher.js'\r\nimport normalizeArguments from './normalizeArguments.js'\r\n\r\nexport default function findPhoneNumbersInText() {\r\n\tconst { text, options, metadata } = normalizeArguments(arguments)\r\n\tconst matcher = new PhoneNumberMatcher(text, { ...options, v2: true }, metadata)\r\n\tconst results = []\r\n\twhile (matcher.hasNext()) {\r\n\t\tresults.push(matcher.next())\r\n\t}\r\n\treturn results\r\n}"], "mappings": ";;;;;;;AAAA;;AACA;;;;;;;;;;AAEe,SAASA,sBAAT,GAAkC;EAChD,0BAAoC,IAAAC,+BAAA,EAAmBC,SAAnB,CAApC;EAAA,IAAQC,IAAR,uBAAQA,IAAR;EAAA,IAAcC,OAAd,uBAAcA,OAAd;EAAA,IAAuBC,QAAvB,uBAAuBA,QAAvB;;EACA,IAAMC,OAAO,GAAG,IAAIC,8BAAJ,CAAuBJ,IAAvB,kCAAkCC,OAAlC;IAA2CI,EAAE,EAAE;EAA/C,IAAuDH,QAAvD,CAAhB;EACA,IAAMI,OAAO,GAAG,EAAhB;;EACA,OAAOH,OAAO,CAACI,OAAR,EAAP,EAA0B;IACzBD,OAAO,CAACE,IAAR,CAAaL,OAAO,CAACM,IAAR,EAAb;EACA;;EACD,OAAOH,OAAP;AACA"}