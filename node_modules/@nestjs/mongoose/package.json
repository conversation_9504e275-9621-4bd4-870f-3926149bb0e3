{"name": "@nestjs/mongoose", "version": "10.1.0", "description": "Nest - modern, fast, powerful node.js web framework (@mongoose)", "author": "<PERSON><PERSON><PERSON>", "repository": "https://github.com/nestjs/mongoose.git", "license": "MIT", "main": "dist/index.js", "types": "dist/index.d.ts", "scripts": {"lint": "eslint \"lib/**/*.ts\" --fix", "format": "prettier \"lib/**/*.ts\" --write", "build": "rm -rf dist && tsc -p tsconfig.build.json", "precommit": "lint-staged", "prepublish:npm": "npm run build", "publish:npm": "npm publish --access public", "prepublish:next": "npm run build", "publish:next": "npm publish --access public --tag next", "prerelease": "npm run build", "release": "release-it", "test:e2e": "jest --config ./tests/jest-e2e.json --runInBand", "test:e2e:dev": "jest --config ./tests/jest-e2e.json --runInBand --watch", "prepare": "husky"}, "devDependencies": {"@commitlint/cli": "19.5.0", "@commitlint/config-angular": "19.5.0", "@nestjs/common": "10.4.5", "@nestjs/core": "10.4.5", "@nestjs/platform-express": "10.4.5", "@nestjs/testing": "10.4.5", "@types/jest": "29.5.13", "@types/node": "20.16.13", "@typescript-eslint/eslint-plugin": "8.10.0", "@typescript-eslint/parser": "8.10.0", "eslint": "9.13.0", "eslint-config-prettier": "9.1.0", "eslint-plugin-import": "2.31.0", "eslint-plugin-prettier": "5.2.1", "husky": "9.1.6", "jest": "29.7.0", "lint-staged": "15.2.10", "mongoose": "8.7.2", "prettier": "3.3.3", "reflect-metadata": "0.2.2", "release-it": "^17.4.0", "rxjs": "7.8.1", "supertest": "7.0.0", "ts-jest": "29.2.5", "ts-node": "10.9.2", "typescript": "5.6.3"}, "peerDependencies": {"@nestjs/common": "^8.0.0 || ^9.0.0 || ^10.0.0", "@nestjs/core": "^8.0.0 || ^9.0.0 || ^10.0.0", "mongoose": "^6.0.2 || ^7.0.0 || ^8.0.0", "rxjs": "^7.0.0"}, "lint-staged": {"**/*.{ts,json}": []}}