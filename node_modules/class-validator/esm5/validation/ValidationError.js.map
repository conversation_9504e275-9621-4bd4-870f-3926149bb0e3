{"version": 3, "file": "ValidationError.js", "sourceRoot": "", "sources": ["../../../src/validation/ValidationError.ts"], "names": [], "mappings": "AAAA;;GAEG;AACH;IAAA;IA0FA,CAAC;IAnDC;;;;;;OAMG;IACH,kCAAQ,GAAR,UACE,cAA+B,EAC/B,SAA0B,EAC1B,UAAuB,EACvB,sBAAuC;QAJzC,iBA2CC;QA1CC,+BAAA,EAAA,sBAA+B;QAC/B,0BAAA,EAAA,iBAA0B;QAC1B,2BAAA,EAAA,eAAuB;QACvB,uCAAA,EAAA,8BAAuC;QAEvC,IAAM,SAAS,GAAG,cAAc,CAAC,CAAC,CAAC,WAAS,CAAC,CAAC,CAAC,EAAE,CAAC;QAClD,IAAM,OAAO,GAAG,cAAc,CAAC,CAAC,CAAC,YAAU,CAAC,CAAC,CAAC,EAAE,CAAC;QACjD,IAAM,mBAAmB,GAAG,sBAC1B,OAAA,CAAC,sBAAsB,CAAC,CAAC,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,MAAA,KAAI,CAAC,WAAW,mCAAI,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA,EAAA,CAAC;QAC5F,IAAM,oBAAoB,GAAG,UAAC,YAAoB;YAChD,OAAA,sBAAe,SAAS,SAAG,UAAU,SAAG,YAAY,SAAG,OAAO,oDAA0C,SAAS,SAAG,mBAAmB,EAAE,SAAG,OAAO,QAAK;QAAxJ,CAAwJ,CAAC;QAE3J,IAAI,CAAC,SAAS,EAAE,CAAC;YACf,OAAO,CACL,yBAAkB,SAAS,SACzB,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC,CAAC,WAAW,SACvD,OAAO,kCAA+B;gBACzC,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,oBAAoB,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;gBAC7D,CAAC,IAAI,CAAC,QAAQ;oBACZ,CAAC,CAAC,IAAI,CAAC,QAAQ;yBACV,GAAG,CAAC,UAAA,UAAU,IAAI,OAAA,UAAU,CAAC,QAAQ,CAAC,cAAc,EAAE,IAAI,EAAE,KAAI,CAAC,QAAQ,EAAE,sBAAsB,CAAC,EAAhF,CAAgF,CAAC;yBACnG,IAAI,CAAC,EAAE,CAAC;oBACb,CAAC,CAAC,EAAE,CAAC,CACR,CAAC;QACJ,CAAC;aAAM,CAAC;YACN,6DAA6D;YAC7D,IAAM,mBAAiB,GAAG,MAAM,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC;gBACxD,CAAC,CAAC,WAAI,IAAI,CAAC,QAAQ,MAAG;gBACtB,CAAC,CAAC,UAAG,UAAU,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,SAAG,IAAI,CAAC,QAAQ,CAAE,CAAC;YAE/C,IAAI,IAAI,CAAC,WAAW,EAAE,CAAC;gBACrB,OAAO,oBAAoB,CAAC,mBAAiB,CAAC,CAAC;YACjD,CAAC;iBAAM,CAAC;gBACN,OAAO,IAAI,CAAC,QAAQ;oBAClB,CAAC,CAAC,IAAI,CAAC,QAAQ;yBACV,GAAG,CAAC,UAAA,UAAU;wBACb,OAAA,UAAU,CAAC,QAAQ,CAAC,cAAc,EAAE,IAAI,EAAE,UAAG,UAAU,SAAG,mBAAiB,CAAE,EAAE,sBAAsB,CAAC;oBAAtG,CAAsG,CACvG;yBACA,IAAI,CAAC,EAAE,CAAC;oBACb,CAAC,CAAC,EAAE,CAAC;YACT,CAAC;QACH,CAAC;IACH,CAAC;IACH,sBAAC;AAAD,CAAC,AA1FD,IA0FC", "sourcesContent": ["/**\n * Validation error description.\n */\nexport class ValidationError {\n  /**\n   * Object that was validated.\n   *\n   * OPTIONAL - configurable via the ValidatorOptions.validationError.target option\n   */\n  target?: object;\n\n  /**\n   * Object's property that haven't pass validation.\n   */\n  property: string;\n\n  /**\n   * Value that haven't pass a validation.\n   *\n   * OPTIONAL - configurable via the ValidatorOptions.validationError.value option\n   */\n  value?: any;\n\n  /**\n   * Constraints that failed validation with error messages.\n   */\n  constraints?: {\n    [type: string]: string;\n  };\n\n  /**\n   * Contains all nested validation errors of the property.\n   */\n  children?: ValidationError[];\n\n  /*\n   * A transient set of data passed through to the validation result for response mapping\n   */\n  contexts?: {\n    [type: string]: any;\n  };\n\n  /**\n   *\n   * @param shouldDecorate decorate the message with ANSI formatter escape codes for better readability\n   * @param hasParent true when the error is a child of an another one\n   * @param parentPath path as string to the parent of this property\n   * @param showConstraintMessages show constraint messages instead of constraint names\n   */\n  toString(\n    shouldDecorate: boolean = false,\n    hasParent: boolean = false,\n    parentPath: string = ``,\n    showConstraintMessages: boolean = false\n  ): string {\n    const boldStart = shouldDecorate ? `\\x1b[1m` : ``;\n    const boldEnd = shouldDecorate ? `\\x1b[22m` : ``;\n    const constraintsToString = () =>\n      (showConstraintMessages ? Object.values : Object.keys)(this.constraints ?? {}).join(`, `);\n    const propConstraintFailed = (propertyName: string): string =>\n      ` - property ${boldStart}${parentPath}${propertyName}${boldEnd} has failed the following constraints: ${boldStart}${constraintsToString()}${boldEnd} \\n`;\n\n    if (!hasParent) {\n      return (\n        `An instance of ${boldStart}${\n          this.target ? this.target.constructor.name : 'an object'\n        }${boldEnd} has failed the validation:\\n` +\n        (this.constraints ? propConstraintFailed(this.property) : ``) +\n        (this.children\n          ? this.children\n              .map(childError => childError.toString(shouldDecorate, true, this.property, showConstraintMessages))\n              .join(``)\n          : ``)\n      );\n    } else {\n      // we format numbers as array indexes for better readability.\n      const formattedProperty = Number.isInteger(+this.property)\n        ? `[${this.property}]`\n        : `${parentPath ? `.` : ``}${this.property}`;\n\n      if (this.constraints) {\n        return propConstraintFailed(formattedProperty);\n      } else {\n        return this.children\n          ? this.children\n              .map(childError =>\n                childError.toString(shouldDecorate, true, `${parentPath}${formattedProperty}`, showConstraintMessages)\n              )\n              .join(``)\n          : ``;\n      }\n    }\n  }\n}\n"]}