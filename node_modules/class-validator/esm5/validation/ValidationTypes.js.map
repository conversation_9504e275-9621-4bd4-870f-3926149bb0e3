{"version": 3, "file": "ValidationTypes.js", "sourceRoot": "", "sources": ["../../../src/validation/ValidationTypes.ts"], "names": [], "mappings": "AAAA;;GAEG;AACH;IAAA;IAqBA,CAAC;IAZC;;OAEG;IACI,uBAAO,GAAd,UAAe,IAAY;QAA3B,iBAQC;QAPC,OAAO,CACL,IAAI,KAAK,SAAS;YAClB,IAAI,KAAK,YAAY;YACrB,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC;iBACd,GAAG,CAAC,UAAA,GAAG,IAAI,OAAC,KAAY,CAAC,GAAG,CAAC,EAAlB,CAAkB,CAAC;iBAC9B,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CACxB,CAAC;IACJ,CAAC;IAnBD,YAAY;IACL,iCAAiB,GAAG,kBAAkB,CAAC,CAAC,OAAO;IAC/C,iCAAiB,GAAG,kBAAkB,CAAC,CAAC,OAAO;IAC/C,kCAAkB,GAAG,mBAAmB,CAAC,CAAC,OAAO;IACjD,sCAAsB,GAAG,uBAAuB,CAAC,CAAC,OAAO;IACzD,yBAAS,GAAG,qBAAqB,CAAC,CAAC,OAAO;IAC1C,0BAAU,GAAG,WAAW,CAAC,CAAC,OAAO;IAc1C,sBAAC;CAAA,AArBD,IAqBC;SArBY,eAAe", "sourcesContent": ["/**\n * Validation types.\n */\nexport class ValidationTypes {\n  /* system */\n  static CUSTOM_VALIDATION = 'customValidation'; // done\n  static NESTED_VALIDATION = 'nestedValidation'; // done\n  static PROMISE_VALIDATION = 'promiseValidation'; // done\n  static CONDITIONAL_VALIDATION = 'conditionalValidation'; // done\n  static WHITELIST = 'whitelistValidation'; // done\n  static IS_DEFINED = 'isDefined'; // done\n\n  /**\n   * Checks if validation type is valid.\n   */\n  static isValid(type: string): boolean {\n    return (\n      type !== 'isValid' &&\n      type !== 'getMessage' &&\n      Object.keys(this)\n        .map(key => (this as any)[key])\n        .indexOf(type) !== -1\n    );\n  }\n}\n"]}