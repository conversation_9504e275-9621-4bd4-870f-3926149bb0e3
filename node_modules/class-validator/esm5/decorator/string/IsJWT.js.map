{"version": 3, "file": "IsJWT.js", "sourceRoot": "", "sources": ["../../../../src/decorator/string/IsJWT.ts"], "names": [], "mappings": "AACA,OAAO,EAAE,YAAY,EAAE,UAAU,EAAE,MAAM,sBAAsB,CAAC;AAChE,OAAO,cAAc,MAAM,qBAAqB,CAAC;AAEjD,MAAM,CAAC,IAAM,MAAM,GAAG,OAAO,CAAC;AAE9B;;;GAGG;AACH,MAAM,UAAU,KAAK,CAAC,KAAc;IAClC,OAAO,OAAO,KAAK,KAAK,QAAQ,IAAI,cAAc,CAAC,KAAK,CAAC,CAAC;AAC5D,CAAC;AAED;;;GAGG;AACH,MAAM,UAAU,KAAK,CAAC,iBAAqC;IACzD,OAAO,UAAU,CACf;QACE,IAAI,EAAE,MAAM;QACZ,SAAS,EAAE;YACT,QAAQ,EAAE,UAAC,KAAK,EAAE,IAAI,IAAc,OAAA,KAAK,CAAC,KAAK,CAAC,EAAZ,CAAY;YAChD,cAAc,EAAE,YAAY,CAAC,UAAA,UAAU,IAAI,OAAA,UAAU,GAAG,gCAAgC,EAA7C,CAA6C,EAAE,iBAAiB,CAAC;SAC7G;KACF,EACD,iBAAiB,CAClB,CAAC;AACJ,CAAC", "sourcesContent": ["import { ValidationOptions } from '../ValidationOptions';\nimport { buildMessage, ValidateBy } from '../common/ValidateBy';\nimport isJwtValidator from 'validator/lib/isJWT';\n\nexport const IS_JWT = 'isJwt';\n\n/**\n * Checks if the string is valid JWT token.\n * If given value is not a string, then it returns false.\n */\nexport function isJWT(value: unknown): boolean {\n  return typeof value === 'string' && isJwtValidator(value);\n}\n\n/**\n * Checks if the string is valid JWT token.\n * If given value is not a string, then it returns false.\n */\nexport function IsJWT(validationOptions?: ValidationOptions): PropertyDecorator {\n  return ValidateBy(\n    {\n      name: IS_JWT,\n      validator: {\n        validate: (value, args): boolean => isJWT(value),\n        defaultMessage: buildMessage(eachPrefix => eachPrefix + '$property must be a jwt string', validationOptions),\n      },\n    },\n    validationOptions\n  );\n}\n"]}