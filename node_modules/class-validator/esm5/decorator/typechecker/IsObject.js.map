{"version": 3, "file": "IsObject.js", "sourceRoot": "", "sources": ["../../../../src/decorator/typechecker/IsObject.ts"], "names": [], "mappings": "AACA,OAAO,EAAE,YAAY,EAAE,UAAU,EAAE,MAAM,sBAAsB,CAAC;AAEhE,MAAM,CAAC,IAAM,SAAS,GAAG,UAAU,CAAC;AAEpC;;;GAGG;AACH,MAAM,UAAU,QAAQ,CAAa,KAAc;IACjD,OAAO,KAAK,IAAI,IAAI,IAAI,CAAC,OAAO,KAAK,KAAK,QAAQ,IAAI,OAAO,KAAK,KAAK,UAAU,CAAC,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;AAC9G,CAAC;AAED;;;GAGG;AACH,MAAM,UAAU,QAAQ,CAAC,iBAAqC;IAC5D,OAAO,UAAU,CACf;QACE,IAAI,EAAE,SAAS;QACf,SAAS,EAAE;YACT,QAAQ,EAAE,UAAC,KAAK,EAAE,IAAI,IAAc,OAAA,QAAQ,CAAC,KAAK,CAAC,EAAf,CAAe;YACnD,cAAc,EAAE,YAAY,CAAC,UAAA,UAAU,IAAI,OAAA,UAAU,GAAG,6BAA6B,EAA1C,CAA0C,EAAE,iBAAiB,CAAC;SAC1G;KACF,EACD,iBAAiB,CAClB,CAAC;AACJ,CAAC", "sourcesContent": ["import { ValidationOptions } from '../ValidationOptions';\nimport { buildMessage, ValidateBy } from '../common/ValidateBy';\n\nexport const IS_OBJECT = 'isObject';\n\n/**\n * Checks if the value is valid Object.\n * Returns false if the value is not an object.\n */\nexport function isObject<T = object>(value: unknown): value is T {\n  return value != null && (typeof value === 'object' || typeof value === 'function') && !Array.isArray(value);\n}\n\n/**\n * Checks if the value is valid Object.\n * Returns false if the value is not an object.\n */\nexport function IsObject(validationOptions?: ValidationOptions): PropertyDecorator {\n  return ValidateBy(\n    {\n      name: IS_OBJECT,\n      validator: {\n        validate: (value, args): boolean => isObject(value),\n        defaultMessage: buildMessage(eachPrefix => eachPrefix + '$property must be an object', validationOptions),\n      },\n    },\n    validationOptions\n  );\n}\n"]}