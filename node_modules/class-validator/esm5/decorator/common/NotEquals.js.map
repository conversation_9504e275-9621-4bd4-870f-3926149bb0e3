{"version": 3, "file": "NotEquals.js", "sourceRoot": "", "sources": ["../../../../src/decorator/common/NotEquals.ts"], "names": [], "mappings": "AACA,OAAO,EAAE,YAAY,EAAE,UAAU,EAAE,MAAM,sBAAsB,CAAC;AAEhE,MAAM,CAAC,IAAM,UAAU,GAAG,WAAW,CAAC;AAEtC;;GAEG;AACH,MAAM,UAAU,SAAS,CAAC,KAAc,EAAE,UAAmB;IAC3D,OAAO,KAAK,KAAK,UAAU,CAAC;AAC9B,CAAC;AAED;;GAEG;AACH,MAAM,UAAU,SAAS,CAAC,UAAe,EAAE,iBAAqC;IAC9E,OAAO,UAAU,CACf;QACE,IAAI,EAAE,UAAU;QAChB,WAAW,EAAE,CAAC,UAAU,CAAC;QACzB,SAAS,EAAE;YACT,QAAQ,EAAE,UAAC,KAAK,EAAE,IAAI,IAAc,OAAA,SAAS,CAAC,KAAK,EAAE,IAAI,aAAJ,IAAI,uBAAJ,IAAI,CAAE,WAAW,CAAC,CAAC,CAAC,CAAC,EAAtC,CAAsC;YAC1E,cAAc,EAAE,YAAY,CAC1B,UAAA,UAAU,IAAI,OAAA,UAAU,GAAG,+CAA+C,EAA5D,CAA4D,EAC1E,iBAAiB,CAClB;SACF;KACF,EACD,iBAAiB,CAClB,CAAC;AACJ,CAAC", "sourcesContent": ["import { ValidationOptions } from '../ValidationOptions';\nimport { buildMessage, ValidateBy } from '../common/ValidateBy';\n\nexport const NOT_EQUALS = 'notEquals';\n\n/**\n * Checks if value does not match (\"!==\") the comparison.\n */\nexport function notEquals(value: unknown, comparison: unknown): boolean {\n  return value !== comparison;\n}\n\n/**\n * Checks if value does not match (\"!==\") the comparison.\n */\nexport function NotEquals(comparison: any, validationOptions?: ValidationOptions): PropertyDecorator {\n  return ValidateBy(\n    {\n      name: NOT_EQUALS,\n      constraints: [comparison],\n      validator: {\n        validate: (value, args): boolean => notEquals(value, args?.constraints[0]),\n        defaultMessage: buildMessage(\n          eachPrefix => eachPrefix + '$property should not be equal to $constraint1',\n          validationOptions\n        ),\n      },\n    },\n    validationOptions\n  );\n}\n"]}