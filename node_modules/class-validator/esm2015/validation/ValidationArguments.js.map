{"version": 3, "file": "ValidationArguments.js", "sourceRoot": "", "sources": ["../../../src/validation/ValidationArguments.ts"], "names": [], "mappings": "", "sourcesContent": ["/**\n * Arguments being sent to message builders - user can create message either by simply returning a string,\n * either by returning a function that accepts MessageArguments and returns a message string built based on these arguments.\n */\nexport interface ValidationArguments {\n  /**\n   * Validating value.\n   */\n  value: any;\n\n  /**\n   * Constraints set by this validation type.\n   */\n  constraints: any[];\n\n  /**\n   * Name of the target that is being validated.\n   */\n  targetName: string;\n\n  /**\n   * Object that is being validated.\n   */\n  object: object;\n\n  /**\n   * Name of the object's property being validated.\n   */\n  property: string;\n}\n"]}