{"version": 3, "file": "ValidatorConstraintInterface.js", "sourceRoot": "", "sources": ["../../../src/validation/ValidatorConstraintInterface.ts"], "names": [], "mappings": "", "sourcesContent": ["import { ValidationArguments } from './ValidationArguments';\n/**\n * Custom validators must implement this interface to provide custom validation logic.\n */\nexport interface ValidatorConstraintInterface {\n  /**\n   * Method to be called to perform custom validation over given value.\n   */\n  validate(value: any, validationArguments?: ValidationArguments): Promise<boolean> | boolean;\n\n  /**\n   * Gets default message when validation for this constraint fail.\n   */\n  defaultMessage?(validationArguments?: ValidationArguments): string;\n}\n"]}