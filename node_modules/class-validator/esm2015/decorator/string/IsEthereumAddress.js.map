{"version": 3, "file": "IsEthereumAddress.js", "sourceRoot": "", "sources": ["../../../../src/decorator/string/IsEthereumAddress.ts"], "names": [], "mappings": "AACA,OAAO,EAAE,YAAY,EAAE,UAAU,EAAE,MAAM,sBAAsB,CAAC;AAChE,OAAO,0BAA0B,MAAM,iCAAiC,CAAC;AAEzE,MAAM,CAAC,MAAM,mBAAmB,GAAG,mBAAmB,CAAC;AAEvD;;;GAGG;AACH,MAAM,UAAU,iBAAiB,CAAC,KAAc;IAC9C,OAAO,OAAO,KAAK,KAAK,QAAQ,IAAI,0BAA0B,CAAC,KAAK,CAAC,CAAC;AACxE,CAAC;AAED;;;GAGG;AACH,MAAM,UAAU,iBAAiB,CAAC,iBAAqC;IACrE,OAAO,UAAU,CACf;QACE,IAAI,EAAE,mBAAmB;QACzB,SAAS,EAAE;YACT,QAAQ,EAAE,CAAC,KAAK,EAAE,IAAI,EAAW,EAAE,CAAC,iBAAiB,CAAC,KAAK,CAAC;YAC5D,cAAc,EAAE,YAAY,CAC1B,UAAU,CAAC,EAAE,CAAC,UAAU,GAAG,uCAAuC,EAClE,iBAAiB,CAClB;SACF;KACF,EACD,iBAAiB,CAClB,CAAC;AACJ,CAAC", "sourcesContent": ["import { ValidationOptions } from '../ValidationOptions';\nimport { buildMessage, ValidateBy } from '../common/ValidateBy';\nimport isEthereumAddressValidator from 'validator/lib/isEthereumAddress';\n\nexport const IS_ETHEREUM_ADDRESS = 'isEthereumAddress';\n\n/**\n * Check if the string is an Ethereum address using basic regex. Does not validate address checksums.\n * If given value is not a string, then it returns false.\n */\nexport function isEthereumAddress(value: unknown): boolean {\n  return typeof value === 'string' && isEthereumAddressValidator(value);\n}\n\n/**\n * Check if the string is an Ethereum address using basic regex. Does not validate address checksums.\n * If given value is not a string, then it returns false.\n */\nexport function IsEthereumAddress(validationOptions?: ValidationOptions): PropertyDecorator {\n  return ValidateBy(\n    {\n      name: IS_ETHEREUM_ADDRESS,\n      validator: {\n        validate: (value, args): boolean => isEthereumAddress(value),\n        defaultMessage: buildMessage(\n          eachPrefix => eachPrefix + '$property must be an Ethereum address',\n          validationOptions\n        ),\n      },\n    },\n    validationOptions\n  );\n}\n"]}