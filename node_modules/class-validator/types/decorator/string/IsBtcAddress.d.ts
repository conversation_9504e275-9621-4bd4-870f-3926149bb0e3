import { ValidationOptions } from '../ValidationOptions';
export declare const IS_BTC_ADDRESS = "isBtcAddress";
/**
 * Check if the string is a valid BTC address.
 * If given value is not a string, then it returns false.
 */
export declare function isBtcAddress(value: unknown): boolean;
/**
 * Check if the string is a valid BTC address.
 * If given value is not a string, then it returns false.
 */
export declare function IsBtcAddress(validationOptions?: ValidationOptions): PropertyDecorator;
