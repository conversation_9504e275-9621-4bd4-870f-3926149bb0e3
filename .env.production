# Production Environment Configuration
NODE_ENV=production

# Database Configuration
DATABASE_ENGINE=mongo
MONGO_URI=mongodb://prod-mongo-cluster:27017/legalyard_production
POSTGRES_URL=*******************************************************/legalyard_production

# Service Ports
GATEWAY_PORT=8000
CORE_PORT=8001

# Service URLs
CORE_SERVICE_URL=http://core-service:8001

# Authentication Secrets (MUST be changed in production)
ACCESS_TOKEN_SECRET=CHANGE_ME_PRODUCTION_ACCESS_TOKEN_SECRET
REFRESH_TOKEN_SECRET=CHANGE_ME_PRODUCTION_REFRESH_TOKEN_SECRET
TOTP_SECRET=CHANGE_ME_PRODUCTION_TOTP_SECRET

# Security Configuration (Restrict to specific IPs in production)
ALLOWED_IPS=10.0.0.0/8

# Rate Limiting (Stricter in production)
THROTTLE_TTL=60000
THROTTLE_LIMIT=100

# CORS Configuration
CORS_ORIGIN=https://yourapp.com,https://admin.yourapp.com

# Logging
LOG_LEVEL=warn
