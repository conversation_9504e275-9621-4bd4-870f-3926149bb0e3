# Legalyard Backend Monolithic

A NestJS monorepo with 2 applications: Gateway (API proxy) and Core (business logic).

## Architecture

This is a **NestJS monorepo with 2 apps**:

1. **Gateway App** - Lightweight API proxy & security layer
   - Handles request routing, token validation, rate-limiting
   - Central point of entry for all clients (admin, coder, etc.)
   - Runs on port 3000

2. **Core App** - Contains all business logic modules
   - Structured for future microservice extraction
   - Modules: `auth`, `admin`, `coder`, `shared`
   - Runs on port 8001

## Quick Start

### Prerequisites

- Node.js 20+
- npm
- MongoDB (optional: Docker)

### Installation

```bash
# Install dependencies
npm install

# Copy environment file
cp .env .env.local

# Start MongoDB (if using Docker)
docker-compose up -d mongo

# Build applications
npm run build

# Start both applications in development mode
npm run start:dev:core    # Terminal 1
npm run start:dev:gateway # Terminal 2
```

### Using Docker

```bash
# Start all services
docker-compose up

# Or start in detached mode
docker-compose up -d
```

## Available Scripts

### Build Commands
- `npm run build` - Build default app (core)
- `npm run build:core` - Build core app
- `npm run build:gateway` - Build gateway app

### Development Commands
- `npm run start:dev:core` - Start core app in watch mode
- `npm run start:dev:gateway` - Start gateway app in watch mode

### Production Commands
- `npm run start:prod:core` - Start core app in production
- `npm run start:prod:gateway` - Start gateway app in production

### Testing Commands
- `npm run test` - Run unit tests
- `npm run test:e2e:core` - Run core app e2e tests
- `npm run test:e2e:gateway` - Run gateway app e2e tests

## Project Structure

```
apps/
├── gateway/                  # NestJS Gateway (API proxy)
│   ├── src/
│   │   ├── auth/            # Auth forwarding
│   │   ├── proxy/           # Request proxying
│   │   └── main.ts
│   └── test/
├── core/                     # Main App (business logic)
│   ├── src/
│   │   ├── modules/
│   │   │   ├── auth/        # Authentication & authorization
│   │   │   ├── admin/       # Admin management
│   │   │   ├── coder/       # CMS functionality
│   │   │   └── shared/      # Shared utilities
│   │   └── main.ts
│   └── test/
libs/
├── config/                   # Configuration utilities
├── guards/                   # Shared guards
└── interfaces/               # TypeScript interfaces
```

## Environment Configuration

The application supports multiple environments:

- `.env` - Development (default)
- `.env.test` - Testing
- `.env.staging` - Staging
- `.env.production` - Production

### Key Environment Variables

```bash
# Service Configuration
GATEWAY_PORT=8000
CORE_PORT=8001
CORE_SERVICE_URL=http://localhost:8001

# Database
DATABASE_ENGINE=mongo
MONGO_URI=mongodb://localhost:27017/legalyard

# Security
ACCESS_TOKEN_SECRET=your-secret
REFRESH_TOKEN_SECRET=your-secret
ALLOWED_IPS=127.0.0.1,::1
```

## API Endpoints

### Gateway (Port 3000)
- `GET /` - Gateway health check
- `GET /health` - Service health status
- `POST /api/auth/*` - Authentication endpoints (forwarded to core)
- `ALL /api/core/*` - All other endpoints (proxied to core)

### Core (Port 3001)
- `GET /api` - Core health check
- `GET /api/health` - Service health status
- `POST /api/auth/*` - Authentication endpoints
- `GET /api/admin/*` - Admin management endpoints
- `GET /api/coder/*` - CMS endpoints

## Documentation

### 📚 Comprehensive API Documentation
- **Complete Documentation**: [docs/README.md](./docs/README.md)
- **API Reference**: [docs/API_REFERENCE.md](./docs/API_REFERENCE.md)
- **Next.js Integration Guide**: [docs/NEXTJS_INTEGRATION_GUIDE.md](./docs/NEXTJS_INTEGRATION_GUIDE.md)
- **TypeScript Definitions**: [docs/TYPESCRIPT_DEFINITIONS.md](./docs/TYPESCRIPT_DEFINITIONS.md)
- **Development Workflow**: [docs/DEVELOPMENT_WORKFLOW.md](./docs/DEVELOPMENT_WORKFLOW.md)

### 🔗 Live API Documentation
- **Gateway Swagger**: http://localhost:8000/api/docs
- **Core Swagger**: http://localhost:8001/api/docs

### 🤖 For AI Agents
The documentation in the `docs/` folder is specifically designed for AI agents building Next.js applications. It provides:
- Precise API integration patterns
- Complete TypeScript definitions
- Step-by-step implementation guides
- Authentication flow examples
- Error handling patterns

## Security Features

- Opaque + refresh token system (phantom pattern)
- TOTP-based MFA support
- Device fingerprinting (IP, OS, Browser)
- Max 2-device login policy
- IP allowlist access for admin system
- Global guards: `AuthGuard`, `RolesGuard`, `RateLimitGuard`
- Rate limiting and CORS protection

## Development

### Adding New Modules

1. Create module in `apps/core/src/modules/`
2. Add to `apps/core/src/app.module.ts`
3. Update interfaces in `libs/interfaces/src/`

### Testing

```bash
# Run all tests
npm run test

# Run specific app tests
npm run test:e2e:core
npm run test:e2e:gateway

# Run with coverage
npm run test:cov
```

### Code Quality

```bash
# Lint code
npm run lint

# Format code
npm run format
```

## Deployment

See [DEPLOYMENT.md](./DEPLOYMENT.md) for detailed deployment instructions.

## License

UNLICENSED
