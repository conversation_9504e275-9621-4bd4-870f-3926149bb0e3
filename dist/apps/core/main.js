/******/ (() => { // webpackBootstrap
/******/ 	"use strict";
/******/ 	var __webpack_modules__ = ({

/***/ "./apps/core/src/app.controller.ts":
/*!*****************************************!*\
  !*** ./apps/core/src/app.controller.ts ***!
  \*****************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {


var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var _a;
Object.defineProperty(exports, "__esModule", ({ value: true }));
exports.AppController = void 0;
const common_1 = __webpack_require__(/*! @nestjs/common */ "@nestjs/common");
const app_service_1 = __webpack_require__(/*! ./app.service */ "./apps/core/src/app.service.ts");
let AppController = class AppController {
    constructor(appService) {
        this.appService = appService;
    }
    getHello() {
        return this.appService.getHello();
    }
    getHealth() {
        return {
            status: 'ok',
            timestamp: new Date().toISOString(),
            service: 'core',
        };
    }
};
exports.AppController = AppController;
__decorate([
    (0, common_1.Get)(),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", String)
], AppController.prototype, "getHello", null);
__decorate([
    (0, common_1.Get)('health'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Object)
], AppController.prototype, "getHealth", null);
exports.AppController = AppController = __decorate([
    (0, common_1.Controller)(),
    __metadata("design:paramtypes", [typeof (_a = typeof app_service_1.AppService !== "undefined" && app_service_1.AppService) === "function" ? _a : Object])
], AppController);


/***/ }),

/***/ "./apps/core/src/app.module.ts":
/*!*************************************!*\
  !*** ./apps/core/src/app.module.ts ***!
  \*************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {


var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", ({ value: true }));
exports.AppModule = void 0;
const common_1 = __webpack_require__(/*! @nestjs/common */ "@nestjs/common");
const config_1 = __webpack_require__(/*! @nestjs/config */ "@nestjs/config");
const app_controller_1 = __webpack_require__(/*! ./app.controller */ "./apps/core/src/app.controller.ts");
const app_service_1 = __webpack_require__(/*! ./app.service */ "./apps/core/src/app.service.ts");
const database_module_1 = __webpack_require__(/*! ./database/database.module */ "./apps/core/src/database/database.module.ts");
const auth_module_1 = __webpack_require__(/*! ./modules/auth/auth.module */ "./apps/core/src/modules/auth/auth.module.ts");
const admin_module_1 = __webpack_require__(/*! ./modules/admin/admin.module */ "./apps/core/src/modules/admin/admin.module.ts");
const coder_module_1 = __webpack_require__(/*! ./modules/coder/coder.module */ "./apps/core/src/modules/coder/coder.module.ts");
const shared_module_1 = __webpack_require__(/*! ./modules/shared/shared.module */ "./apps/core/src/modules/shared/shared.module.ts");
const docs_module_1 = __webpack_require__(/*! ./modules/docs/docs.module */ "./apps/core/src/modules/docs/docs.module.ts");
let AppModule = class AppModule {
};
exports.AppModule = AppModule;
exports.AppModule = AppModule = __decorate([
    (0, common_1.Module)({
        imports: [
            config_1.ConfigModule.forRoot({
                isGlobal: true,
                envFilePath: ['.env.local', '.env'],
            }),
            database_module_1.DatabaseModule,
            auth_module_1.AuthModule,
            admin_module_1.AdminModule,
            coder_module_1.CoderModule,
            shared_module_1.SharedModule,
            docs_module_1.DocsModule,
        ],
        controllers: [app_controller_1.AppController],
        providers: [app_service_1.AppService],
    })
], AppModule);


/***/ }),

/***/ "./apps/core/src/app.service.ts":
/*!**************************************!*\
  !*** ./apps/core/src/app.service.ts ***!
  \**************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {


var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", ({ value: true }));
exports.AppService = void 0;
const common_1 = __webpack_require__(/*! @nestjs/common */ "@nestjs/common");
let AppService = class AppService {
    getHello() {
        return 'Core API is running!';
    }
};
exports.AppService = AppService;
exports.AppService = AppService = __decorate([
    (0, common_1.Injectable)()
], AppService);


/***/ }),

/***/ "./apps/core/src/database/database.module.ts":
/*!***************************************************!*\
  !*** ./apps/core/src/database/database.module.ts ***!
  \***************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {


var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", ({ value: true }));
exports.DatabaseModule = void 0;
const common_1 = __webpack_require__(/*! @nestjs/common */ "@nestjs/common");
const config_1 = __webpack_require__(/*! @nestjs/config */ "@nestjs/config");
const mongoose_1 = __webpack_require__(/*! @nestjs/mongoose */ "@nestjs/mongoose");
let DatabaseModule = class DatabaseModule {
};
exports.DatabaseModule = DatabaseModule;
exports.DatabaseModule = DatabaseModule = __decorate([
    (0, common_1.Module)({
        imports: [
            mongoose_1.MongooseModule.forRootAsync({
                imports: [config_1.ConfigModule],
                useFactory: async (configService) => {
                    const mongoUri = configService.get('MONGO_URI');
                    return {
                        uri: mongoUri,
                        retryWrites: true,
                        w: 'majority',
                        retryAttempts: 1,
                        retryDelay: 1000,
                        maxPoolSize: 10,
                        serverSelectionTimeoutMS: 5000,
                        socketTimeoutMS: 45000,
                        bufferCommands: false,
                        autoCreate: false,
                        autoIndex: false,
                    };
                },
                inject: [config_1.ConfigService],
            }),
        ],
        exports: [mongoose_1.MongooseModule],
    })
], DatabaseModule);


/***/ }),

/***/ "./apps/core/src/modules/admin/admin.controller.ts":
/*!*********************************************************!*\
  !*** ./apps/core/src/modules/admin/admin.controller.ts ***!
  \*********************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {


var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var _a;
Object.defineProperty(exports, "__esModule", ({ value: true }));
exports.AdminController = void 0;
const common_1 = __webpack_require__(/*! @nestjs/common */ "@nestjs/common");
const admin_service_1 = __webpack_require__(/*! ./admin.service */ "./apps/core/src/modules/admin/admin.service.ts");
let AdminController = class AdminController {
    constructor(adminService) {
        this.adminService = adminService;
    }
    async getUsers() {
        return { message: 'Get users endpoint - to be implemented' };
    }
    async createUser(userData) {
        return { message: 'Create user endpoint - to be implemented' };
    }
    async updateUser(id, userData) {
        return { message: `Update user ${id} endpoint - to be implemented` };
    }
    async deleteUser(id) {
        return { message: `Delete user ${id} endpoint - to be implemented` };
    }
    async getTenants() {
        return { message: 'Get tenants endpoint - to be implemented' };
    }
    async createTenant(tenantData) {
        return { message: 'Create tenant endpoint - to be implemented' };
    }
    async getPermissions() {
        return { message: 'Get permissions endpoint - to be implemented' };
    }
    async getAuditLogs() {
        return { message: 'Get audit logs endpoint - to be implemented' };
    }
};
exports.AdminController = AdminController;
__decorate([
    (0, common_1.Get)('users'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], AdminController.prototype, "getUsers", null);
__decorate([
    (0, common_1.Post)('users'),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], AdminController.prototype, "createUser", null);
__decorate([
    (0, common_1.Put)('users/:id'),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], AdminController.prototype, "updateUser", null);
__decorate([
    (0, common_1.Delete)('users/:id'),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], AdminController.prototype, "deleteUser", null);
__decorate([
    (0, common_1.Get)('tenants'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], AdminController.prototype, "getTenants", null);
__decorate([
    (0, common_1.Post)('tenants'),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], AdminController.prototype, "createTenant", null);
__decorate([
    (0, common_1.Get)('permissions'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], AdminController.prototype, "getPermissions", null);
__decorate([
    (0, common_1.Get)('audit-logs'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], AdminController.prototype, "getAuditLogs", null);
exports.AdminController = AdminController = __decorate([
    (0, common_1.Controller)('admin'),
    __metadata("design:paramtypes", [typeof (_a = typeof admin_service_1.AdminService !== "undefined" && admin_service_1.AdminService) === "function" ? _a : Object])
], AdminController);


/***/ }),

/***/ "./apps/core/src/modules/admin/admin.module.ts":
/*!*****************************************************!*\
  !*** ./apps/core/src/modules/admin/admin.module.ts ***!
  \*****************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {


var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", ({ value: true }));
exports.AdminModule = void 0;
const common_1 = __webpack_require__(/*! @nestjs/common */ "@nestjs/common");
const admin_controller_1 = __webpack_require__(/*! ./admin.controller */ "./apps/core/src/modules/admin/admin.controller.ts");
const admin_service_1 = __webpack_require__(/*! ./admin.service */ "./apps/core/src/modules/admin/admin.service.ts");
let AdminModule = class AdminModule {
};
exports.AdminModule = AdminModule;
exports.AdminModule = AdminModule = __decorate([
    (0, common_1.Module)({
        controllers: [admin_controller_1.AdminController],
        providers: [admin_service_1.AdminService],
        exports: [admin_service_1.AdminService],
    })
], AdminModule);


/***/ }),

/***/ "./apps/core/src/modules/admin/admin.service.ts":
/*!******************************************************!*\
  !*** ./apps/core/src/modules/admin/admin.service.ts ***!
  \******************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {


var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", ({ value: true }));
exports.AdminService = void 0;
const common_1 = __webpack_require__(/*! @nestjs/common */ "@nestjs/common");
let AdminService = class AdminService {
    async getUsers() {
        return { message: 'Get users service method - to be implemented' };
    }
    async createUser(userData) {
        return { message: 'Create user service method - to be implemented' };
    }
    async updateUser(id, userData) {
        return { message: 'Update user service method - to be implemented' };
    }
    async deleteUser(id) {
        return { message: 'Delete user service method - to be implemented' };
    }
    async getTenants() {
        return { message: 'Get tenants service method - to be implemented' };
    }
    async createTenant(tenantData) {
        return { message: 'Create tenant service method - to be implemented' };
    }
    async getPermissions() {
        return { message: 'Get permissions service method - to be implemented' };
    }
    async getAuditLogs() {
        return { message: 'Get audit logs service method - to be implemented' };
    }
};
exports.AdminService = AdminService;
exports.AdminService = AdminService = __decorate([
    (0, common_1.Injectable)()
], AdminService);


/***/ }),

/***/ "./apps/core/src/modules/auth/auth.controller.ts":
/*!*******************************************************!*\
  !*** ./apps/core/src/modules/auth/auth.controller.ts ***!
  \*******************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {


var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var _a;
Object.defineProperty(exports, "__esModule", ({ value: true }));
exports.AuthController = void 0;
const common_1 = __webpack_require__(/*! @nestjs/common */ "@nestjs/common");
const swagger_1 = __webpack_require__(/*! @nestjs/swagger */ "@nestjs/swagger");
const auth_service_1 = __webpack_require__(/*! ./auth.service */ "./apps/core/src/modules/auth/auth.service.ts");
let AuthController = class AuthController {
    constructor(authService) {
        this.authService = authService;
    }
    async login(loginDto) {
        return { message: 'Login endpoint - to be implemented' };
    }
    async register(registerDto) {
        return { message: 'Register endpoint - to be implemented' };
    }
    async refresh(refreshDto) {
        return { message: 'Refresh endpoint - to be implemented' };
    }
    async logout(logoutDto) {
        return { message: 'Logout endpoint - to be implemented' };
    }
    async validate(validateDto) {
        return { message: 'Validate endpoint - to be implemented' };
    }
    async verify(authHeader) {
        return { message: 'Verify endpoint - to be implemented' };
    }
};
exports.AuthController = AuthController;
__decorate([
    (0, common_1.Post)('login'),
    (0, swagger_1.ApiOperation)({ summary: 'User login' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Login successful' }),
    (0, swagger_1.ApiResponse)({ status: 401, description: 'Invalid credentials' }),
    (0, swagger_1.ApiBody)({
        schema: {
            type: 'object',
            required: ['email', 'password'],
            properties: {
                email: { type: 'string', example: '<EMAIL>' },
                password: { type: 'string', example: 'password123' },
                totpCode: { type: 'string', example: '123456' },
            },
        },
    }),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], AuthController.prototype, "login", null);
__decorate([
    (0, common_1.Post)('register'),
    (0, swagger_1.ApiOperation)({ summary: 'User registration' }),
    (0, swagger_1.ApiResponse)({ status: 201, description: 'Registration successful' }),
    (0, swagger_1.ApiResponse)({ status: 400, description: 'Invalid input data' }),
    (0, swagger_1.ApiBody)({
        schema: {
            type: 'object',
            required: ['email', 'password'],
            properties: {
                email: { type: 'string', example: '<EMAIL>' },
                password: { type: 'string', example: 'password123' },
                username: { type: 'string', example: 'johndoe' },
                firstName: { type: 'string', example: 'John' },
                lastName: { type: 'string', example: 'Doe' },
            },
        },
    }),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], AuthController.prototype, "register", null);
__decorate([
    (0, common_1.Post)('refresh'),
    (0, swagger_1.ApiOperation)({ summary: 'Refresh access token' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Token refreshed successfully' }),
    (0, swagger_1.ApiResponse)({ status: 401, description: 'Invalid refresh token' }),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], AuthController.prototype, "refresh", null);
__decorate([
    (0, common_1.Post)('logout'),
    (0, swagger_1.ApiOperation)({ summary: 'User logout' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Logout successful' }),
    (0, swagger_1.ApiBearerAuth)(),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], AuthController.prototype, "logout", null);
__decorate([
    (0, common_1.Post)('validate'),
    (0, swagger_1.ApiOperation)({ summary: 'Validate token (internal use)' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Token is valid' }),
    (0, swagger_1.ApiResponse)({ status: 401, description: 'Token is invalid' }),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], AuthController.prototype, "validate", null);
__decorate([
    (0, common_1.Get)('verify'),
    (0, swagger_1.ApiOperation)({ summary: 'Verify current user token' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Token verified successfully' }),
    (0, swagger_1.ApiResponse)({ status: 401, description: 'Invalid or expired token' }),
    (0, swagger_1.ApiBearerAuth)(),
    __param(0, (0, common_1.Headers)('authorization')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], AuthController.prototype, "verify", null);
exports.AuthController = AuthController = __decorate([
    (0, swagger_1.ApiTags)('auth'),
    (0, common_1.Controller)('auth'),
    __metadata("design:paramtypes", [typeof (_a = typeof auth_service_1.AuthService !== "undefined" && auth_service_1.AuthService) === "function" ? _a : Object])
], AuthController);


/***/ }),

/***/ "./apps/core/src/modules/auth/auth.module.ts":
/*!***************************************************!*\
  !*** ./apps/core/src/modules/auth/auth.module.ts ***!
  \***************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {


var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", ({ value: true }));
exports.AuthModule = void 0;
const common_1 = __webpack_require__(/*! @nestjs/common */ "@nestjs/common");
const auth_controller_1 = __webpack_require__(/*! ./auth.controller */ "./apps/core/src/modules/auth/auth.controller.ts");
const auth_service_1 = __webpack_require__(/*! ./auth.service */ "./apps/core/src/modules/auth/auth.service.ts");
let AuthModule = class AuthModule {
};
exports.AuthModule = AuthModule;
exports.AuthModule = AuthModule = __decorate([
    (0, common_1.Module)({
        controllers: [auth_controller_1.AuthController],
        providers: [auth_service_1.AuthService],
        exports: [auth_service_1.AuthService],
    })
], AuthModule);


/***/ }),

/***/ "./apps/core/src/modules/auth/auth.service.ts":
/*!****************************************************!*\
  !*** ./apps/core/src/modules/auth/auth.service.ts ***!
  \****************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {


var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", ({ value: true }));
exports.AuthService = void 0;
const common_1 = __webpack_require__(/*! @nestjs/common */ "@nestjs/common");
let AuthService = class AuthService {
    async login(credentials) {
        return { message: 'Login service method - to be implemented' };
    }
    async register(userData) {
        return { message: 'Register service method - to be implemented' };
    }
    async validateToken(token) {
        return { message: 'Token validation service method - to be implemented' };
    }
    async refreshToken(refreshToken) {
        return { message: 'Token refresh service method - to be implemented' };
    }
    async logout(token) {
        return { message: 'Logout service method - to be implemented' };
    }
};
exports.AuthService = AuthService;
exports.AuthService = AuthService = __decorate([
    (0, common_1.Injectable)()
], AuthService);


/***/ }),

/***/ "./apps/core/src/modules/coder/coder.controller.ts":
/*!*********************************************************!*\
  !*** ./apps/core/src/modules/coder/coder.controller.ts ***!
  \*********************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {


var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var _a;
Object.defineProperty(exports, "__esModule", ({ value: true }));
exports.CoderController = void 0;
const common_1 = __webpack_require__(/*! @nestjs/common */ "@nestjs/common");
const coder_service_1 = __webpack_require__(/*! ./coder.service */ "./apps/core/src/modules/coder/coder.service.ts");
let CoderController = class CoderController {
    constructor(coderService) {
        this.coderService = coderService;
    }
    async getContent() {
        return { message: 'Get content endpoint - to be implemented' };
    }
    async createContent(contentData) {
        return { message: 'Create content endpoint - to be implemented' };
    }
    async updateContent(id, contentData) {
        return { message: `Update content ${id} endpoint - to be implemented` };
    }
    async deleteContent(id) {
        return { message: `Delete content ${id} endpoint - to be implemented` };
    }
    async getFilters() {
        return { message: 'Get filters endpoint - to be implemented' };
    }
    async createFilter(filterData) {
        return { message: 'Create filter endpoint - to be implemented' };
    }
    async getForms() {
        return { message: 'Get forms endpoint - to be implemented' };
    }
    async createForm(formData) {
        return { message: 'Create form endpoint - to be implemented' };
    }
    async getThemes() {
        return { message: 'Get themes endpoint - to be implemented' };
    }
    async createTheme(themeData) {
        return { message: 'Create theme endpoint - to be implemented' };
    }
    async getNewsletters() {
        return { message: 'Get newsletters endpoint - to be implemented' };
    }
    async createNewsletter(newsletterData) {
        return { message: 'Create newsletter endpoint - to be implemented' };
    }
};
exports.CoderController = CoderController;
__decorate([
    (0, common_1.Get)('content'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], CoderController.prototype, "getContent", null);
__decorate([
    (0, common_1.Post)('content'),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], CoderController.prototype, "createContent", null);
__decorate([
    (0, common_1.Put)('content/:id'),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], CoderController.prototype, "updateContent", null);
__decorate([
    (0, common_1.Delete)('content/:id'),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], CoderController.prototype, "deleteContent", null);
__decorate([
    (0, common_1.Get)('filters'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], CoderController.prototype, "getFilters", null);
__decorate([
    (0, common_1.Post)('filters'),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], CoderController.prototype, "createFilter", null);
__decorate([
    (0, common_1.Get)('forms'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], CoderController.prototype, "getForms", null);
__decorate([
    (0, common_1.Post)('forms'),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], CoderController.prototype, "createForm", null);
__decorate([
    (0, common_1.Get)('themes'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], CoderController.prototype, "getThemes", null);
__decorate([
    (0, common_1.Post)('themes'),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], CoderController.prototype, "createTheme", null);
__decorate([
    (0, common_1.Get)('newsletter'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], CoderController.prototype, "getNewsletters", null);
__decorate([
    (0, common_1.Post)('newsletter'),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], CoderController.prototype, "createNewsletter", null);
exports.CoderController = CoderController = __decorate([
    (0, common_1.Controller)('coder'),
    __metadata("design:paramtypes", [typeof (_a = typeof coder_service_1.CoderService !== "undefined" && coder_service_1.CoderService) === "function" ? _a : Object])
], CoderController);


/***/ }),

/***/ "./apps/core/src/modules/coder/coder.module.ts":
/*!*****************************************************!*\
  !*** ./apps/core/src/modules/coder/coder.module.ts ***!
  \*****************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {


var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", ({ value: true }));
exports.CoderModule = void 0;
const common_1 = __webpack_require__(/*! @nestjs/common */ "@nestjs/common");
const coder_controller_1 = __webpack_require__(/*! ./coder.controller */ "./apps/core/src/modules/coder/coder.controller.ts");
const coder_service_1 = __webpack_require__(/*! ./coder.service */ "./apps/core/src/modules/coder/coder.service.ts");
let CoderModule = class CoderModule {
};
exports.CoderModule = CoderModule;
exports.CoderModule = CoderModule = __decorate([
    (0, common_1.Module)({
        controllers: [coder_controller_1.CoderController],
        providers: [coder_service_1.CoderService],
        exports: [coder_service_1.CoderService],
    })
], CoderModule);


/***/ }),

/***/ "./apps/core/src/modules/coder/coder.service.ts":
/*!******************************************************!*\
  !*** ./apps/core/src/modules/coder/coder.service.ts ***!
  \******************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {


var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", ({ value: true }));
exports.CoderService = void 0;
const common_1 = __webpack_require__(/*! @nestjs/common */ "@nestjs/common");
let CoderService = class CoderService {
    async getContent() {
        return { message: 'Get content service method - to be implemented' };
    }
    async createContent(contentData) {
        return { message: 'Create content service method - to be implemented' };
    }
    async updateContent(id, contentData) {
        return { message: 'Update content service method - to be implemented' };
    }
    async deleteContent(id) {
        return { message: 'Delete content service method - to be implemented' };
    }
    async getFilters() {
        return { message: 'Get filters service method - to be implemented' };
    }
    async createFilter(filterData) {
        return { message: 'Create filter service method - to be implemented' };
    }
    async getForms() {
        return { message: 'Get forms service method - to be implemented' };
    }
    async createForm(formData) {
        return { message: 'Create form service method - to be implemented' };
    }
    async getThemes() {
        return { message: 'Get themes service method - to be implemented' };
    }
    async createTheme(themeData) {
        return { message: 'Create theme service method - to be implemented' };
    }
    async getNewsletters() {
        return { message: 'Get newsletters service method - to be implemented' };
    }
    async createNewsletter(newsletterData) {
        return { message: 'Create newsletter service method - to be implemented' };
    }
};
exports.CoderService = CoderService;
exports.CoderService = CoderService = __decorate([
    (0, common_1.Injectable)()
], CoderService);


/***/ }),

/***/ "./apps/core/src/modules/docs/docs.controller.ts":
/*!*******************************************************!*\
  !*** ./apps/core/src/modules/docs/docs.controller.ts ***!
  \*******************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {


var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var _a, _b, _c;
Object.defineProperty(exports, "__esModule", ({ value: true }));
exports.DocsController = void 0;
const common_1 = __webpack_require__(/*! @nestjs/common */ "@nestjs/common");
const swagger_1 = __webpack_require__(/*! @nestjs/swagger */ "@nestjs/swagger");
const express_1 = __webpack_require__(/*! express */ "express");
const docs_service_1 = __webpack_require__(/*! ./docs.service */ "./apps/core/src/modules/docs/docs.service.ts");
let DocsController = class DocsController {
    constructor(docsService) {
        this.docsService = docsService;
    }
    getAiOnboardingOverview() {
        return this.docsService.getAiOnboardingOverview();
    }
    getArchitectureDoc() {
        return this.docsService.getArchitectureDoc();
    }
    getAuthenticationDoc() {
        return this.docsService.getAuthenticationDoc();
    }
    getApiReference(module) {
        return this.docsService.getApiReference(module);
    }
    getIntegrationPatterns(framework) {
        return this.docsService.getIntegrationPatterns(framework);
    }
    getTypeScriptDefinitions() {
        return this.docsService.getTypeScriptDefinitions();
    }
    getCodeExamples(scenario) {
        return this.docsService.getCodeExamples(scenario);
    }
    getRequirements() {
        return this.docsService.getRequirements();
    }
    getTestingGuidelines() {
        return this.docsService.getTestingGuidelines();
    }
    getModuleEndpoints(module) {
        return this.docsService.getModuleEndpoints(module);
    }
    getSchemas(format) {
        return this.docsService.getSchemas(format);
    }
    getWorkflow() {
        return this.docsService.getWorkflow();
    }
    getOpenApiSpec() {
        return this.docsService.getOpenApiSpec();
    }
    getPostmanCollection(res) {
        const collection = this.docsService.generatePostmanCollection();
        res.setHeader('Content-Type', 'application/json');
        res.setHeader('Content-Disposition', 'attachment; filename="Legalyard-Backend-API.postman_collection.json"');
        return res.send(JSON.stringify(collection, null, 2));
    }
    getPostmanDownloadPage(res) {
        const html = this.docsService.generatePostmanDownloadPage();
        res.setHeader('Content-Type', 'text/html');
        return res.send(html);
    }
    getCompleteAiAgentInfo() {
        return this.docsService.getCompleteAiAgentInfo();
    }
    getHealth() {
        return {
            status: 'ok',
            service: 'docs',
            timestamp: new Date().toISOString(),
            availableEndpoints: [
                '/api/docs/ai-agent-complete',
                '/api/docs/postman-collection',
                '/api/docs/postman-download',
                '/api/docs/ai-onboarding',
                '/api/docs/ai-onboarding/architecture',
                '/api/docs/ai-onboarding/authentication',
                '/api/docs/ai-onboarding/api-reference',
                '/api/docs/ai-onboarding/integration-patterns',
                '/api/docs/ai-onboarding/typescript-definitions',
                '/api/docs/ai-onboarding/examples',
                '/api/docs/ai-onboarding/requirements',
                '/api/docs/ai-onboarding/testing',
                '/api/docs/ai-onboarding/endpoints/:module',
                '/api/docs/ai-onboarding/schemas',
                '/api/docs/ai-onboarding/workflow',
                '/api/docs/openapi'
            ]
        };
    }
};
exports.DocsController = DocsController;
__decorate([
    (0, common_1.Get)('ai-onboarding'),
    (0, swagger_1.ApiOperation)({ summary: 'Get AI onboarding documentation overview' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'AI onboarding documentation overview' }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", void 0)
], DocsController.prototype, "getAiOnboardingOverview", null);
__decorate([
    (0, common_1.Get)('ai-onboarding/architecture'),
    (0, swagger_1.ApiOperation)({ summary: 'Get system architecture documentation for AI agents' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'System architecture documentation' }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", void 0)
], DocsController.prototype, "getArchitectureDoc", null);
__decorate([
    (0, common_1.Get)('ai-onboarding/authentication'),
    (0, swagger_1.ApiOperation)({ summary: 'Get authentication flow documentation for AI agents' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Authentication flow documentation' }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", void 0)
], DocsController.prototype, "getAuthenticationDoc", null);
__decorate([
    (0, common_1.Get)('ai-onboarding/api-reference'),
    (0, swagger_1.ApiOperation)({ summary: 'Get complete API reference for AI agents' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Complete API reference' }),
    (0, swagger_1.ApiQuery)({ name: 'module', required: false, description: 'Filter by module (auth, admin, coder)' }),
    __param(0, (0, common_1.Query)('module')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", void 0)
], DocsController.prototype, "getApiReference", null);
__decorate([
    (0, common_1.Get)('ai-onboarding/integration-patterns'),
    (0, swagger_1.ApiOperation)({ summary: 'Get integration patterns for Next.js and other frameworks' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Integration patterns documentation' }),
    (0, swagger_1.ApiQuery)({ name: 'framework', required: false, description: 'Filter by framework (nextjs, react, vue)' }),
    __param(0, (0, common_1.Query)('framework')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", void 0)
], DocsController.prototype, "getIntegrationPatterns", null);
__decorate([
    (0, common_1.Get)('ai-onboarding/typescript-definitions'),
    (0, swagger_1.ApiOperation)({ summary: 'Get TypeScript type definitions for AI agents' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'TypeScript type definitions' }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", void 0)
], DocsController.prototype, "getTypeScriptDefinitions", null);
__decorate([
    (0, common_1.Get)('ai-onboarding/examples'),
    (0, swagger_1.ApiOperation)({ summary: 'Get code examples for common scenarios' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Code examples' }),
    (0, swagger_1.ApiQuery)({ name: 'scenario', required: false, description: 'Filter by scenario (auth, crud, forms)' }),
    __param(0, (0, common_1.Query)('scenario')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", void 0)
], DocsController.prototype, "getCodeExamples", null);
__decorate([
    (0, common_1.Get)('ai-onboarding/requirements'),
    (0, swagger_1.ApiOperation)({ summary: 'Get system requirements and constraints for AI agents' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'System requirements and constraints' }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", void 0)
], DocsController.prototype, "getRequirements", null);
__decorate([
    (0, common_1.Get)('ai-onboarding/testing'),
    (0, swagger_1.ApiOperation)({ summary: 'Get testing guidelines and procedures for AI agents' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Testing guidelines and procedures' }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", void 0)
], DocsController.prototype, "getTestingGuidelines", null);
__decorate([
    (0, common_1.Get)('ai-onboarding/endpoints/:module'),
    (0, swagger_1.ApiOperation)({ summary: 'Get detailed endpoint documentation for specific module' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Module-specific endpoint documentation' }),
    (0, swagger_1.ApiParam)({ name: 'module', description: 'Module name (auth, admin, coder, docs)' }),
    __param(0, (0, common_1.Param)('module')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", void 0)
], DocsController.prototype, "getModuleEndpoints", null);
__decorate([
    (0, common_1.Get)('ai-onboarding/schemas'),
    (0, swagger_1.ApiOperation)({ summary: 'Get all data schemas and models for AI agents' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Data schemas and models' }),
    (0, swagger_1.ApiQuery)({ name: 'format', required: false, description: 'Response format (json, typescript, openapi)' }),
    __param(0, (0, common_1.Query)('format')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", void 0)
], DocsController.prototype, "getSchemas", null);
__decorate([
    (0, common_1.Get)('ai-onboarding/workflow'),
    (0, swagger_1.ApiOperation)({ summary: 'Get development workflow and best practices for AI agents' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Development workflow and best practices' }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", void 0)
], DocsController.prototype, "getWorkflow", null);
__decorate([
    (0, common_1.Get)('openapi'),
    (0, swagger_1.ApiOperation)({ summary: 'Get OpenAPI specification for the entire system' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'OpenAPI specification' }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", void 0)
], DocsController.prototype, "getOpenApiSpec", null);
__decorate([
    (0, common_1.Get)('postman-collection'),
    (0, swagger_1.ApiOperation)({ summary: 'Download Postman collection with all API endpoints' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Postman collection JSON file',
        headers: {
            'Content-Type': { description: 'application/json' },
            'Content-Disposition': { description: 'attachment; filename="Legalyard-Backend-API.postman_collection.json"' }
        }
    }),
    __param(0, (0, common_1.Res)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [typeof (_b = typeof express_1.Response !== "undefined" && express_1.Response) === "function" ? _b : Object]),
    __metadata("design:returntype", void 0)
], DocsController.prototype, "getPostmanCollection", null);
__decorate([
    (0, common_1.Get)('postman-download'),
    (0, swagger_1.ApiOperation)({ summary: 'Download page for Postman collection' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'HTML page with download button for Postman collection' }),
    __param(0, (0, common_1.Res)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [typeof (_c = typeof express_1.Response !== "undefined" && express_1.Response) === "function" ? _c : Object]),
    __metadata("design:returntype", void 0)
], DocsController.prototype, "getPostmanDownloadPage", null);
__decorate([
    (0, common_1.Get)('ai-agent-complete'),
    (0, swagger_1.ApiOperation)({ summary: 'Complete AI agent onboarding - Single endpoint with all information' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Complete system information for AI agents' }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", void 0)
], DocsController.prototype, "getCompleteAiAgentInfo", null);
__decorate([
    (0, common_1.Get)('health'),
    (0, swagger_1.ApiOperation)({ summary: 'Check documentation service health' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Documentation service health status' }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", void 0)
], DocsController.prototype, "getHealth", null);
exports.DocsController = DocsController = __decorate([
    (0, swagger_1.ApiTags)('docs'),
    (0, common_1.Controller)('docs'),
    __metadata("design:paramtypes", [typeof (_a = typeof docs_service_1.DocsService !== "undefined" && docs_service_1.DocsService) === "function" ? _a : Object])
], DocsController);


/***/ }),

/***/ "./apps/core/src/modules/docs/docs.module.ts":
/*!***************************************************!*\
  !*** ./apps/core/src/modules/docs/docs.module.ts ***!
  \***************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {


var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", ({ value: true }));
exports.DocsModule = void 0;
const common_1 = __webpack_require__(/*! @nestjs/common */ "@nestjs/common");
const docs_controller_1 = __webpack_require__(/*! ./docs.controller */ "./apps/core/src/modules/docs/docs.controller.ts");
const docs_service_1 = __webpack_require__(/*! ./docs.service */ "./apps/core/src/modules/docs/docs.service.ts");
let DocsModule = class DocsModule {
};
exports.DocsModule = DocsModule;
exports.DocsModule = DocsModule = __decorate([
    (0, common_1.Module)({
        controllers: [docs_controller_1.DocsController],
        providers: [docs_service_1.DocsService],
        exports: [docs_service_1.DocsService],
    })
], DocsModule);


/***/ }),

/***/ "./apps/core/src/modules/docs/docs.service.ts":
/*!****************************************************!*\
  !*** ./apps/core/src/modules/docs/docs.service.ts ***!
  \****************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {


var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", ({ value: true }));
exports.DocsService = void 0;
const common_1 = __webpack_require__(/*! @nestjs/common */ "@nestjs/common");
let DocsService = class DocsService {
    getAiOnboardingOverview() {
        return {
            title: 'Legalyard Backend AI Onboarding Documentation',
            version: '1.0.0',
            description: 'Comprehensive documentation for AI agents to understand and integrate with Legalyard Backend APIs',
            lastUpdated: new Date().toISOString(),
            architecture: {
                type: 'NestJS Monorepo with 2 Applications',
                flow: 'Client → Gateway (8000) → Core (8001) → MongoDB',
                apps: {
                    gateway: {
                        port: 8000,
                        purpose: 'API proxy, authentication, rate limiting, security',
                        technology: 'NestJS + Express'
                    },
                    core: {
                        port: 8001,
                        purpose: 'Business logic, database operations, modules',
                        technology: 'NestJS + Express + MongoDB'
                    }
                }
            },
            modules: {
                auth: 'Authentication and authorization',
                admin: 'User and organization management',
                coder: 'Content management system (CMS)',
                docs: 'AI onboarding documentation'
            },
            keyFeatures: [
                'Bearer token authentication with refresh mechanism',
                'Rate limiting and security headers',
                'MongoDB integration with Mongoose',
                'Swagger API documentation',
                'TypeScript type safety',
                'Modular architecture for microservice extraction'
            ],
            integrationPoints: {
                primaryEndpoint: 'http://localhost:8000 (Gateway)',
                authenticationFlow: 'POST /api/auth/login → Bearer token → API calls',
                proxyPattern: '/api/core/* → Core app endpoints',
                documentation: {
                    swagger: {
                        gateway: 'http://localhost:8000/api/docs',
                        core: 'http://localhost:8001/api/docs'
                    },
                    aiOnboarding: 'http://localhost:8000/api/core/docs/ai-onboarding'
                }
            },
            quickStart: {
                step1: 'Access documentation: GET /api/docs/ai-onboarding',
                step2: 'Understand architecture: GET /api/docs/ai-onboarding/architecture',
                step3: 'Learn authentication: GET /api/docs/ai-onboarding/authentication',
                step4: 'Get API reference: GET /api/docs/ai-onboarding/api-reference',
                step5: 'View integration patterns: GET /api/docs/ai-onboarding/integration-patterns',
                step6: 'Get TypeScript definitions: GET /api/docs/ai-onboarding/typescript-definitions',
                step7: 'See code examples: GET /api/docs/ai-onboarding/examples'
            },
            supportedFrameworks: ['Next.js', 'React', 'Vue.js', 'Angular', 'Vanilla JavaScript'],
            aiAgentGuidelines: [
                'Always use Gateway endpoints (port 8000) for client integration',
                'Implement proper authentication with token refresh',
                'Handle errors gracefully with consistent patterns',
                'Use TypeScript definitions for type safety',
                'Follow RESTful conventions',
                'Implement proper loading states',
                'Validate user permissions before UI actions',
                'Test API endpoints before implementing UI'
            ]
        };
    }
    getArchitectureDoc() {
        return {
            title: 'System Architecture Documentation',
            overview: {
                pattern: 'API Gateway + Microservice-Ready Monolith',
                flow: 'Client Applications → Gateway App → Core App → Database',
                benefits: [
                    'Centralized authentication and security',
                    'Rate limiting and request validation',
                    'Easy microservice extraction',
                    'Consistent API patterns',
                    'Scalable architecture'
                ]
            },
            components: {
                gateway: {
                    port: 3000,
                    responsibilities: [
                        'API proxy and routing',
                        'Authentication token validation',
                        'Rate limiting (100 req/min default)',
                        'CORS and security headers',
                        'Request/response logging',
                        'IP allowlisting for admin access'
                    ],
                    endpoints: {
                        health: 'GET /api/health',
                        auth: 'POST /api/auth/* (forwarded to core)',
                        proxy: 'ALL /api/core/* (proxied to core)'
                    },
                    technology: 'NestJS + Express + Swagger'
                },
                core: {
                    port: 3001,
                    responsibilities: [
                        'Business logic processing',
                        'Database operations',
                        'User management',
                        'Content management',
                        'Authentication logic',
                        'Audit logging'
                    ],
                    modules: {
                        auth: 'Authentication and session management',
                        admin: 'User and organization administration',
                        coder: 'Content management system',
                        shared: 'Common utilities and services',
                        docs: 'AI onboarding documentation'
                    },
                    technology: 'NestJS + Express + MongoDB + Mongoose'
                },
                database: {
                    type: 'MongoDB',
                    collections: [
                        'users - User accounts and profiles',
                        'organizations - Tenant organizations',
                        'sessions - User sessions and devices',
                        'content - CMS content items',
                        'audit_logs - System audit trail'
                    ],
                    features: [
                        'Automatic indexing',
                        'TTL for session expiration',
                        'Full-text search capabilities',
                        'Aggregation pipelines'
                    ]
                }
            },
            requestFlow: {
                authentication: [
                    '1. Client sends credentials to Gateway /api/auth/login',
                    '2. Gateway forwards to Core /api/auth/login',
                    '3. Core validates credentials and creates session',
                    '4. Core returns access + refresh tokens',
                    '5. Gateway forwards tokens to client',
                    '6. Client stores tokens for future requests'
                ],
                apiCalls: [
                    '1. Client sends request to Gateway with Bearer token',
                    '2. Gateway validates token with Core',
                    '3. Gateway adds user context headers',
                    '4. Gateway proxies request to Core',
                    '5. Core processes business logic',
                    '6. Core returns response to Gateway',
                    '7. Gateway forwards response to client'
                ]
            },
            security: {
                authentication: 'Bearer tokens (JWT-like opaque tokens)',
                authorization: 'Role-based access control (RBAC)',
                rateLimit: '100 requests per minute per IP',
                cors: 'Configurable origins',
                headers: 'Helmet security headers',
                ipAllowlist: 'Admin access restriction',
                deviceTracking: 'Maximum 2 devices per user'
            },
            scalability: {
                horizontal: 'Multiple Gateway instances behind load balancer',
                vertical: 'Core app can be scaled independently',
                microservices: 'Modules can be extracted to separate services',
                database: 'MongoDB replica sets and sharding'
            }
        };
    }
    getAuthenticationDoc() {
        return {
            title: 'Authentication Flow Documentation',
            overview: {
                pattern: 'Opaque Token + Refresh Token (Phantom Pattern)',
                tokenTypes: {
                    accessToken: {
                        purpose: 'API access authorization',
                        lifetime: '15 minutes',
                        storage: 'Memory or secure storage',
                        format: 'Opaque string (not JWT for security)'
                    },
                    refreshToken: {
                        purpose: 'Access token renewal',
                        lifetime: '7 days',
                        storage: 'Secure HTTP-only cookie or secure storage',
                        format: 'Opaque string'
                    }
                },
                security: [
                    'Opaque tokens prevent token inspection',
                    'Short access token lifetime reduces exposure',
                    'Refresh tokens enable seamless renewal',
                    'Device tracking prevents token sharing',
                    'Session invalidation on logout'
                ]
            },
            endpoints: {
                login: {
                    method: 'POST',
                    url: '/api/auth/login',
                    description: 'Authenticate user and receive tokens',
                    request: {
                        email: 'string (required)',
                        password: 'string (required)',
                        totpCode: 'string (optional, required if MFA enabled)'
                    },
                    response: {
                        accessToken: 'string',
                        refreshToken: 'string',
                        user: 'User object',
                        expiresIn: 'number (seconds)'
                    },
                    errors: [
                        '400 - Invalid request format',
                        '401 - Invalid credentials',
                        '429 - Too many login attempts'
                    ]
                },
                register: {
                    method: 'POST',
                    url: '/api/auth/register',
                    description: 'Create new user account',
                    request: {
                        email: 'string (required)',
                        password: 'string (required)',
                        username: 'string (optional)',
                        firstName: 'string (optional)',
                        lastName: 'string (optional)'
                    },
                    response: 'Same as login response',
                    errors: [
                        '400 - Validation errors',
                        '409 - Email already exists'
                    ]
                },
                refresh: {
                    method: 'POST',
                    url: '/api/auth/refresh',
                    description: 'Refresh access token',
                    request: {
                        refreshToken: 'string (required)'
                    },
                    response: {
                        accessToken: 'string',
                        refreshToken: 'string (new)',
                        expiresIn: 'number'
                    },
                    errors: [
                        '401 - Invalid refresh token',
                        '403 - Token expired or revoked'
                    ]
                },
                logout: {
                    method: 'POST',
                    url: '/api/auth/logout',
                    description: 'Invalidate user session',
                    headers: {
                        Authorization: 'Bearer <access_token>'
                    },
                    response: {
                        message: 'Logout successful'
                    }
                },
                verify: {
                    method: 'GET',
                    url: '/api/auth/verify',
                    description: 'Verify token and get user info',
                    headers: {
                        Authorization: 'Bearer <access_token>'
                    },
                    response: {
                        user: 'User object',
                        tokenValid: 'boolean'
                    }
                }
            },
            implementationPatterns: {
                clientSide: {
                    tokenStorage: [
                        'localStorage for development/testing',
                        'sessionStorage for temporary sessions',
                        'Secure HTTP-only cookies for production',
                        'React Context or Zustand for state management'
                    ],
                    refreshLogic: [
                        'Automatic refresh on 401 responses',
                        'Proactive refresh before expiration',
                        'Retry failed requests after refresh',
                        'Logout on refresh failure'
                    ]
                },
                errorHandling: [
                    'Check response status codes',
                    'Handle network errors gracefully',
                    'Implement exponential backoff for retries',
                    'Clear tokens on authentication failures',
                    'Redirect to login on session expiry'
                ]
            },
            securityConsiderations: [
                'Never store tokens in plain text',
                'Use HTTPS in production',
                'Implement CSRF protection',
                'Validate token on every request',
                'Log authentication events',
                'Monitor for suspicious activity',
                'Implement account lockout policies'
            ]
        };
    }
    getApiReference(module) {
        const allEndpoints = {
            gateway: {
                baseUrl: 'http://localhost:8000',
                description: 'API Gateway - Central entry point for all client applications',
                endpoints: {
                    health: {
                        method: 'GET',
                        path: '/api/health',
                        description: 'Gateway health check',
                        authentication: false,
                        response: { status: 'string', timestamp: 'string', service: 'string' }
                    },
                    auth: {
                        login: {
                            method: 'POST',
                            path: '/api/auth/login',
                            description: 'User authentication (forwarded to core)',
                            authentication: false,
                            request: { email: 'string', password: 'string', totpCode: 'string (optional)' },
                            response: { accessToken: 'string', refreshToken: 'string', user: 'User', expiresIn: 'number' }
                        },
                        register: { method: 'POST', path: '/api/auth/register', description: 'User registration (forwarded to core)', authentication: false },
                        refresh: { method: 'POST', path: '/api/auth/refresh', description: 'Token refresh (forwarded to core)', authentication: false },
                        logout: { method: 'POST', path: '/api/auth/logout', description: 'User logout (forwarded to core)', authentication: true },
                        verify: { method: 'GET', path: '/api/auth/verify', description: 'Token verification (forwarded to core)', authentication: true }
                    },
                    proxy: {
                        method: 'ALL',
                        path: '/api/core/*',
                        description: 'Proxy all requests to Core app',
                        authentication: true,
                        note: 'Requests are forwarded to Core app with user context headers'
                    }
                }
            },
            core: {
                baseUrl: 'http://localhost:8001 (Access via Gateway: http://localhost:8000/api/core)',
                description: 'Core Business Logic - Contains all application modules',
                modules: {
                    auth: {
                        description: 'Authentication and session management',
                        endpoints: {
                            login: { method: 'POST', path: '/api/auth/login', description: 'Process user authentication', authentication: false },
                            validate: { method: 'POST', path: '/api/auth/validate', description: 'Validate token (internal use)', authentication: false }
                        }
                    },
                    admin: {
                        description: 'User and organization administration',
                        endpoints: {
                            getUsers: { method: 'GET', path: '/api/admin/users', description: 'List all users', authentication: true, permissions: ['admin'] },
                            createUser: { method: 'POST', path: '/api/admin/users', description: 'Create new user', authentication: true, permissions: ['admin'] },
                            updateUser: { method: 'PUT', path: '/api/admin/users/:id', description: 'Update user', authentication: true, permissions: ['admin'] },
                            deleteUser: { method: 'DELETE', path: '/api/admin/users/:id', description: 'Delete user', authentication: true, permissions: ['admin'] },
                            getTenants: { method: 'GET', path: '/api/admin/tenants', description: 'List organizations', authentication: true, permissions: ['admin'] },
                            createTenant: { method: 'POST', path: '/api/admin/tenants', description: 'Create organization', authentication: true, permissions: ['admin'] },
                            getPermissions: { method: 'GET', path: '/api/admin/permissions', description: 'List permissions', authentication: true, permissions: ['admin'] },
                            getAuditLogs: { method: 'GET', path: '/api/admin/audit-logs', description: 'View audit logs', authentication: true, permissions: ['admin'] }
                        }
                    },
                    coder: {
                        description: 'Content Management System (CMS)',
                        endpoints: {
                            getContent: { method: 'GET', path: '/api/coder/content', description: 'List content items', authentication: true },
                            createContent: { method: 'POST', path: '/api/coder/content', description: 'Create new content', authentication: true, permissions: ['coder', 'admin'] },
                            updateContent: { method: 'PUT', path: '/api/coder/content/:id', description: 'Update content', authentication: true, permissions: ['coder', 'admin'] },
                            deleteContent: { method: 'DELETE', path: '/api/coder/content/:id', description: 'Delete content', authentication: true, permissions: ['coder', 'admin'] },
                            getForms: { method: 'GET', path: '/api/coder/forms', description: 'List forms', authentication: true },
                            createForm: { method: 'POST', path: '/api/coder/forms', description: 'Create form', authentication: true, permissions: ['coder', 'admin'] },
                            getThemes: { method: 'GET', path: '/api/coder/themes', description: 'List themes', authentication: true },
                            createTheme: { method: 'POST', path: '/api/coder/themes', description: 'Create theme', authentication: true, permissions: ['coder', 'admin'] },
                            getNewsletters: { method: 'GET', path: '/api/coder/newsletter', description: 'List newsletters', authentication: true },
                            createNewsletter: { method: 'POST', path: '/api/coder/newsletter', description: 'Create newsletter', authentication: true, permissions: ['coder', 'admin'] }
                        }
                    },
                    docs: {
                        description: 'AI Onboarding Documentation',
                        endpoints: {
                            overview: { method: 'GET', path: '/api/docs/ai-onboarding', description: 'AI onboarding overview', authentication: false },
                            architecture: { method: 'GET', path: '/api/docs/ai-onboarding/architecture', description: 'System architecture', authentication: false },
                            authentication: { method: 'GET', path: '/api/docs/ai-onboarding/authentication', description: 'Authentication flow', authentication: false },
                            apiReference: { method: 'GET', path: '/api/docs/ai-onboarding/api-reference', description: 'Complete API reference', authentication: false },
                            integrationPatterns: { method: 'GET', path: '/api/docs/ai-onboarding/integration-patterns', description: 'Framework integration patterns', authentication: false },
                            typeScriptDefinitions: { method: 'GET', path: '/api/docs/ai-onboarding/typescript-definitions', description: 'TypeScript type definitions', authentication: false },
                            examples: { method: 'GET', path: '/api/docs/ai-onboarding/examples', description: 'Code examples', authentication: false },
                            requirements: { method: 'GET', path: '/api/docs/ai-onboarding/requirements', description: 'System requirements', authentication: false },
                            testing: { method: 'GET', path: '/api/docs/ai-onboarding/testing', description: 'Testing guidelines', authentication: false },
                            moduleEndpoints: { method: 'GET', path: '/api/docs/ai-onboarding/endpoints/:module', description: 'Module-specific endpoints', authentication: false },
                            schemas: { method: 'GET', path: '/api/docs/ai-onboarding/schemas', description: 'Data schemas', authentication: false },
                            workflow: { method: 'GET', path: '/api/docs/ai-onboarding/workflow', description: 'Development workflow', authentication: false },
                            openapi: { method: 'GET', path: '/api/docs/openapi', description: 'OpenAPI specification', authentication: false }
                        }
                    }
                }
            }
        };
        if (module) {
            return allEndpoints.core.modules[module] || { error: `Module '${module}' not found`, availableModules: Object.keys(allEndpoints.core.modules) };
        }
        return allEndpoints;
    }
    getIntegrationPatterns(framework) {
        const patterns = {
            nextjs: {
                framework: 'Next.js',
                description: 'React framework with SSR/SSG capabilities',
                setup: {
                    environment: {
                        'NEXT_PUBLIC_API_URL': 'http://localhost:8000',
                        'NEXT_PUBLIC_APP_NAME': 'Legalyard',
                        'NEXT_PUBLIC_APP_VERSION': '1.0.0'
                    },
                    dependencies: ['next', 'react', 'react-dom', 'typescript'],
                    devDependencies: ['@types/react', '@types/node']
                },
                patterns: {
                    apiClient: 'Centralized API client with token management',
                    authentication: 'Context-based auth state with token refresh',
                    routing: 'Protected routes with middleware',
                    stateManagement: 'React Context or Zustand',
                    errorHandling: 'Global error boundary with retry logic'
                },
                examples: {
                    apiClient: '/api/docs/ai-onboarding/examples?scenario=nextjs-api-client',
                    authHook: '/api/docs/ai-onboarding/examples?scenario=nextjs-auth-hook',
                    protectedRoute: '/api/docs/ai-onboarding/examples?scenario=nextjs-protected-route'
                }
            },
            react: {
                framework: 'React',
                description: 'JavaScript library for building user interfaces',
                setup: {
                    routing: 'React Router for navigation',
                    stateManagement: 'Redux Toolkit or Context API',
                    httpClient: 'Axios or Fetch API'
                },
                patterns: {
                    authentication: 'Higher-order components or hooks',
                    routing: 'Protected routes with React Router',
                    stateManagement: 'Redux slices for auth and data'
                }
            },
            vue: {
                framework: 'Vue.js',
                description: 'Progressive JavaScript framework',
                setup: {
                    routing: 'Vue Router with navigation guards',
                    stateManagement: 'Pinia or Vuex',
                    httpClient: 'Axios with interceptors'
                },
                patterns: {
                    authentication: 'Composables for auth state',
                    routing: 'Route guards for protection',
                    stateManagement: 'Pinia stores for data management'
                }
            },
            angular: {
                framework: 'Angular',
                description: 'Platform for building mobile and desktop web applications',
                setup: {
                    routing: 'Angular Router with guards',
                    stateManagement: 'NgRx or services',
                    httpClient: 'HttpClient with interceptors'
                },
                patterns: {
                    authentication: 'Guards and interceptors',
                    routing: 'Route guards and resolvers',
                    stateManagement: 'Services with RxJS'
                }
            }
        };
        if (framework) {
            return patterns[framework] || { error: `Framework '${framework}' not supported`, availableFrameworks: Object.keys(patterns) };
        }
        return patterns;
    }
    getTypeScriptDefinitions() {
        return {
            title: 'TypeScript Definitions for Legalyard Backend',
            description: 'Comprehensive type definitions for all API interactions',
            categories: {
                apiTypes: {
                    ApiResponse: 'interface ApiResponse<T = any> { data?: T; error?: string; statusCode?: number; }',
                    PaginatedResponse: 'interface PaginatedResponse<T> { data: T[]; pagination: { page: number; limit: number; total: number; pages: number; }; }',
                    ErrorResponse: 'interface ErrorResponse { statusCode: number; message: string; error: string; details?: Array<{ field: string; message: string; }>; }'
                },
                authTypes: {
                    LoginCredentials: 'interface LoginCredentials { email: string; password: string; totpCode?: string; }',
                    AuthResponse: 'interface AuthResponse { accessToken: string; refreshToken: string; user: User; expiresIn: number; }',
                    User: 'interface User { id: string; email: string; username?: string; roles: string[]; organizationId?: string; isActive: boolean; createdAt: string; updatedAt: string; }'
                },
                contentTypes: {
                    Content: 'interface Content { id: string; title: string; slug: string; body: string; status: "draft" | "published" | "archived"; type: "page" | "post" | "article"; authorId: string; tags: string[]; metadata: Record<string, any>; createdAt: string; updatedAt: string; }',
                    CreateContentData: 'interface CreateContentData { title: string; body: string; excerpt?: string; type: "page" | "post" | "article"; status?: "draft" | "published" | "archived"; tags?: string[]; metadata?: Record<string, any>; }'
                },
                serviceTypes: {
                    AuthService: 'interface AuthService { login(credentials: LoginCredentials): Promise<ApiResponse<AuthResponse>>; logout(): Promise<ApiResponse<void>>; refreshToken(): Promise<ApiResponse<AuthResponse>>; }',
                    ContentService: 'interface ContentService { getContent(): Promise<ApiResponse<PaginatedResponse<Content>>>; createContent(data: CreateContentData): Promise<ApiResponse<Content>>; }'
                }
            },
            fullDefinitionsUrl: '/api/docs/ai-onboarding/schemas?format=typescript',
            usage: {
                import: 'Import types from generated definitions file',
                validation: 'Use with runtime validation libraries like Zod',
                codegen: 'Generate from OpenAPI specification'
            }
        };
    }
    getCodeExamples(scenario) {
        const examples = {
            authentication: {
                title: 'Authentication Examples',
                login: {
                    description: 'Complete login implementation with error handling',
                    code: `
const login = async (credentials: LoginCredentials): Promise<AuthResponse | null> => {
  try {
    const response = await fetch('http://localhost:8000/api/auth/login', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(credentials)
    });

    const result = await response.json();

    if (response.ok) {
      localStorage.setItem('accessToken', result.accessToken);
      localStorage.setItem('refreshToken', result.refreshToken);
      return result;
    } else {
      throw new Error(result.message || 'Login failed');
    }
  } catch (error) {
    console.error('Login error:', error);
    return null;
  }
};
          `
                },
                tokenRefresh: {
                    description: 'Automatic token refresh with retry logic',
                    code: `
const refreshToken = async (): Promise<string | null> => {
  const refreshToken = localStorage.getItem('refreshToken');
  if (!refreshToken) return null;

  try {
    const response = await fetch('http://localhost:8000/api/auth/refresh', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ refreshToken })
    });

    if (response.ok) {
      const result = await response.json();
      localStorage.setItem('accessToken', result.accessToken);
      return result.accessToken;
    }
  } catch (error) {
    console.error('Token refresh failed:', error);
  }

  return null;
};
          `
                }
            },
            crud: {
                title: 'CRUD Operations Examples',
                createContent: {
                    description: 'Create new content with validation',
                    code: `
const createContent = async (contentData: CreateContentData): Promise<Content | null> => {
  const token = localStorage.getItem('accessToken');

  try {
    const response = await fetch('http://localhost:8000/api/core/coder/content', {
      method: 'POST',
      headers: {
        'Authorization': \`Bearer \${token}\`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(contentData)
    });

    if (response.status === 401) {
      const newToken = await refreshToken();
      if (newToken) {
        return createContent(contentData); // Retry with new token
      }
      throw new Error('Authentication failed');
    }

    const result = await response.json();
    return response.ok ? result.data : null;
  } catch (error) {
    console.error('Create content error:', error);
    return null;
  }
};
          `
                }
            },
            forms: {
                title: 'Form Handling Examples',
                genericForm: {
                    description: 'Generic form submission with validation',
                    code: `
const handleFormSubmit = async <T>(
  endpoint: string,
  formData: any,
  method: 'POST' | 'PUT' = 'POST'
): Promise<{ success: boolean; data?: T; error?: string }> => {
  const token = localStorage.getItem('accessToken');

  try {
    const response = await fetch(\`http://localhost:8000\${endpoint}\`, {
      method,
      headers: {
        'Authorization': \`Bearer \${token}\`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(formData)
    });

    const result = await response.json();

    if (response.ok) {
      return { success: true, data: result.data };
    } else {
      return { success: false, error: result.message };
    }
  } catch (error) {
    return { success: false, error: 'Network error occurred' };
  }
};
          `
                }
            }
        };
        if (scenario) {
            return examples[scenario] || { error: `Scenario '${scenario}' not found`, availableScenarios: Object.keys(examples) };
        }
        return examples;
    }
    getRequirements() {
        return {
            title: 'System Requirements and Constraints',
            systemRequirements: {
                backend: {
                    node: '>=20.0.0',
                    npm: '>=9.0.0',
                    mongodb: '>=7.0.0',
                    memory: '>=2GB RAM',
                    storage: '>=10GB available space'
                },
                frontend: {
                    node: '>=18.0.0',
                    browsers: ['Chrome >=90', 'Firefox >=88', 'Safari >=14', 'Edge >=90'],
                    frameworks: ['Next.js >=13', 'React >=18', 'Vue >=3', 'Angular >=15']
                }
            },
            apiConstraints: {
                authentication: {
                    tokenExpiry: '15 minutes (access), 7 days (refresh)',
                    maxDevices: 2,
                    sessionTimeout: '1 hour of inactivity',
                    mfaRequired: 'For admin accounts'
                },
                rateLimiting: {
                    default: '100 requests per minute per IP',
                    auth: '10 login attempts per minute per IP',
                    admin: '50 requests per minute per user'
                },
                dataLimits: {
                    requestSize: '10MB maximum',
                    responseSize: '50MB maximum',
                    fileUpload: '100MB maximum',
                    pagination: '100 items maximum per page'
                }
            },
            securityRequirements: {
                https: 'Required in production',
                cors: 'Configured for specific origins',
                headers: 'Security headers via Helmet',
                validation: 'Input validation on all endpoints',
                sanitization: 'XSS protection and data sanitization'
            },
            performanceRequirements: {
                responseTime: '<200ms for simple queries, <2s for complex operations',
                availability: '99.9% uptime',
                concurrency: '1000 concurrent users',
                throughput: '10,000 requests per minute'
            }
        };
    }
    getTestingGuidelines() {
        return {
            title: 'Testing Guidelines and Procedures',
            apiTesting: {
                tools: ['curl', 'Postman', 'Insomnia', 'Jest', 'Supertest'],
                procedures: {
                    authentication: [
                        'Test login with valid credentials',
                        'Test login with invalid credentials',
                        'Test token refresh flow',
                        'Test token expiration handling',
                        'Test logout functionality'
                    ],
                    endpoints: [
                        'Test all HTTP methods',
                        'Test with and without authentication',
                        'Test with different user roles',
                        'Test input validation',
                        'Test error responses'
                    ],
                    integration: [
                        'Test complete user workflows',
                        'Test data consistency',
                        'Test concurrent operations',
                        'Test rate limiting',
                        'Test error recovery'
                    ]
                }
            },
            frontendTesting: {
                tools: ['Jest', 'React Testing Library', 'Cypress', 'Playwright'],
                procedures: {
                    components: [
                        'Test component rendering',
                        'Test user interactions',
                        'Test API integration',
                        'Test error states',
                        'Test loading states'
                    ],
                    integration: [
                        'Test authentication flows',
                        'Test protected routes',
                        'Test form submissions',
                        'Test data fetching',
                        'Test error handling'
                    ]
                }
            },
            testCommands: {
                backend: {
                    unit: 'npm run test',
                    e2e: 'npm run test:e2e',
                    coverage: 'npm run test:cov'
                },
                apiEndpoints: {
                    health: 'curl http://localhost:8000/api/health',
                    login: 'curl -X POST http://localhost:8000/api/auth/login -H "Content-Type: application/json" -d \'{"email":"<EMAIL>","password":"password123"}\'',
                    protectedEndpoint: 'curl http://localhost:8000/api/core/admin/users -H "Authorization: Bearer YOUR_TOKEN"'
                }
            }
        };
    }
    getModuleEndpoints(module) {
        const moduleEndpoints = {
            auth: {
                module: 'Authentication Module',
                description: 'Handles user authentication and session management',
                endpoints: {
                    login: { method: 'POST', path: '/api/auth/login', description: 'User login', public: true },
                    register: { method: 'POST', path: '/api/auth/register', description: 'User registration', public: true },
                    refresh: { method: 'POST', path: '/api/auth/refresh', description: 'Token refresh', public: true },
                    logout: { method: 'POST', path: '/api/auth/logout', description: 'User logout', protected: true },
                    verify: { method: 'GET', path: '/api/auth/verify', description: 'Token verification', protected: true },
                    validate: { method: 'POST', path: '/api/auth/validate', description: 'Token validation (internal)', internal: true }
                }
            },
            admin: {
                module: 'Admin Module',
                description: 'User and organization management for administrators',
                endpoints: {
                    getUsers: { method: 'GET', path: '/api/admin/users', description: 'List users', roles: ['admin'] },
                    createUser: { method: 'POST', path: '/api/admin/users', description: 'Create user', roles: ['admin'] },
                    updateUser: { method: 'PUT', path: '/api/admin/users/:id', description: 'Update user', roles: ['admin'] },
                    deleteUser: { method: 'DELETE', path: '/api/admin/users/:id', description: 'Delete user', roles: ['admin'] },
                    getTenants: { method: 'GET', path: '/api/admin/tenants', description: 'List organizations', roles: ['admin'] },
                    createTenant: { method: 'POST', path: '/api/admin/tenants', description: 'Create organization', roles: ['admin'] },
                    getPermissions: { method: 'GET', path: '/api/admin/permissions', description: 'List permissions', roles: ['admin'] },
                    getAuditLogs: { method: 'GET', path: '/api/admin/audit-logs', description: 'View audit logs', roles: ['admin'] }
                }
            },
            coder: {
                module: 'Coder Module (CMS)',
                description: 'Content management system for content creators',
                endpoints: {
                    getContent: { method: 'GET', path: '/api/coder/content', description: 'List content', roles: ['coder', 'admin'] },
                    createContent: { method: 'POST', path: '/api/coder/content', description: 'Create content', roles: ['coder', 'admin'] },
                    updateContent: { method: 'PUT', path: '/api/coder/content/:id', description: 'Update content', roles: ['coder', 'admin'] },
                    deleteContent: { method: 'DELETE', path: '/api/coder/content/:id', description: 'Delete content', roles: ['coder', 'admin'] },
                    getForms: { method: 'GET', path: '/api/coder/forms', description: 'List forms', roles: ['coder', 'admin'] },
                    createForm: { method: 'POST', path: '/api/coder/forms', description: 'Create form', roles: ['coder', 'admin'] },
                    getThemes: { method: 'GET', path: '/api/coder/themes', description: 'List themes', roles: ['coder', 'admin'] },
                    createTheme: { method: 'POST', path: '/api/coder/themes', description: 'Create theme', roles: ['coder', 'admin'] },
                    getNewsletters: { method: 'GET', path: '/api/coder/newsletter', description: 'List newsletters', roles: ['coder', 'admin'] },
                    createNewsletter: { method: 'POST', path: '/api/coder/newsletter', description: 'Create newsletter', roles: ['coder', 'admin'] }
                }
            },
            docs: {
                module: 'Documentation Module',
                description: 'AI onboarding and API documentation',
                endpoints: {
                    overview: { method: 'GET', path: '/api/docs/ai-onboarding', description: 'AI onboarding overview', public: true },
                    architecture: { method: 'GET', path: '/api/docs/ai-onboarding/architecture', description: 'System architecture', public: true },
                    authentication: { method: 'GET', path: '/api/docs/ai-onboarding/authentication', description: 'Authentication flow', public: true },
                    apiReference: { method: 'GET', path: '/api/docs/ai-onboarding/api-reference', description: 'API reference', public: true },
                    integrationPatterns: { method: 'GET', path: '/api/docs/ai-onboarding/integration-patterns', description: 'Integration patterns', public: true },
                    typeScriptDefinitions: { method: 'GET', path: '/api/docs/ai-onboarding/typescript-definitions', description: 'TypeScript definitions', public: true },
                    examples: { method: 'GET', path: '/api/docs/ai-onboarding/examples', description: 'Code examples', public: true },
                    requirements: { method: 'GET', path: '/api/docs/ai-onboarding/requirements', description: 'System requirements', public: true },
                    testing: { method: 'GET', path: '/api/docs/ai-onboarding/testing', description: 'Testing guidelines', public: true },
                    schemas: { method: 'GET', path: '/api/docs/ai-onboarding/schemas', description: 'Data schemas', public: true },
                    workflow: { method: 'GET', path: '/api/docs/ai-onboarding/workflow', description: 'Development workflow', public: true },
                    openapi: { method: 'GET', path: '/api/docs/openapi', description: 'OpenAPI specification', public: true }
                }
            }
        };
        return moduleEndpoints[module] || { error: `Module '${module}' not found`, availableModules: Object.keys(moduleEndpoints) };
    }
    getSchemas(format) {
        const schemas = {
            title: 'Data Schemas and Models',
            format: format || 'json',
            schemas: {
                User: {
                    type: 'object',
                    properties: {
                        id: { type: 'string', description: 'Unique user identifier' },
                        email: { type: 'string', format: 'email', description: 'User email address' },
                        username: { type: 'string', description: 'Optional username' },
                        firstName: { type: 'string', description: 'User first name' },
                        lastName: { type: 'string', description: 'User last name' },
                        roles: { type: 'array', items: { type: 'string' }, description: 'User roles' },
                        organizationId: { type: 'string', description: 'Organization ID' },
                        isActive: { type: 'boolean', description: 'Account status' },
                        isEmailVerified: { type: 'boolean', description: 'Email verification status' },
                        createdAt: { type: 'string', format: 'date-time', description: 'Creation timestamp' },
                        updatedAt: { type: 'string', format: 'date-time', description: 'Last update timestamp' }
                    },
                    required: ['id', 'email', 'roles', 'isActive', 'isEmailVerified', 'createdAt', 'updatedAt']
                },
                Content: {
                    type: 'object',
                    properties: {
                        id: { type: 'string', description: 'Unique content identifier' },
                        title: { type: 'string', description: 'Content title' },
                        slug: { type: 'string', description: 'URL-friendly identifier' },
                        body: { type: 'string', description: 'Content body' },
                        excerpt: { type: 'string', description: 'Content excerpt' },
                        status: { type: 'string', enum: ['draft', 'published', 'archived'], description: 'Content status' },
                        type: { type: 'string', enum: ['page', 'post', 'article'], description: 'Content type' },
                        authorId: { type: 'string', description: 'Author user ID' },
                        tags: { type: 'array', items: { type: 'string' }, description: 'Content tags' },
                        metadata: { type: 'object', description: 'Additional metadata' },
                        publishedAt: { type: 'string', format: 'date-time', description: 'Publication timestamp' },
                        createdAt: { type: 'string', format: 'date-time', description: 'Creation timestamp' },
                        updatedAt: { type: 'string', format: 'date-time', description: 'Last update timestamp' }
                    },
                    required: ['id', 'title', 'slug', 'body', 'status', 'type', 'authorId', 'tags', 'createdAt', 'updatedAt']
                },
                Organization: {
                    type: 'object',
                    properties: {
                        id: { type: 'string', description: 'Unique organization identifier' },
                        name: { type: 'string', description: 'Organization name' },
                        domain: { type: 'string', description: 'Organization domain' },
                        settings: {
                            type: 'object',
                            properties: {
                                allowedDomains: { type: 'array', items: { type: 'string' } },
                                maxUsers: { type: 'number' },
                                features: { type: 'array', items: { type: 'string' } }
                            }
                        },
                        isActive: { type: 'boolean', description: 'Organization status' },
                        createdAt: { type: 'string', format: 'date-time', description: 'Creation timestamp' },
                        updatedAt: { type: 'string', format: 'date-time', description: 'Last update timestamp' }
                    },
                    required: ['id', 'name', 'settings', 'isActive', 'createdAt', 'updatedAt']
                }
            }
        };
        if (format === 'typescript') {
            return {
                ...schemas,
                typeScriptDefinitions: `
interface User {
  id: string;
  email: string;
  username?: string;
  firstName?: string;
  lastName?: string;
  roles: string[];
  organizationId?: string;
  isActive: boolean;
  isEmailVerified: boolean;
  createdAt: string;
  updatedAt: string;
}

interface Content {
  id: string;
  title: string;
  slug: string;
  body: string;
  excerpt?: string;
  status: 'draft' | 'published' | 'archived';
  type: 'page' | 'post' | 'article';
  authorId: string;
  tags: string[];
  metadata: Record<string, any>;
  publishedAt?: string;
  createdAt: string;
  updatedAt: string;
}

interface Organization {
  id: string;
  name: string;
  domain?: string;
  settings: {
    allowedDomains?: string[];
    maxUsers?: number;
    features: string[];
  };
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}
        `
            };
        }
        return schemas;
    }
    getWorkflow() {
        return {
            title: 'Development Workflow and Best Practices',
            aiAgentWorkflow: {
                step1: {
                    title: 'Understand Architecture',
                    description: 'Get system overview and architecture',
                    endpoint: 'GET /api/docs/ai-onboarding/architecture',
                    action: 'Study the Gateway → Core → Database flow'
                },
                step2: {
                    title: 'Learn Authentication',
                    description: 'Understand token-based authentication',
                    endpoint: 'GET /api/docs/ai-onboarding/authentication',
                    action: 'Implement login/logout and token refresh patterns'
                },
                step3: {
                    title: 'Explore API Endpoints',
                    description: 'Get complete API reference',
                    endpoint: 'GET /api/docs/ai-onboarding/api-reference',
                    action: 'Map out required endpoints for your application'
                },
                step4: {
                    title: 'Choose Integration Pattern',
                    description: 'Select framework-specific patterns',
                    endpoint: 'GET /api/docs/ai-onboarding/integration-patterns',
                    action: 'Implement API client and authentication service'
                },
                step5: {
                    title: 'Implement Type Safety',
                    description: 'Use TypeScript definitions',
                    endpoint: 'GET /api/docs/ai-onboarding/typescript-definitions',
                    action: 'Add type definitions to your project'
                },
                step6: {
                    title: 'Build Components',
                    description: 'Use code examples as reference',
                    endpoint: 'GET /api/docs/ai-onboarding/examples',
                    action: 'Implement UI components with API integration'
                },
                step7: {
                    title: 'Test Integration',
                    description: 'Validate implementation',
                    endpoint: 'GET /api/docs/ai-onboarding/testing',
                    action: 'Test authentication flows and API calls'
                }
            },
            bestPractices: {
                apiIntegration: [
                    'Always use Gateway endpoints (port 8000) for client requests',
                    'Implement proper error handling with retry logic',
                    'Use TypeScript for type safety',
                    'Handle token refresh automatically',
                    'Implement loading states for better UX',
                    'Validate user permissions before showing UI elements'
                ],
                security: [
                    'Store tokens securely (avoid localStorage in production)',
                    'Implement CSRF protection',
                    'Use HTTPS in production',
                    'Validate all user inputs',
                    'Log security events',
                    'Implement proper session management'
                ],
                performance: [
                    'Implement request caching where appropriate',
                    'Use pagination for large datasets',
                    'Optimize bundle size',
                    'Implement lazy loading',
                    'Use proper error boundaries',
                    'Monitor API response times'
                ]
            },
            troubleshooting: {
                commonIssues: {
                    authenticationFailed: {
                        symptoms: '401 Unauthorized responses',
                        solutions: ['Check token validity', 'Implement token refresh', 'Verify API endpoint URLs']
                    },
                    corsErrors: {
                        symptoms: 'CORS policy errors in browser',
                        solutions: ['Configure CORS_ORIGIN environment variable', 'Use proper request headers', 'Check API base URL']
                    },
                    rateLimitExceeded: {
                        symptoms: '429 Too Many Requests',
                        solutions: ['Implement request throttling', 'Add retry with exponential backoff', 'Check rate limit headers']
                    }
                }
            }
        };
    }
    getOpenApiSpec() {
        return {
            openapi: '3.0.0',
            info: {
                title: 'Legalyard Backend API',
                version: '1.0.0',
                description: 'Comprehensive API for Legalyard Backend with Gateway and Core applications',
                contact: {
                    name: 'API Support',
                    url: 'http://localhost:8000/api/docs'
                }
            },
            servers: [
                { url: 'http://localhost:8000', description: 'Gateway (Development)' },
                { url: 'http://localhost:8001', description: 'Core (Internal)' }
            ],
            paths: {
                '/api/health': {
                    get: {
                        summary: 'Health check',
                        responses: {
                            '200': {
                                description: 'Service health status',
                                content: {
                                    'application/json': {
                                        schema: {
                                            type: 'object',
                                            properties: {
                                                status: { type: 'string' },
                                                timestamp: { type: 'string' },
                                                service: { type: 'string' }
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                },
                '/api/auth/login': {
                    post: {
                        summary: 'User authentication',
                        requestBody: {
                            required: true,
                            content: {
                                'application/json': {
                                    schema: {
                                        type: 'object',
                                        properties: {
                                            email: { type: 'string', format: 'email' },
                                            password: { type: 'string' },
                                            totpCode: { type: 'string' }
                                        },
                                        required: ['email', 'password']
                                    }
                                }
                            }
                        },
                        responses: {
                            '200': {
                                description: 'Authentication successful',
                                content: {
                                    'application/json': {
                                        schema: {
                                            type: 'object',
                                            properties: {
                                                accessToken: { type: 'string' },
                                                refreshToken: { type: 'string' },
                                                user: { $ref: '#/components/schemas/User' },
                                                expiresIn: { type: 'number' }
                                            }
                                        }
                                    }
                                }
                            },
                            '401': { description: 'Invalid credentials' }
                        }
                    }
                }
            },
            components: {
                schemas: {
                    User: {
                        type: 'object',
                        properties: {
                            id: { type: 'string' },
                            email: { type: 'string', format: 'email' },
                            username: { type: 'string' },
                            roles: { type: 'array', items: { type: 'string' } },
                            isActive: { type: 'boolean' },
                            createdAt: { type: 'string', format: 'date-time' },
                            updatedAt: { type: 'string', format: 'date-time' }
                        }
                    }
                },
                securitySchemes: {
                    bearerAuth: {
                        type: 'http',
                        scheme: 'bearer',
                        bearerFormat: 'JWT'
                    }
                }
            },
            security: [{ bearerAuth: [] }]
        };
    }
    getCompleteAiAgentInfo() {
        return {
            meta: {
                title: 'Legalyard Backend - Complete AI Agent Integration Guide',
                version: '1.0.0',
                description: 'Single endpoint containing all information needed for AI agents to understand and integrate with the Legalyard Backend system',
                lastUpdated: new Date().toISOString(),
                masterEndpoint: 'http://localhost:8000/api/core/docs/ai-agent-complete',
                quickAccess: {
                    gatewaySwagger: 'http://localhost:8000/api/docs',
                    coreSwagger: 'http://localhost:8001/api/docs',
                    gatewayHealth: 'http://localhost:8000/api/health',
                    coreHealth: 'http://localhost:8001/api/health'
                }
            },
            systemOverview: {
                architecture: 'NestJS Monorepo with Gateway + Core Applications',
                pattern: 'API Gateway + Microservice-Ready Monolith',
                requestFlow: 'Client → Gateway (8000) → Core (8001) → MongoDB',
                primaryIntegrationPoint: 'http://localhost:8000 (Gateway)',
                applications: {
                    gateway: {
                        port: 8000,
                        purpose: 'API proxy, authentication, rate limiting, security layer',
                        technology: 'NestJS + Express + Swagger',
                        responsibilities: [
                            'Central entry point for all client applications',
                            'Authentication token validation and forwarding',
                            'Rate limiting (100 requests/minute default)',
                            'CORS and security headers (Helmet)',
                            'Request proxying to Core application',
                            'IP allowlisting for admin access'
                        ],
                        endpoints: {
                            health: 'GET /api/health',
                            status: 'GET /api',
                            authProxy: 'POST /api/auth/* (forwarded to core)',
                            coreProxy: 'ALL /api/core/* (proxied to core with /api prefix)'
                        }
                    },
                    core: {
                        port: 8001,
                        purpose: 'Business logic engine with all application modules',
                        technology: 'NestJS + Express + MongoDB + Mongoose + Swagger',
                        responsibilities: [
                            'Authentication and session management',
                            'User and organization administration',
                            'Content management system (CMS)',
                            'Database operations and business logic',
                            'Audit logging and security',
                            'AI onboarding documentation'
                        ],
                        modules: {
                            auth: 'Authentication and authorization',
                            admin: 'User and organization management',
                            coder: 'Content management system',
                            docs: 'AI onboarding documentation',
                            shared: 'Common utilities and services'
                        }
                    }
                },
                database: {
                    type: 'MongoDB',
                    orm: 'Mongoose',
                    collections: [
                        'users - User accounts and profiles',
                        'organizations - Tenant organizations',
                        'sessions - User sessions and device tracking',
                        'content - CMS content items',
                        'audit_logs - System audit trail'
                    ]
                }
            },
            authentication: {
                pattern: 'Opaque Token + Refresh Token (Phantom Pattern)',
                security: 'Bearer token authentication with automatic refresh',
                tokenTypes: {
                    accessToken: {
                        purpose: 'API access authorization',
                        lifetime: '15 minutes',
                        format: 'Opaque string (not JWT for security)',
                        storage: 'Memory or secure storage (avoid localStorage in production)'
                    },
                    refreshToken: {
                        purpose: 'Access token renewal',
                        lifetime: '7 days',
                        format: 'Opaque string',
                        storage: 'Secure HTTP-only cookie or secure storage'
                    }
                },
                flow: {
                    step1: 'POST /api/auth/login with credentials',
                    step2: 'Receive accessToken + refreshToken + user info',
                    step3: 'Include Authorization: Bearer <accessToken> in all requests',
                    step4: 'Auto-refresh token on 401 responses',
                    step5: 'POST /api/auth/logout to invalidate session'
                },
                endpoints: {
                    login: {
                        method: 'POST',
                        url: '/api/auth/login',
                        request: { email: 'string', password: 'string', totpCode: 'string (optional)' },
                        response: { accessToken: 'string', refreshToken: 'string', user: 'User', expiresIn: 'number' }
                    },
                    register: {
                        method: 'POST',
                        url: '/api/auth/register',
                        request: { email: 'string', password: 'string', username: 'string (optional)', firstName: 'string (optional)', lastName: 'string (optional)' }
                    },
                    refresh: {
                        method: 'POST',
                        url: '/api/auth/refresh',
                        request: { refreshToken: 'string' },
                        response: { accessToken: 'string', refreshToken: 'string', expiresIn: 'number' }
                    },
                    logout: {
                        method: 'POST',
                        url: '/api/auth/logout',
                        headers: { Authorization: 'Bearer <accessToken>' }
                    },
                    verify: {
                        method: 'GET',
                        url: '/api/auth/verify',
                        headers: { Authorization: 'Bearer <accessToken>' },
                        response: { user: 'User', tokenValid: 'boolean' }
                    }
                },
                securityFeatures: [
                    'Opaque tokens prevent token inspection',
                    'Short access token lifetime reduces exposure',
                    'Device tracking (max 2 devices per user)',
                    'Session invalidation on logout',
                    'TOTP MFA support for admin accounts',
                    'IP allowlisting for admin access'
                ]
            },
            apiEndpoints: {
                gateway: {
                    baseUrl: 'http://localhost:8000',
                    description: 'Primary integration point - use this for all client requests',
                    directEndpoints: {
                        health: { method: 'GET', path: '/api/health', auth: false },
                        status: { method: 'GET', path: '/api', auth: false },
                        login: { method: 'POST', path: '/api/auth/login', auth: false },
                        register: { method: 'POST', path: '/api/auth/register', auth: false },
                        refresh: { method: 'POST', path: '/api/auth/refresh', auth: false },
                        logout: { method: 'POST', path: '/api/auth/logout', auth: true },
                        verify: { method: 'GET', path: '/api/auth/verify', auth: true }
                    },
                    proxyEndpoints: {
                        pattern: '/api/core/*',
                        description: 'All /api/core/* requests are proxied to Core app',
                        transformation: '/api/core/admin/users → http://localhost:8001/api/admin/users',
                        examples: [
                            'GET /api/core/admin/users → List users',
                            'POST /api/core/coder/content → Create content',
                            'GET /api/core/docs/ai-agent-complete → This endpoint'
                        ]
                    }
                },
                core: {
                    baseUrl: 'http://localhost:8001 (Access via Gateway: /api/core/*)',
                    description: 'Business logic endpoints - access through Gateway proxy',
                    modules: {
                        admin: {
                            description: 'User and organization management (Admin only)',
                            endpoints: {
                                'GET /api/admin/users': 'List all users',
                                'POST /api/admin/users': 'Create new user',
                                'PUT /api/admin/users/:id': 'Update user',
                                'DELETE /api/admin/users/:id': 'Delete user',
                                'GET /api/admin/tenants': 'List organizations',
                                'POST /api/admin/tenants': 'Create organization',
                                'GET /api/admin/permissions': 'List permissions',
                                'GET /api/admin/audit-logs': 'View audit logs'
                            }
                        },
                        coder: {
                            description: 'Content Management System',
                            endpoints: {
                                'GET /api/coder/content': 'List content items',
                                'POST /api/coder/content': 'Create new content',
                                'PUT /api/coder/content/:id': 'Update content',
                                'DELETE /api/coder/content/:id': 'Delete content',
                                'GET /api/coder/forms': 'List forms',
                                'POST /api/coder/forms': 'Create form',
                                'GET /api/coder/themes': 'List themes',
                                'POST /api/coder/themes': 'Create theme',
                                'GET /api/coder/newsletter': 'List newsletters',
                                'POST /api/coder/newsletter': 'Create newsletter'
                            }
                        },
                        docs: {
                            description: 'AI Onboarding Documentation',
                            endpoints: {
                                'GET /api/docs/ai-agent-complete': 'Complete AI agent info (this endpoint)',
                                'GET /api/docs/ai-onboarding': 'AI onboarding overview',
                                'GET /api/docs/ai-onboarding/architecture': 'System architecture',
                                'GET /api/docs/ai-onboarding/authentication': 'Authentication flow',
                                'GET /api/docs/ai-onboarding/api-reference': 'Complete API reference',
                                'GET /api/docs/ai-onboarding/integration-patterns': 'Framework patterns',
                                'GET /api/docs/ai-onboarding/typescript-definitions': 'TypeScript types',
                                'GET /api/docs/ai-onboarding/examples': 'Code examples',
                                'GET /api/docs/ai-onboarding/requirements': 'System requirements',
                                'GET /api/docs/ai-onboarding/testing': 'Testing guidelines',
                                'GET /api/docs/ai-onboarding/workflow': 'Development workflow',
                                'GET /api/docs/openapi': 'OpenAPI specification'
                            }
                        }
                    }
                }
            },
            dataModels: {
                User: {
                    properties: {
                        id: 'string - Unique identifier',
                        email: 'string - Email address (unique)',
                        username: 'string - Optional username',
                        firstName: 'string - First name',
                        lastName: 'string - Last name',
                        roles: 'string[] - User roles (default: ["user"])',
                        organizationId: 'string - Organization reference',
                        isActive: 'boolean - Account status',
                        isEmailVerified: 'boolean - Email verification status',
                        createdAt: 'string - Creation timestamp',
                        updatedAt: 'string - Last update timestamp'
                    },
                    required: ['id', 'email', 'roles', 'isActive', 'isEmailVerified']
                },
                Content: {
                    properties: {
                        id: 'string - Unique identifier',
                        title: 'string - Content title',
                        slug: 'string - URL-friendly identifier',
                        body: 'string - Content body',
                        excerpt: 'string - Content excerpt',
                        status: '"draft" | "published" | "archived"',
                        type: '"page" | "post" | "article"',
                        authorId: 'string - Author user ID',
                        tags: 'string[] - Content tags',
                        metadata: 'Record<string, any> - Additional metadata',
                        publishedAt: 'string - Publication timestamp',
                        createdAt: 'string - Creation timestamp',
                        updatedAt: 'string - Last update timestamp'
                    },
                    required: ['id', 'title', 'slug', 'body', 'status', 'type', 'authorId']
                },
                Organization: {
                    properties: {
                        id: 'string - Unique identifier',
                        name: 'string - Organization name',
                        domain: 'string - Organization domain',
                        settings: 'object - Configuration settings',
                        isActive: 'boolean - Organization status',
                        createdAt: 'string - Creation timestamp',
                        updatedAt: 'string - Last update timestamp'
                    },
                    required: ['id', 'name', 'settings', 'isActive']
                }
            },
            integrationGuide: {
                quickStart: {
                    step1: {
                        title: 'Set Environment Variables',
                        code: 'NEXT_PUBLIC_API_URL=http://localhost:8000'
                    },
                    step2: {
                        title: 'Create API Client',
                        code: `
class ApiClient {
  constructor(baseURL) { this.baseURL = baseURL; }
  setToken(token) { this.token = token; }
  async request(endpoint, options = {}) {
    const headers = { 'Content-Type': 'application/json', ...options.headers };
    if (this.token) headers.Authorization = \`Bearer \${this.token}\`;
    const response = await fetch(\`\${this.baseURL}\${endpoint}\`, { ...options, headers });
    return response.json();
  }
}
const apiClient = new ApiClient('http://localhost:8000');
            `
                    },
                    step3: {
                        title: 'Implement Authentication',
                        code: `
const login = async (credentials) => {
  const result = await apiClient.request('/api/auth/login', {
    method: 'POST',
    body: JSON.stringify(credentials)
  });
  if (result.accessToken) {
    localStorage.setItem('accessToken', result.accessToken);
    apiClient.setToken(result.accessToken);
  }
  return result;
};
            `
                    },
                    step4: {
                        title: 'Make API Calls',
                        code: `
// Get users (admin only)
const users = await apiClient.request('/api/core/admin/users');

// Create content
const content = await apiClient.request('/api/core/coder/content', {
  method: 'POST',
  body: JSON.stringify({
    title: 'New Article',
    body: 'Content body...',
    type: 'article',
    status: 'draft'
  })
});
            `
                    }
                },
                frameworkPatterns: {
                    nextjs: {
                        environment: 'NEXT_PUBLIC_API_URL=http://localhost:8000',
                        apiClient: 'Centralized API client with token management',
                        authentication: 'Context-based auth state with token refresh',
                        routing: 'Protected routes with middleware',
                        stateManagement: 'React Context or Zustand'
                    },
                    react: {
                        routing: 'React Router for navigation',
                        stateManagement: 'Redux Toolkit or Context API',
                        authentication: 'Higher-order components or hooks'
                    },
                    vue: {
                        routing: 'Vue Router with navigation guards',
                        stateManagement: 'Pinia or Vuex',
                        authentication: 'Composables for auth state'
                    }
                },
                errorHandling: {
                    pattern: 'Check response status and handle errors gracefully',
                    tokenRefresh: 'Automatically refresh on 401 responses',
                    retryLogic: 'Implement exponential backoff for retries',
                    userFeedback: 'Show appropriate error messages to users'
                }
            },
            systemRequirements: {
                backend: {
                    node: '>=20.0.0',
                    npm: '>=9.0.0',
                    mongodb: '>=7.0.0',
                    memory: '>=2GB RAM',
                    storage: '>=10GB available space'
                },
                frontend: {
                    node: '>=18.0.0',
                    browsers: ['Chrome >=90', 'Firefox >=88', 'Safari >=14', 'Edge >=90'],
                    frameworks: ['Next.js >=13', 'React >=18', 'Vue >=3', 'Angular >=15']
                },
                apiConstraints: {
                    rateLimit: '100 requests per minute per IP',
                    requestSize: '10MB maximum',
                    responseSize: '50MB maximum',
                    tokenExpiry: '15 minutes (access), 7 days (refresh)',
                    maxDevices: '2 per user'
                }
            },
            testingAndValidation: {
                healthChecks: {
                    gateway: 'curl http://localhost:8000/api/health',
                    core: 'curl http://localhost:8001/api/health'
                },
                authenticationTest: {
                    login: 'curl -X POST http://localhost:8000/api/auth/login -H "Content-Type: application/json" -d \'{"email":"<EMAIL>","password":"password123"}\'',
                    protectedEndpoint: 'curl http://localhost:8000/api/core/admin/users -H "Authorization: Bearer YOUR_TOKEN"'
                },
                swaggerDocumentation: {
                    gateway: 'http://localhost:8000/api/docs',
                    core: 'http://localhost:8001/api/docs'
                }
            },
            bestPractices: {
                security: [
                    'Always use Gateway endpoints (port 8000) for client integration',
                    'Store tokens securely (avoid localStorage in production)',
                    'Implement proper token refresh logic',
                    'Use HTTPS in production',
                    'Validate all user inputs',
                    'Handle authentication errors gracefully'
                ],
                performance: [
                    'Implement request caching where appropriate',
                    'Use pagination for large datasets',
                    'Implement loading states for better UX',
                    'Monitor API response times',
                    'Use proper error boundaries'
                ],
                development: [
                    'Use TypeScript for type safety',
                    'Follow RESTful conventions',
                    'Implement proper error handling',
                    'Test API endpoints before implementing UI',
                    'Validate user permissions before showing UI elements',
                    'Log important events for debugging'
                ]
            },
            troubleshooting: {
                commonIssues: {
                    authenticationFailed: {
                        symptoms: '401 Unauthorized responses',
                        solutions: ['Check token validity', 'Implement token refresh', 'Verify API endpoint URLs']
                    },
                    corsErrors: {
                        symptoms: 'CORS policy errors in browser',
                        solutions: ['Configure CORS_ORIGIN environment variable', 'Use proper request headers', 'Check API base URL']
                    },
                    rateLimitExceeded: {
                        symptoms: '429 Too Many Requests',
                        solutions: ['Implement request throttling', 'Add retry with exponential backoff', 'Check rate limit headers']
                    },
                    proxyErrors: {
                        symptoms: '404 errors when accessing /api/core/* endpoints',
                        solutions: ['Verify Gateway proxy configuration', 'Check Core service availability', 'Ensure proper URL transformation']
                    }
                }
            },
            nextSteps: {
                forNewAiAgents: [
                    '1. Test this endpoint: GET http://localhost:8000/api/core/docs/ai-agent-complete',
                    '2. Verify system health: GET http://localhost:8000/api/health',
                    '3. Explore Swagger docs: http://localhost:8000/api/docs',
                    '4. Implement authentication flow using provided examples',
                    '5. Build API client using the integration patterns',
                    '6. Test with sample API calls to verify integration',
                    '7. Implement error handling and token refresh logic'
                ],
                additionalResources: [
                    'Detailed API Reference: GET /api/core/docs/ai-onboarding/api-reference',
                    'Code Examples: GET /api/core/docs/ai-onboarding/examples',
                    'TypeScript Definitions: GET /api/core/docs/ai-onboarding/typescript-definitions',
                    'Testing Guidelines: GET /api/core/docs/ai-onboarding/testing',
                    'Development Workflow: GET /api/core/docs/ai-onboarding/workflow'
                ]
            }
        };
    }
    generatePostmanCollection() {
        const timestamp = new Date().toISOString();
        return {
            info: {
                _postman_id: `legalyard-backend-${Date.now()}`,
                name: 'Legalyard Backend API',
                description: 'Complete API collection for Legalyard Backend system with Gateway and Core applications',
                version: '1.0.0',
                schema: 'https://schema.getpostman.com/json/collection/v2.1.0/collection.json',
                _exporter_id: 'legalyard-backend',
                _collection_link: 'http://localhost:8000/api/core/docs/postman-collection'
            },
            auth: {
                type: 'bearer',
                bearer: [
                    {
                        key: 'token',
                        value: '{{accessToken}}',
                        type: 'string'
                    }
                ]
            },
            event: [
                {
                    listen: 'prerequest',
                    script: {
                        type: 'text/javascript',
                        exec: [
                            '// Auto-refresh token if expired',
                            'if (pm.globals.get("accessToken") && pm.globals.get("refreshToken")) {',
                            '    const tokenExpiry = pm.globals.get("tokenExpiry");',
                            '    if (tokenExpiry && new Date() > new Date(tokenExpiry)) {',
                            '        console.log("Token expired, refreshing...");',
                            '        pm.sendRequest({',
                            '            url: pm.globals.get("gatewayUrl") + "/api/auth/refresh",',
                            '            method: "POST",',
                            '            header: { "Content-Type": "application/json" },',
                            '            body: {',
                            '                mode: "raw",',
                            '                raw: JSON.stringify({ refreshToken: pm.globals.get("refreshToken") })',
                            '            }',
                            '        }, function (err, res) {',
                            '            if (!err && res.code === 200) {',
                            '                const data = res.json();',
                            '                pm.globals.set("accessToken", data.accessToken);',
                            '                pm.globals.set("refreshToken", data.refreshToken);',
                            '                const expiry = new Date(Date.now() + (data.expiresIn * 1000));',
                            '                pm.globals.set("tokenExpiry", expiry.toISOString());',
                            '                console.log("Token refreshed successfully");',
                            '            }',
                            '        });',
                            '    }',
                            '}'
                        ]
                    }
                }
            ],
            variable: [
                {
                    key: 'gatewayUrl',
                    value: 'http://localhost:8000',
                    type: 'string'
                },
                {
                    key: 'coreUrl',
                    value: 'http://localhost:8001',
                    type: 'string'
                },
                {
                    key: 'accessToken',
                    value: '',
                    type: 'string'
                },
                {
                    key: 'refreshToken',
                    value: '',
                    type: 'string'
                }
            ],
            item: [
                {
                    name: '🏠 System Health & Status',
                    item: [
                        {
                            name: 'Gateway Health Check',
                            request: {
                                method: 'GET',
                                header: [],
                                url: {
                                    raw: '{{gatewayUrl}}/api/health',
                                    host: ['{{gatewayUrl}}'],
                                    path: ['api', 'health']
                                },
                                description: 'Check Gateway application health status'
                            },
                            response: []
                        },
                        {
                            name: 'Core Health Check',
                            request: {
                                method: 'GET',
                                header: [],
                                url: {
                                    raw: '{{coreUrl}}/api/health',
                                    host: ['{{coreUrl}}'],
                                    path: ['api', 'health']
                                },
                                description: 'Check Core application health status'
                            },
                            response: []
                        },
                        {
                            name: 'Gateway Status',
                            request: {
                                method: 'GET',
                                header: [],
                                url: {
                                    raw: '{{gatewayUrl}}/api',
                                    host: ['{{gatewayUrl}}'],
                                    path: ['api']
                                },
                                description: 'Get Gateway application status'
                            },
                            response: []
                        }
                    ]
                },
                {
                    name: '🔐 Authentication',
                    item: [
                        {
                            name: 'Login',
                            event: [
                                {
                                    listen: 'test',
                                    script: {
                                        exec: [
                                            'if (pm.response.code === 200) {',
                                            '    const data = pm.response.json();',
                                            '    pm.globals.set("accessToken", data.accessToken);',
                                            '    pm.globals.set("refreshToken", data.refreshToken);',
                                            '    const expiry = new Date(Date.now() + (data.expiresIn * 1000));',
                                            '    pm.globals.set("tokenExpiry", expiry.toISOString());',
                                            '    pm.globals.set("currentUser", JSON.stringify(data.user));',
                                            '    console.log("Login successful, tokens saved");',
                                            '}'
                                        ]
                                    }
                                }
                            ],
                            request: {
                                method: 'POST',
                                header: [
                                    {
                                        key: 'Content-Type',
                                        value: 'application/json'
                                    }
                                ],
                                body: {
                                    mode: 'raw',
                                    raw: JSON.stringify({
                                        email: '<EMAIL>',
                                        password: 'password123'
                                    }, null, 2)
                                },
                                url: {
                                    raw: '{{gatewayUrl}}/api/auth/login',
                                    host: ['{{gatewayUrl}}'],
                                    path: ['api', 'auth', 'login']
                                },
                                description: 'Authenticate user and receive access tokens'
                            },
                            response: []
                        },
                        {
                            name: 'Register',
                            request: {
                                method: 'POST',
                                header: [
                                    {
                                        key: 'Content-Type',
                                        value: 'application/json'
                                    }
                                ],
                                body: {
                                    mode: 'raw',
                                    raw: JSON.stringify({
                                        email: '<EMAIL>',
                                        password: 'securepassword123',
                                        username: 'newuser',
                                        firstName: 'John',
                                        lastName: 'Doe'
                                    }, null, 2)
                                },
                                url: {
                                    raw: '{{gatewayUrl}}/api/auth/register',
                                    host: ['{{gatewayUrl}}'],
                                    path: ['api', 'auth', 'register']
                                },
                                description: 'Register a new user account'
                            },
                            response: []
                        },
                        {
                            name: 'Refresh Token',
                            event: [
                                {
                                    listen: 'test',
                                    script: {
                                        exec: [
                                            'if (pm.response.code === 200) {',
                                            '    const data = pm.response.json();',
                                            '    pm.globals.set("accessToken", data.accessToken);',
                                            '    pm.globals.set("refreshToken", data.refreshToken);',
                                            '    const expiry = new Date(Date.now() + (data.expiresIn * 1000));',
                                            '    pm.globals.set("tokenExpiry", expiry.toISOString());',
                                            '    console.log("Token refreshed successfully");',
                                            '}'
                                        ]
                                    }
                                }
                            ],
                            request: {
                                method: 'POST',
                                header: [
                                    {
                                        key: 'Content-Type',
                                        value: 'application/json'
                                    }
                                ],
                                body: {
                                    mode: 'raw',
                                    raw: JSON.stringify({
                                        refreshToken: '{{refreshToken}}'
                                    }, null, 2)
                                },
                                url: {
                                    raw: '{{gatewayUrl}}/api/auth/refresh',
                                    host: ['{{gatewayUrl}}'],
                                    path: ['api', 'auth', 'refresh']
                                },
                                description: 'Refresh access token using refresh token'
                            },
                            response: []
                        },
                        {
                            name: 'Verify Token',
                            request: {
                                auth: {
                                    type: 'bearer',
                                    bearer: [
                                        {
                                            key: 'token',
                                            value: '{{accessToken}}',
                                            type: 'string'
                                        }
                                    ]
                                },
                                method: 'GET',
                                header: [],
                                url: {
                                    raw: '{{gatewayUrl}}/api/auth/verify',
                                    host: ['{{gatewayUrl}}'],
                                    path: ['api', 'auth', 'verify']
                                },
                                description: 'Verify current access token and get user info'
                            },
                            response: []
                        },
                        {
                            name: 'Logout',
                            event: [
                                {
                                    listen: 'test',
                                    script: {
                                        exec: [
                                            'if (pm.response.code === 200) {',
                                            '    pm.globals.unset("accessToken");',
                                            '    pm.globals.unset("refreshToken");',
                                            '    pm.globals.unset("tokenExpiry");',
                                            '    pm.globals.unset("currentUser");',
                                            '    console.log("Logout successful, tokens cleared");',
                                            '}'
                                        ]
                                    }
                                }
                            ],
                            request: {
                                auth: {
                                    type: 'bearer',
                                    bearer: [
                                        {
                                            key: 'token',
                                            value: '{{accessToken}}',
                                            type: 'string'
                                        }
                                    ]
                                },
                                method: 'POST',
                                header: [],
                                url: {
                                    raw: '{{gatewayUrl}}/api/auth/logout',
                                    host: ['{{gatewayUrl}}'],
                                    path: ['api', 'auth', 'logout']
                                },
                                description: 'Logout user and invalidate session'
                            },
                            response: []
                        }
                    ]
                },
                {
                    name: '👥 Admin Management',
                    item: [
                        {
                            name: 'List Users',
                            request: {
                                auth: {
                                    type: 'bearer',
                                    bearer: [
                                        {
                                            key: 'token',
                                            value: '{{accessToken}}',
                                            type: 'string'
                                        }
                                    ]
                                },
                                method: 'GET',
                                header: [],
                                url: {
                                    raw: '{{gatewayUrl}}/api/core/admin/users?page=1&limit=20',
                                    host: ['{{gatewayUrl}}'],
                                    path: ['api', 'core', 'admin', 'users'],
                                    query: [
                                        {
                                            key: 'page',
                                            value: '1'
                                        },
                                        {
                                            key: 'limit',
                                            value: '20'
                                        },
                                        {
                                            key: 'search',
                                            value: '',
                                            disabled: true
                                        }
                                    ]
                                },
                                description: 'List all users (Admin only)'
                            },
                            response: []
                        },
                        {
                            name: 'Create User',
                            request: {
                                auth: {
                                    type: 'bearer',
                                    bearer: [
                                        {
                                            key: 'token',
                                            value: '{{accessToken}}',
                                            type: 'string'
                                        }
                                    ]
                                },
                                method: 'POST',
                                header: [
                                    {
                                        key: 'Content-Type',
                                        value: 'application/json'
                                    }
                                ],
                                body: {
                                    mode: 'raw',
                                    raw: JSON.stringify({
                                        email: '<EMAIL>',
                                        password: 'securepassword123',
                                        username: 'adminuser',
                                        firstName: 'Admin',
                                        lastName: 'User',
                                        roles: ['admin'],
                                        organizationId: 'org_id'
                                    }, null, 2)
                                },
                                url: {
                                    raw: '{{gatewayUrl}}/api/core/admin/users',
                                    host: ['{{gatewayUrl}}'],
                                    path: ['api', 'core', 'admin', 'users']
                                },
                                description: 'Create a new user (Admin only)'
                            },
                            response: []
                        },
                        {
                            name: 'Update User',
                            request: {
                                auth: {
                                    type: 'bearer',
                                    bearer: [
                                        {
                                            key: 'token',
                                            value: '{{accessToken}}',
                                            type: 'string'
                                        }
                                    ]
                                },
                                method: 'PUT',
                                header: [
                                    {
                                        key: 'Content-Type',
                                        value: 'application/json'
                                    }
                                ],
                                body: {
                                    mode: 'raw',
                                    raw: JSON.stringify({
                                        firstName: 'Updated',
                                        lastName: 'Name',
                                        roles: ['user', 'moderator'],
                                        isActive: true
                                    }, null, 2)
                                },
                                url: {
                                    raw: '{{gatewayUrl}}/api/core/admin/users/:userId',
                                    host: ['{{gatewayUrl}}'],
                                    path: ['api', 'core', 'admin', 'users', ':userId'],
                                    variable: [
                                        {
                                            key: 'userId',
                                            value: 'user_id_here'
                                        }
                                    ]
                                },
                                description: 'Update user information (Admin only)'
                            },
                            response: []
                        },
                        {
                            name: 'Delete User',
                            request: {
                                auth: {
                                    type: 'bearer',
                                    bearer: [
                                        {
                                            key: 'token',
                                            value: '{{accessToken}}',
                                            type: 'string'
                                        }
                                    ]
                                },
                                method: 'DELETE',
                                header: [],
                                url: {
                                    raw: '{{gatewayUrl}}/api/core/admin/users/:userId',
                                    host: ['{{gatewayUrl}}'],
                                    path: ['api', 'core', 'admin', 'users', ':userId'],
                                    variable: [
                                        {
                                            key: 'userId',
                                            value: 'user_id_here'
                                        }
                                    ]
                                },
                                description: 'Delete a user (Admin only)'
                            },
                            response: []
                        },
                        {
                            name: 'List Organizations',
                            request: {
                                auth: {
                                    type: 'bearer',
                                    bearer: [
                                        {
                                            key: 'token',
                                            value: '{{accessToken}}',
                                            type: 'string'
                                        }
                                    ]
                                },
                                method: 'GET',
                                header: [],
                                url: {
                                    raw: '{{gatewayUrl}}/api/core/admin/tenants',
                                    host: ['{{gatewayUrl}}'],
                                    path: ['api', 'core', 'admin', 'tenants']
                                },
                                description: 'List all organizations/tenants (Admin only)'
                            },
                            response: []
                        },
                        {
                            name: 'Create Organization',
                            request: {
                                auth: {
                                    type: 'bearer',
                                    bearer: [
                                        {
                                            key: 'token',
                                            value: '{{accessToken}}',
                                            type: 'string'
                                        }
                                    ]
                                },
                                method: 'POST',
                                header: [
                                    {
                                        key: 'Content-Type',
                                        value: 'application/json'
                                    }
                                ],
                                body: {
                                    mode: 'raw',
                                    raw: JSON.stringify({
                                        name: 'New Organization',
                                        domain: 'neworg.com',
                                        settings: {
                                            allowedDomains: ['neworg.com'],
                                            maxUsers: 50,
                                            features: ['cms']
                                        }
                                    }, null, 2)
                                },
                                url: {
                                    raw: '{{gatewayUrl}}/api/core/admin/tenants',
                                    host: ['{{gatewayUrl}}'],
                                    path: ['api', 'core', 'admin', 'tenants']
                                },
                                description: 'Create a new organization (Admin only)'
                            },
                            response: []
                        },
                        {
                            name: 'List Permissions',
                            request: {
                                auth: {
                                    type: 'bearer',
                                    bearer: [
                                        {
                                            key: 'token',
                                            value: '{{accessToken}}',
                                            type: 'string'
                                        }
                                    ]
                                },
                                method: 'GET',
                                header: [],
                                url: {
                                    raw: '{{gatewayUrl}}/api/core/admin/permissions',
                                    host: ['{{gatewayUrl}}'],
                                    path: ['api', 'core', 'admin', 'permissions']
                                },
                                description: 'List all available permissions (Admin only)'
                            },
                            response: []
                        },
                        {
                            name: 'View Audit Logs',
                            request: {
                                auth: {
                                    type: 'bearer',
                                    bearer: [
                                        {
                                            key: 'token',
                                            value: '{{accessToken}}',
                                            type: 'string'
                                        }
                                    ]
                                },
                                method: 'GET',
                                header: [],
                                url: {
                                    raw: '{{gatewayUrl}}/api/core/admin/audit-logs?page=1&limit=20',
                                    host: ['{{gatewayUrl}}'],
                                    path: ['api', 'core', 'admin', 'audit-logs'],
                                    query: [
                                        {
                                            key: 'page',
                                            value: '1'
                                        },
                                        {
                                            key: 'limit',
                                            value: '20'
                                        },
                                        {
                                            key: 'userId',
                                            value: '',
                                            disabled: true
                                        },
                                        {
                                            key: 'action',
                                            value: '',
                                            disabled: true
                                        },
                                        {
                                            key: 'resource',
                                            value: '',
                                            disabled: true
                                        }
                                    ]
                                },
                                description: 'View system audit logs (Admin only)'
                            },
                            response: []
                        }
                    ]
                },
                {
                    name: '📝 Content Management (CMS)',
                    item: [
                        {
                            name: 'List Content',
                            request: {
                                auth: {
                                    type: 'bearer',
                                    bearer: [
                                        {
                                            key: 'token',
                                            value: '{{accessToken}}',
                                            type: 'string'
                                        }
                                    ]
                                },
                                method: 'GET',
                                header: [],
                                url: {
                                    raw: '{{gatewayUrl}}/api/core/coder/content?page=1&limit=20',
                                    host: ['{{gatewayUrl}}'],
                                    path: ['api', 'core', 'coder', 'content'],
                                    query: [
                                        {
                                            key: 'page',
                                            value: '1'
                                        },
                                        {
                                            key: 'limit',
                                            value: '20'
                                        },
                                        {
                                            key: 'status',
                                            value: '',
                                            disabled: true
                                        },
                                        {
                                            key: 'type',
                                            value: '',
                                            disabled: true
                                        },
                                        {
                                            key: 'search',
                                            value: '',
                                            disabled: true
                                        }
                                    ]
                                },
                                description: 'List all content items'
                            },
                            response: []
                        },
                        {
                            name: 'Create Content',
                            request: {
                                auth: {
                                    type: 'bearer',
                                    bearer: [
                                        {
                                            key: 'token',
                                            value: '{{accessToken}}',
                                            type: 'string'
                                        }
                                    ]
                                },
                                method: 'POST',
                                header: [
                                    {
                                        key: 'Content-Type',
                                        value: 'application/json'
                                    }
                                ],
                                body: {
                                    mode: 'raw',
                                    raw: JSON.stringify({
                                        title: 'Sample Article',
                                        body: 'This is the article content...',
                                        excerpt: 'Brief description',
                                        type: 'article',
                                        status: 'draft',
                                        tags: ['technology', 'tutorial'],
                                        metadata: {
                                            seoTitle: 'Sample Article - Tech Tutorial',
                                            seoDescription: 'Learn about technology'
                                        }
                                    }, null, 2)
                                },
                                url: {
                                    raw: '{{gatewayUrl}}/api/core/coder/content',
                                    host: ['{{gatewayUrl}}'],
                                    path: ['api', 'core', 'coder', 'content']
                                },
                                description: 'Create new content item'
                            },
                            response: []
                        },
                        {
                            name: 'Get Content by ID',
                            request: {
                                auth: {
                                    type: 'bearer',
                                    bearer: [
                                        {
                                            key: 'token',
                                            value: '{{accessToken}}',
                                            type: 'string'
                                        }
                                    ]
                                },
                                method: 'GET',
                                header: [],
                                url: {
                                    raw: '{{gatewayUrl}}/api/core/coder/content/:contentId',
                                    host: ['{{gatewayUrl}}'],
                                    path: ['api', 'core', 'coder', 'content', ':contentId'],
                                    variable: [
                                        {
                                            key: 'contentId',
                                            value: 'content_id_here'
                                        }
                                    ]
                                },
                                description: 'Get content item by ID'
                            },
                            response: []
                        },
                        {
                            name: 'Update Content',
                            request: {
                                auth: {
                                    type: 'bearer',
                                    bearer: [
                                        {
                                            key: 'token',
                                            value: '{{accessToken}}',
                                            type: 'string'
                                        }
                                    ]
                                },
                                method: 'PUT',
                                header: [
                                    {
                                        key: 'Content-Type',
                                        value: 'application/json'
                                    }
                                ],
                                body: {
                                    mode: 'raw',
                                    raw: JSON.stringify({
                                        title: 'Updated Article Title',
                                        body: 'Updated content body...',
                                        status: 'published',
                                        publishedAt: new Date().toISOString()
                                    }, null, 2)
                                },
                                url: {
                                    raw: '{{gatewayUrl}}/api/core/coder/content/:contentId',
                                    host: ['{{gatewayUrl}}'],
                                    path: ['api', 'core', 'coder', 'content', ':contentId'],
                                    variable: [
                                        {
                                            key: 'contentId',
                                            value: 'content_id_here'
                                        }
                                    ]
                                },
                                description: 'Update existing content item'
                            },
                            response: []
                        },
                        {
                            name: 'Delete Content',
                            request: {
                                auth: {
                                    type: 'bearer',
                                    bearer: [
                                        {
                                            key: 'token',
                                            value: '{{accessToken}}',
                                            type: 'string'
                                        }
                                    ]
                                },
                                method: 'DELETE',
                                header: [],
                                url: {
                                    raw: '{{gatewayUrl}}/api/core/coder/content/:contentId',
                                    host: ['{{gatewayUrl}}'],
                                    path: ['api', 'core', 'coder', 'content', ':contentId'],
                                    variable: [
                                        {
                                            key: 'contentId',
                                            value: 'content_id_here'
                                        }
                                    ]
                                },
                                description: 'Delete content item'
                            },
                            response: []
                        },
                        {
                            name: 'List Forms',
                            request: {
                                auth: {
                                    type: 'bearer',
                                    bearer: [
                                        {
                                            key: 'token',
                                            value: '{{accessToken}}',
                                            type: 'string'
                                        }
                                    ]
                                },
                                method: 'GET',
                                header: [],
                                url: {
                                    raw: '{{gatewayUrl}}/api/core/coder/forms',
                                    host: ['{{gatewayUrl}}'],
                                    path: ['api', 'core', 'coder', 'forms']
                                },
                                description: 'List all forms'
                            },
                            response: []
                        },
                        {
                            name: 'Create Form',
                            request: {
                                auth: {
                                    type: 'bearer',
                                    bearer: [
                                        {
                                            key: 'token',
                                            value: '{{accessToken}}',
                                            type: 'string'
                                        }
                                    ]
                                },
                                method: 'POST',
                                header: [
                                    {
                                        key: 'Content-Type',
                                        value: 'application/json'
                                    }
                                ],
                                body: {
                                    mode: 'raw',
                                    raw: JSON.stringify({
                                        name: 'contact-form',
                                        title: 'Contact Us',
                                        description: 'Get in touch with us',
                                        fields: [
                                            {
                                                name: 'name',
                                                label: 'Full Name',
                                                type: 'text',
                                                required: true,
                                                order: 1
                                            },
                                            {
                                                name: 'email',
                                                label: 'Email Address',
                                                type: 'email',
                                                required: true,
                                                order: 2
                                            },
                                            {
                                                name: 'message',
                                                label: 'Message',
                                                type: 'textarea',
                                                required: true,
                                                order: 3
                                            }
                                        ],
                                        settings: {
                                            submitText: 'Send Message',
                                            redirectUrl: '/thank-you',
                                            emailNotifications: ['<EMAIL>']
                                        }
                                    }, null, 2)
                                },
                                url: {
                                    raw: '{{gatewayUrl}}/api/core/coder/forms',
                                    host: ['{{gatewayUrl}}'],
                                    path: ['api', 'core', 'coder', 'forms']
                                },
                                description: 'Create a new form'
                            },
                            response: []
                        },
                        {
                            name: 'List Themes',
                            request: {
                                auth: {
                                    type: 'bearer',
                                    bearer: [
                                        {
                                            key: 'token',
                                            value: '{{accessToken}}',
                                            type: 'string'
                                        }
                                    ]
                                },
                                method: 'GET',
                                header: [],
                                url: {
                                    raw: '{{gatewayUrl}}/api/core/coder/themes',
                                    host: ['{{gatewayUrl}}'],
                                    path: ['api', 'core', 'coder', 'themes']
                                },
                                description: 'List all themes'
                            },
                            response: []
                        },
                        {
                            name: 'List Newsletters',
                            request: {
                                auth: {
                                    type: 'bearer',
                                    bearer: [
                                        {
                                            key: 'token',
                                            value: '{{accessToken}}',
                                            type: 'string'
                                        }
                                    ]
                                },
                                method: 'GET',
                                header: [],
                                url: {
                                    raw: '{{gatewayUrl}}/api/core/coder/newsletter',
                                    host: ['{{gatewayUrl}}'],
                                    path: ['api', 'core', 'coder', 'newsletter']
                                },
                                description: 'List all newsletters'
                            },
                            response: []
                        }
                    ]
                },
                {
                    name: '📚 AI Documentation',
                    item: [
                        {
                            name: 'AI Agent Complete Info',
                            request: {
                                method: 'GET',
                                header: [],
                                url: {
                                    raw: '{{gatewayUrl}}/api/core/docs/ai-agent-complete',
                                    host: ['{{gatewayUrl}}'],
                                    path: ['api', 'core', 'docs', 'ai-agent-complete']
                                },
                                description: 'Get complete AI agent integration information in single endpoint'
                            },
                            response: []
                        },
                        {
                            name: 'AI Onboarding Overview',
                            request: {
                                method: 'GET',
                                header: [],
                                url: {
                                    raw: '{{gatewayUrl}}/api/core/docs/ai-onboarding',
                                    host: ['{{gatewayUrl}}'],
                                    path: ['api', 'core', 'docs', 'ai-onboarding']
                                },
                                description: 'Get AI onboarding documentation overview'
                            },
                            response: []
                        },
                        {
                            name: 'System Architecture',
                            request: {
                                method: 'GET',
                                header: [],
                                url: {
                                    raw: '{{gatewayUrl}}/api/core/docs/ai-onboarding/architecture',
                                    host: ['{{gatewayUrl}}'],
                                    path: ['api', 'core', 'docs', 'ai-onboarding', 'architecture']
                                },
                                description: 'Get system architecture documentation'
                            },
                            response: []
                        },
                        {
                            name: 'Authentication Documentation',
                            request: {
                                method: 'GET',
                                header: [],
                                url: {
                                    raw: '{{gatewayUrl}}/api/core/docs/ai-onboarding/authentication',
                                    host: ['{{gatewayUrl}}'],
                                    path: ['api', 'core', 'docs', 'ai-onboarding', 'authentication']
                                },
                                description: 'Get authentication flow documentation'
                            },
                            response: []
                        },
                        {
                            name: 'API Reference',
                            request: {
                                method: 'GET',
                                header: [],
                                url: {
                                    raw: '{{gatewayUrl}}/api/core/docs/ai-onboarding/api-reference?module=',
                                    host: ['{{gatewayUrl}}'],
                                    path: ['api', 'core', 'docs', 'ai-onboarding', 'api-reference'],
                                    query: [
                                        {
                                            key: 'module',
                                            value: '',
                                            description: 'Filter by module (auth, admin, coder)'
                                        }
                                    ]
                                },
                                description: 'Get complete API reference'
                            },
                            response: []
                        },
                        {
                            name: 'Integration Patterns',
                            request: {
                                method: 'GET',
                                header: [],
                                url: {
                                    raw: '{{gatewayUrl}}/api/core/docs/ai-onboarding/integration-patterns?framework=nextjs',
                                    host: ['{{gatewayUrl}}'],
                                    path: ['api', 'core', 'docs', 'ai-onboarding', 'integration-patterns'],
                                    query: [
                                        {
                                            key: 'framework',
                                            value: 'nextjs',
                                            description: 'Framework (nextjs, react, vue, angular)'
                                        }
                                    ]
                                },
                                description: 'Get integration patterns for specific framework'
                            },
                            response: []
                        },
                        {
                            name: 'TypeScript Definitions',
                            request: {
                                method: 'GET',
                                header: [],
                                url: {
                                    raw: '{{gatewayUrl}}/api/core/docs/ai-onboarding/typescript-definitions',
                                    host: ['{{gatewayUrl}}'],
                                    path: ['api', 'core', 'docs', 'ai-onboarding', 'typescript-definitions']
                                },
                                description: 'Get TypeScript type definitions'
                            },
                            response: []
                        },
                        {
                            name: 'Code Examples',
                            request: {
                                method: 'GET',
                                header: [],
                                url: {
                                    raw: '{{gatewayUrl}}/api/core/docs/ai-onboarding/examples?scenario=authentication',
                                    host: ['{{gatewayUrl}}'],
                                    path: ['api', 'core', 'docs', 'ai-onboarding', 'examples'],
                                    query: [
                                        {
                                            key: 'scenario',
                                            value: 'authentication',
                                            description: 'Scenario (authentication, crud, forms)'
                                        }
                                    ]
                                },
                                description: 'Get code examples for common scenarios'
                            },
                            response: []
                        },
                        {
                            name: 'System Requirements',
                            request: {
                                method: 'GET',
                                header: [],
                                url: {
                                    raw: '{{gatewayUrl}}/api/core/docs/ai-onboarding/requirements',
                                    host: ['{{gatewayUrl}}'],
                                    path: ['api', 'core', 'docs', 'ai-onboarding', 'requirements']
                                },
                                description: 'Get system requirements and constraints'
                            },
                            response: []
                        },
                        {
                            name: 'Testing Guidelines',
                            request: {
                                method: 'GET',
                                header: [],
                                url: {
                                    raw: '{{gatewayUrl}}/api/core/docs/ai-onboarding/testing',
                                    host: ['{{gatewayUrl}}'],
                                    path: ['api', 'core', 'docs', 'ai-onboarding', 'testing']
                                },
                                description: 'Get testing guidelines and procedures'
                            },
                            response: []
                        },
                        {
                            name: 'Development Workflow',
                            request: {
                                method: 'GET',
                                header: [],
                                url: {
                                    raw: '{{gatewayUrl}}/api/core/docs/ai-onboarding/workflow',
                                    host: ['{{gatewayUrl}}'],
                                    path: ['api', 'core', 'docs', 'ai-onboarding', 'workflow']
                                },
                                description: 'Get development workflow and best practices'
                            },
                            response: []
                        },
                        {
                            name: 'Data Schemas',
                            request: {
                                method: 'GET',
                                header: [],
                                url: {
                                    raw: '{{gatewayUrl}}/api/core/docs/ai-onboarding/schemas?format=json',
                                    host: ['{{gatewayUrl}}'],
                                    path: ['api', 'core', 'docs', 'ai-onboarding', 'schemas'],
                                    query: [
                                        {
                                            key: 'format',
                                            value: 'json',
                                            description: 'Response format (json, typescript, openapi)'
                                        }
                                    ]
                                },
                                description: 'Get all data schemas and models'
                            },
                            response: []
                        },
                        {
                            name: 'OpenAPI Specification',
                            request: {
                                method: 'GET',
                                header: [],
                                url: {
                                    raw: '{{gatewayUrl}}/api/core/docs/openapi',
                                    host: ['{{gatewayUrl}}'],
                                    path: ['api', 'core', 'docs', 'openapi']
                                },
                                description: 'Get OpenAPI specification for the entire system'
                            },
                            response: []
                        },
                        {
                            name: 'Documentation Health',
                            request: {
                                method: 'GET',
                                header: [],
                                url: {
                                    raw: '{{gatewayUrl}}/api/core/docs/health',
                                    host: ['{{gatewayUrl}}'],
                                    path: ['api', 'core', 'docs', 'health']
                                },
                                description: 'Check documentation service health and available endpoints'
                            },
                            response: []
                        },
                        {
                            name: 'Download Postman Collection',
                            request: {
                                method: 'GET',
                                header: [],
                                url: {
                                    raw: '{{gatewayUrl}}/api/core/docs/postman-collection',
                                    host: ['{{gatewayUrl}}'],
                                    path: ['api', 'core', 'docs', 'postman-collection']
                                },
                                description: 'Download latest Postman collection file'
                            },
                            response: []
                        }
                    ]
                }
            ]
        };
    }
    generatePostmanDownloadPage() {
        return `
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Legalyard Backend API - Postman Collection</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }

        .container {
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            padding: 40px;
            max-width: 600px;
            width: 100%;
            text-align: center;
        }

        .logo {
            font-size: 2.5rem;
            font-weight: bold;
            color: #667eea;
            margin-bottom: 10px;
        }

        .subtitle {
            color: #666;
            font-size: 1.1rem;
            margin-bottom: 30px;
        }

        .description {
            color: #555;
            line-height: 1.6;
            margin-bottom: 30px;
        }

        .features {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
            text-align: left;
        }

        .features h3 {
            color: #333;
            margin-bottom: 15px;
            font-size: 1.2rem;
        }

        .features ul {
            list-style: none;
            padding: 0;
        }

        .features li {
            padding: 5px 0;
            color: #666;
            position: relative;
            padding-left: 20px;
        }

        .features li:before {
            content: "✓";
            color: #28a745;
            font-weight: bold;
            position: absolute;
            left: 0;
        }

        .download-btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 15px 30px;
            font-size: 1.1rem;
            border-radius: 50px;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
            margin: 10px;
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
        }

        .download-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(102, 126, 234, 0.6);
        }

        .secondary-btn {
            background: #6c757d;
            box-shadow: 0 4px 15px rgba(108, 117, 125, 0.4);
        }

        .secondary-btn:hover {
            box-shadow: 0 6px 20px rgba(108, 117, 125, 0.6);
        }

        .info-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin: 30px 0;
        }

        .info-card {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            text-align: left;
        }

        .info-card h4 {
            color: #667eea;
            margin-bottom: 10px;
            font-size: 1.1rem;
        }

        .info-card p {
            color: #666;
            font-size: 0.9rem;
            line-height: 1.5;
        }

        .endpoints-count {
            background: #e3f2fd;
            color: #1976d2;
            padding: 5px 10px;
            border-radius: 20px;
            font-size: 0.9rem;
            font-weight: bold;
            display: inline-block;
            margin: 10px 0;
        }

        .footer {
            margin-top: 30px;
            padding-top: 20px;
            border-top: 1px solid #eee;
            color: #999;
            font-size: 0.9rem;
        }

        .footer a {
            color: #667eea;
            text-decoration: none;
        }

        .footer a:hover {
            text-decoration: underline;
        }

        @media (max-width: 768px) {
            .container {
                padding: 20px;
            }

            .logo {
                font-size: 2rem;
            }

            .info-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="logo">🚀 Legalyard Backend API</div>
        <div class="subtitle">Postman Collection Download</div>

        <div class="description">
            Download the complete Postman collection for the Legalyard Backend API. This collection includes all endpoints with pre-configured authentication, examples, and automatic token management.
        </div>

        <div class="endpoints-count">50+ API Endpoints Ready to Use</div>

        <div class="features">
            <h3>📦 What's Included</h3>
            <ul>
                <li>Complete API collection with all Gateway and Core endpoints</li>
                <li>Pre-configured authentication with automatic token refresh</li>
                <li>Environment variables for easy switching between environments</li>
                <li>Request examples with sample data</li>
                <li>Organized folders: Health, Authentication, Admin, CMS, Documentation</li>
                <li>Test scripts for automatic token management</li>
                <li>Ready-to-use request templates</li>
            </ul>
        </div>

        <div class="info-grid">
            <div class="info-card">
                <h4>🔐 Authentication</h4>
                <p>Includes login, register, token refresh, and logout endpoints with automatic token management scripts.</p>
            </div>
            <div class="info-card">
                <h4>👥 Admin Management</h4>
                <p>User management, organization setup, permissions, and audit log endpoints for administrators.</p>
            </div>
            <div class="info-card">
                <h4>📝 Content Management</h4>
                <p>Complete CMS endpoints for content, forms, themes, and newsletter management.</p>
            </div>
            <div class="info-card">
                <h4>📚 AI Documentation</h4>
                <p>All AI onboarding endpoints including the master endpoint with complete system information.</p>
            </div>
        </div>

        <div style="margin: 30px 0;">
            <a href="/api/docs/postman-collection" class="download-btn" download="Legalyard-Backend-API.postman_collection.json">
                📥 Download Postman Collection
            </a>
            <a href="/api/docs" class="download-btn secondary-btn">
                📖 View Swagger Documentation
            </a>
        </div>

        <div class="features">
            <h3>🚀 Quick Setup Instructions</h3>
            <ul>
                <li>Download the collection file using the button above</li>
                <li>Open Postman and click "Import" in the top left</li>
                <li>Select the downloaded JSON file</li>
                <li>The collection will be imported with all endpoints and configurations</li>
                <li>Set your environment variables (gatewayUrl, coreUrl) if needed</li>
                <li>Start with the "Login" request to authenticate</li>
                <li>All subsequent requests will use the stored authentication token</li>
            </ul>
        </div>

        <div class="footer">
            <p>
                Generated on ${new Date().toLocaleString()} |
                <a href="/api/core/docs/ai-agent-complete">AI Agent Documentation</a> |
                <a href="/api/health">System Health</a>
            </p>
            <p style="margin-top: 10px;">
                <strong>Base URLs:</strong> Gateway (8000) | Core (8001)<br>
                <strong>Version:</strong> 1.0.0 | <strong>Format:</strong> Postman Collection v2.1.0
            </p>
        </div>
    </div>

    <script>
        // Add click tracking
        document.querySelector('.download-btn').addEventListener('click', function() {
            console.log('Postman collection download initiated');
        });

        // Auto-refresh page every 5 minutes to get latest collection
        setTimeout(function() {
            location.reload();
        }, 300000);
    </script>
</body>
</html>
    `;
    }
};
exports.DocsService = DocsService;
exports.DocsService = DocsService = __decorate([
    (0, common_1.Injectable)()
], DocsService);


/***/ }),

/***/ "./apps/core/src/modules/shared/shared.module.ts":
/*!*******************************************************!*\
  !*** ./apps/core/src/modules/shared/shared.module.ts ***!
  \*******************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {


var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", ({ value: true }));
exports.SharedModule = void 0;
const common_1 = __webpack_require__(/*! @nestjs/common */ "@nestjs/common");
const utils_service_1 = __webpack_require__(/*! ./utils.service */ "./apps/core/src/modules/shared/utils.service.ts");
let SharedModule = class SharedModule {
};
exports.SharedModule = SharedModule;
exports.SharedModule = SharedModule = __decorate([
    (0, common_1.Global)(),
    (0, common_1.Module)({
        providers: [utils_service_1.UtilsService],
        exports: [utils_service_1.UtilsService],
    })
], SharedModule);


/***/ }),

/***/ "./apps/core/src/modules/shared/utils.service.ts":
/*!*******************************************************!*\
  !*** ./apps/core/src/modules/shared/utils.service.ts ***!
  \*******************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {


var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", ({ value: true }));
exports.UtilsService = void 0;
const common_1 = __webpack_require__(/*! @nestjs/common */ "@nestjs/common");
let UtilsService = class UtilsService {
    generateId() {
        return Math.random().toString(36).substring(2, 15) + Math.random().toString(36).substring(2, 15);
    }
    formatDate(date) {
        return date.toISOString();
    }
    validateEmail(email) {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return emailRegex.test(email);
    }
    hashPassword(password) {
        return `hashed_${password}`;
    }
    generateToken() {
        return `token_${this.generateId()}`;
    }
};
exports.UtilsService = UtilsService;
exports.UtilsService = UtilsService = __decorate([
    (0, common_1.Injectable)()
], UtilsService);


/***/ }),

/***/ "@nestjs/common":
/*!*********************************!*\
  !*** external "@nestjs/common" ***!
  \*********************************/
/***/ ((module) => {

module.exports = require("@nestjs/common");

/***/ }),

/***/ "@nestjs/config":
/*!*********************************!*\
  !*** external "@nestjs/config" ***!
  \*********************************/
/***/ ((module) => {

module.exports = require("@nestjs/config");

/***/ }),

/***/ "@nestjs/core":
/*!*******************************!*\
  !*** external "@nestjs/core" ***!
  \*******************************/
/***/ ((module) => {

module.exports = require("@nestjs/core");

/***/ }),

/***/ "@nestjs/mongoose":
/*!***********************************!*\
  !*** external "@nestjs/mongoose" ***!
  \***********************************/
/***/ ((module) => {

module.exports = require("@nestjs/mongoose");

/***/ }),

/***/ "@nestjs/swagger":
/*!**********************************!*\
  !*** external "@nestjs/swagger" ***!
  \**********************************/
/***/ ((module) => {

module.exports = require("@nestjs/swagger");

/***/ }),

/***/ "express":
/*!**************************!*\
  !*** external "express" ***!
  \**************************/
/***/ ((module) => {

module.exports = require("express");

/***/ }),

/***/ "helmet":
/*!*************************!*\
  !*** external "helmet" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("helmet");

/***/ })

/******/ 	});
/************************************************************************/
/******/ 	// The module cache
/******/ 	var __webpack_module_cache__ = {};
/******/ 	
/******/ 	// The require function
/******/ 	function __webpack_require__(moduleId) {
/******/ 		// Check if module is in cache
/******/ 		var cachedModule = __webpack_module_cache__[moduleId];
/******/ 		if (cachedModule !== undefined) {
/******/ 			return cachedModule.exports;
/******/ 		}
/******/ 		// Create a new module (and put it into the cache)
/******/ 		var module = __webpack_module_cache__[moduleId] = {
/******/ 			// no module.id needed
/******/ 			// no module.loaded needed
/******/ 			exports: {}
/******/ 		};
/******/ 	
/******/ 		// Execute the module function
/******/ 		__webpack_modules__[moduleId].call(module.exports, module, module.exports, __webpack_require__);
/******/ 	
/******/ 		// Return the exports of the module
/******/ 		return module.exports;
/******/ 	}
/******/ 	
/************************************************************************/
var __webpack_exports__ = {};
// This entry needs to be wrapped in an IIFE because it needs to be isolated against other modules in the chunk.
(() => {
var exports = __webpack_exports__;
/*!*******************************!*\
  !*** ./apps/core/src/main.ts ***!
  \*******************************/

Object.defineProperty(exports, "__esModule", ({ value: true }));
const core_1 = __webpack_require__(/*! @nestjs/core */ "@nestjs/core");
const app_module_1 = __webpack_require__(/*! ./app.module */ "./apps/core/src/app.module.ts");
const common_1 = __webpack_require__(/*! @nestjs/common */ "@nestjs/common");
const swagger_1 = __webpack_require__(/*! @nestjs/swagger */ "@nestjs/swagger");
const helmet_1 = __webpack_require__(/*! helmet */ "helmet");
async function bootstrap() {
    const app = await core_1.NestFactory.create(app_module_1.AppModule);
    app.use((0, helmet_1.default)());
    app.useGlobalPipes(new common_1.ValidationPipe({
        whitelist: true,
        forbidNonWhitelisted: true,
        transform: true,
    }));
    app.setGlobalPrefix('api');
    if (process.env.NODE_ENV !== 'production') {
        const config = new swagger_1.DocumentBuilder()
            .setTitle('Legalyard Core API')
            .setDescription('Core business logic API for Legalyard application')
            .setVersion('1.0')
            .addBearerAuth()
            .addTag('auth', 'Authentication endpoints')
            .addTag('admin', 'Admin management endpoints')
            .addTag('coder', 'CMS and content management endpoints')
            .build();
        const document = swagger_1.SwaggerModule.createDocument(app, config);
        swagger_1.SwaggerModule.setup('api/docs', app, document);
    }
    const port = process.env.CORE_PORT || 8001;
    await app.listen(port, '0.0.0.0');
    console.log(`🚀 Core app is running on: http://localhost:${port}`);
    if (process.env.NODE_ENV !== 'production') {
        console.log(`📚 Swagger docs available at: http://localhost:${port}/api/docs`);
    }
}
bootstrap();

})();

/******/ })()
;