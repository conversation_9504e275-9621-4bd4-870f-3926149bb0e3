# Test Environment Configuration
NODE_ENV=test

# Database Configuration
DATABASE_ENGINE=mongo
MONGO_URI=mongodb://localhost:27017/legalyard_test
POSTGRES_URL=postgres://user:password@localhost:5432/legalyard_test

# Service Ports
GATEWAY_PORT=8010
CORE_PORT=8011

# Service URLs
CORE_SERVICE_URL=http://localhost:8011

# Authentication Secrets (Test keys - not for production)
ACCESS_TOKEN_SECRET=test-access-token-secret
REFRESH_TOKEN_SECRET=test-refresh-token-secret
TOTP_SECRET=test-totp-secret

# Security Configuration
ALLOWED_IPS=127.0.0.1,::1

# Rate Limiting (More lenient for testing)
THROTTLE_TTL=60000
THROTTLE_LIMIT=1000

# CORS Configuration
CORS_ORIGIN=http://localhost:3010,http://localhost:3011

# Logging
LOG_LEVEL=error
