// MongoDB initialization script
db = db.getSiblingDB('legalyard');

// Create collections
db.createCollection('users');
db.createCollection('organizations');
db.createCollection('sessions');
db.createCollection('audit_logs');
db.createCollection('content');
db.createCollection('forms');
db.createCollection('themes');
db.createCollection('newsletters');

// Create indexes
db.users.createIndex({ email: 1 }, { unique: true });
db.users.createIndex({ username: 1 }, { unique: true, sparse: true });
db.users.createIndex({ organizationId: 1 });

db.organizations.createIndex({ domain: 1 }, { unique: true, sparse: true });

db.sessions.createIndex({ userId: 1 });
db.sessions.createIndex({ expiresAt: 1 }, { expireAfterSeconds: 0 });

db.audit_logs.createIndex({ userId: 1 });
db.audit_logs.createIndex({ timestamp: -1 });
db.audit_logs.createIndex({ resource: 1, action: 1 });

db.content.createIndex({ slug: 1 }, { unique: true });
db.content.createIndex({ status: 1 });
db.content.createIndex({ type: 1 });
db.content.createIndex({ authorId: 1 });
db.content.createIndex({ tags: 1 });

print('MongoDB initialization completed successfully!');
