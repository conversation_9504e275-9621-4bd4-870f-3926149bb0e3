# TypeScript Definitions for Legalyard Backend

## Core Type Definitions

### API Response Types
```typescript
interface ApiResponse<T = any> {
  data?: T;
  error?: string;
  message?: string;
  statusCode?: number;
}

interface PaginatedResponse<T> {
  data: T[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    pages: number;
  };
}

interface ErrorResponse {
  statusCode: number;
  message: string;
  error: string;
  details?: Array<{
    field: string;
    message: string;
  }>;
}
```

### Authentication Types
```typescript
interface LoginCredentials {
  email: string;
  password: string;
  totpCode?: string;
}

interface RegisterData {
  email: string;
  password: string;
  username?: string;
  firstName?: string;
  lastName?: string;
  organizationId?: string;
}

interface AuthResponse {
  accessToken: string;
  refreshToken: string;
  user: User;
  expiresIn: number;
}

interface RefreshTokenRequest {
  refreshToken: string;
}

interface TokenPayload {
  sub: string; // user id
  email: string;
  roles: string[];
  organizationId?: string;
  sessionId: string;
  iat: number;
  exp: number;
}
```

### User Types
```typescript
interface User {
  id: string;
  email: string;
  username?: string;
  firstName?: string;
  lastName?: string;
  roles: string[];
  organizationId?: string;
  isActive: boolean;
  isEmailVerified: boolean;
  lastLoginAt?: string;
  createdAt: string;
  updatedAt: string;
}

interface CreateUserData {
  email: string;
  password: string;
  username?: string;
  firstName?: string;
  lastName?: string;
  roles?: string[];
  organizationId?: string;
}

interface UpdateUserData {
  username?: string;
  firstName?: string;
  lastName?: string;
  roles?: string[];
  isActive?: boolean;
}

interface UserSession {
  id: string;
  userId: string;
  deviceId: string;
  deviceInfo: {
    userAgent: string;
    ip: string;
    os?: string;
    browser?: string;
    device?: string;
  };
  isActive: boolean;
  expiresAt: string;
  createdAt: string;
}
```

### Organization Types
```typescript
interface Organization {
  id: string;
  name: string;
  domain?: string;
  settings: {
    allowedDomains?: string[];
    maxUsers?: number;
    features: string[];
  };
  isActive: boolean;
  subscriptionPlan?: string;
  subscriptionExpires?: string;
  metadata?: Record<string, any>;
  createdAt: string;
  updatedAt: string;
}

interface CreateOrganizationData {
  name: string;
  domain?: string;
  settings?: {
    allowedDomains?: string[];
    maxUsers?: number;
    features?: string[];
  };
}
```

### Content Types
```typescript
type ContentStatus = 'draft' | 'published' | 'archived';
type ContentType = 'page' | 'post' | 'article';

interface Content {
  id: string;
  title: string;
  slug: string;
  body: string;
  excerpt?: string;
  status: ContentStatus;
  type: ContentType;
  authorId: string;
  tags: string[];
  metadata: Record<string, any>;
  publishedAt?: string;
  createdAt: string;
  updatedAt: string;
}

interface CreateContentData {
  title: string;
  body: string;
  excerpt?: string;
  type: ContentType;
  status?: ContentStatus;
  tags?: string[];
  metadata?: Record<string, any>;
  publishedAt?: string;
}

interface UpdateContentData {
  title?: string;
  body?: string;
  excerpt?: string;
  status?: ContentStatus;
  tags?: string[];
  metadata?: Record<string, any>;
  publishedAt?: string;
}

interface ContentFilter {
  page?: number;
  limit?: number;
  status?: ContentStatus;
  type?: ContentType;
  search?: string;
  authorId?: string;
  tags?: string[];
}
```

### Form Types
```typescript
type FormFieldType = 'text' | 'email' | 'number' | 'textarea' | 'select' | 'checkbox' | 'radio' | 'file';

interface FormField {
  id: string;
  name: string;
  label: string;
  type: FormFieldType;
  required: boolean;
  placeholder?: string;
  options?: string[];
  validation?: Record<string, any>;
  order: number;
}

interface Form {
  id: string;
  name: string;
  title: string;
  description?: string;
  fields: FormField[];
  settings: {
    submitText?: string;
    redirectUrl?: string;
    emailNotifications?: string[];
  };
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

interface CreateFormData {
  name: string;
  title: string;
  description?: string;
  fields: Omit<FormField, 'id'>[];
  settings?: {
    submitText?: string;
    redirectUrl?: string;
    emailNotifications?: string[];
  };
}
```

### Theme Types
```typescript
interface Theme {
  id: string;
  name: string;
  description?: string;
  version: string;
  config: {
    colors: Record<string, string>;
    fonts: Record<string, string>;
    layout: Record<string, any>;
    components: Record<string, any>;
  };
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

interface CreateThemeData {
  name: string;
  description?: string;
  version: string;
  config: {
    colors: Record<string, string>;
    fonts: Record<string, string>;
    layout: Record<string, any>;
    components: Record<string, any>;
  };
}
```

### Newsletter Types
```typescript
type NewsletterStatus = 'draft' | 'scheduled' | 'sent' | 'failed';

interface Newsletter {
  id: string;
  subject: string;
  content: string;
  recipients: string[];
  status: NewsletterStatus;
  scheduledAt?: string;
  sentAt?: string;
  stats: {
    sent: number;
    opened: number;
    clicked: number;
    bounced: number;
  };
  createdAt: string;
  updatedAt: string;
}

interface CreateNewsletterData {
  subject: string;
  content: string;
  recipients: string[];
  scheduledAt?: string;
}
```

### Permission & Role Types
```typescript
interface Permission {
  id: string;
  name: string;
  resource: string;
  action: string;
  description?: string;
}

interface Role {
  id: string;
  name: string;
  description?: string;
  permissions: Permission[];
  organizationId?: string;
  isSystemRole: boolean;
}
```

### Audit Log Types
```typescript
interface AuditLog {
  id: string;
  userId: string;
  action: string;
  resource: string;
  resourceId?: string;
  details: Record<string, any>;
  ip: string;
  userAgent: string;
  timestamp: string;
}

interface CreateAuditLogData {
  userId: string;
  action: string;
  resource: string;
  resourceId?: string;
  details?: Record<string, any>;
  ip: string;
  userAgent: string;
}

interface AuditLogFilter {
  page?: number;
  limit?: number;
  userId?: string;
  action?: string;
  resource?: string;
  startDate?: string;
  endDate?: string;
}
```

### Filter Types
```typescript
type FilterType = 'search' | 'category' | 'tag' | 'date' | 'custom';

interface Filter {
  id: string;
  name: string;
  type: FilterType;
  criteria: Record<string, any>;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

interface CreateFilterData {
  name: string;
  type: FilterType;
  criteria: Record<string, any>;
}
```

### API Client Types
```typescript
interface ApiClientConfig {
  baseURL: string;
  timeout?: number;
  headers?: Record<string, string>;
}

interface RequestOptions {
  headers?: Record<string, string>;
  params?: Record<string, any>;
  timeout?: number;
}

interface HttpClient {
  get<T>(endpoint: string, options?: RequestOptions): Promise<ApiResponse<T>>;
  post<T>(endpoint: string, data?: any, options?: RequestOptions): Promise<ApiResponse<T>>;
  put<T>(endpoint: string, data?: any, options?: RequestOptions): Promise<ApiResponse<T>>;
  delete<T>(endpoint: string, options?: RequestOptions): Promise<ApiResponse<T>>;
  patch<T>(endpoint: string, data?: any, options?: RequestOptions): Promise<ApiResponse<T>>;
}
```

### Service Interface Types
```typescript
interface AuthService {
  login(credentials: LoginCredentials): Promise<ApiResponse<AuthResponse>>;
  register(userData: RegisterData): Promise<ApiResponse<AuthResponse>>;
  logout(): Promise<ApiResponse<void>>;
  refreshToken(refreshToken: string): Promise<ApiResponse<AuthResponse>>;
  verifyToken(): Promise<ApiResponse<{ user: User; tokenValid: boolean }>>;
  getCurrentUser(): User | null;
  getAccessToken(): string | null;
  isAuthenticated(): boolean;
}

interface UserService {
  getUsers(filter?: UserFilter): Promise<ApiResponse<PaginatedResponse<User>>>;
  getUserById(id: string): Promise<ApiResponse<User>>;
  createUser(data: CreateUserData): Promise<ApiResponse<User>>;
  updateUser(id: string, data: UpdateUserData): Promise<ApiResponse<User>>;
  deleteUser(id: string): Promise<ApiResponse<void>>;
}

interface ContentService {
  getContent(filter?: ContentFilter): Promise<ApiResponse<PaginatedResponse<Content>>>;
  getContentById(id: string): Promise<ApiResponse<Content>>;
  getContentBySlug(slug: string): Promise<ApiResponse<Content>>;
  createContent(data: CreateContentData): Promise<ApiResponse<Content>>;
  updateContent(id: string, data: UpdateContentData): Promise<ApiResponse<Content>>;
  deleteContent(id: string): Promise<ApiResponse<void>>;
}
```

### React Hook Types
```typescript
interface UseAuthReturn {
  user: User | null;
  isLoading: boolean;
  isAuthenticated: boolean;
  login: (email: string, password: string) => Promise<boolean>;
  logout: () => Promise<void>;
  register: (userData: RegisterData) => Promise<boolean>;
}

interface UseContentReturn {
  content: Content[];
  isLoading: boolean;
  error: string | null;
  fetchContent: () => Promise<void>;
  createContent: (data: CreateContentData) => Promise<boolean>;
  updateContent: (id: string, data: UpdateContentData) => Promise<boolean>;
  deleteContent: (id: string) => Promise<boolean>;
}

interface UseUsersReturn {
  users: User[];
  isLoading: boolean;
  error: string | null;
  fetchUsers: () => Promise<void>;
  createUser: (data: CreateUserData) => Promise<boolean>;
  updateUser: (id: string, data: UpdateUserData) => Promise<boolean>;
  deleteUser: (id: string) => Promise<boolean>;
}
```

### Environment Types
```typescript
interface EnvironmentConfig {
  NODE_ENV: 'development' | 'test' | 'staging' | 'production';
  GATEWAY_PORT: number;
  CORE_PORT: number;
  CORE_SERVICE_URL: string;
  MONGO_URI: string;
  ACCESS_TOKEN_SECRET: string;
  REFRESH_TOKEN_SECRET: string;
  ACCESS_TOKEN_EXPIRY: string;
  REFRESH_TOKEN_EXPIRY: string;
  ALLOWED_IPS?: string;
  CORS_ORIGIN: string;
  THROTTLE_TTL: number;
  THROTTLE_LIMIT: number;
}

interface NextJsEnvironment {
  NEXT_PUBLIC_API_URL: string;
  NEXT_PUBLIC_APP_NAME: string;
  NEXT_PUBLIC_APP_VERSION: string;
}
```

These TypeScript definitions provide comprehensive type safety for all API interactions and data structures in the Legalyard Backend system.
