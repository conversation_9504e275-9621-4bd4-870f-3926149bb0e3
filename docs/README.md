# Legalyard Backend Documentation

## 📚 Documentation Overview

This documentation provides comprehensive information for AI agents and developers to understand and integrate with the Legalyard Backend APIs when building Next.js applications.

## 📁 Documentation Structure

### 1. [API_DOCUMENTATION.md](./API_DOCUMENTATION.md)
**Complete API reference and architecture overview**
- System architecture and request flow
- Authentication system details
- All Gateway and Core API endpoints
- Request/response examples
- Error handling patterns
- Security features and rate limiting

### 2. [API_REFERENCE.md](./API_REFERENCE.md)
**Detailed endpoint reference with examples**
- Complete endpoint documentation
- Request/response schemas
- HTTP status codes
- Query parameters and filters
- Authentication requirements
- Pagination details

### 3. [NEXTJS_INTEGRATION_GUIDE.md](./NEXTJS_INTEGRATION_GUIDE.md)
**Step-by-step Next.js integration guide**
- Environment setup
- API client implementation
- Authentication service
- Content management service
- React hooks for API integration
- Complete page examples

### 4. [TYPESCRIPT_DEFINITIONS.md](./TYPESCRIPT_DEFINITIONS.md)
**Comprehensive TypeScript type definitions**
- All API response types
- Authentication interfaces
- User and organization types
- Content management types
- Service interface definitions
- React hook types

### 5. [DEVELOPMENT_WORKFLOW.md](./DEVELOPMENT_WORKFLOW.md)
**Development patterns and best practices**
- Quick start guide for AI agents
- Common development scenarios
- API integration patterns
- Testing procedures
- Best practices and guidelines

## 🚀 Quick Start for AI Agents

### 1. Understanding the Architecture
```
Next.js Frontend → Gateway (3000) → Core (3001) → MongoDB
```

### 2. Key Integration Points
- **All API calls** go through Gateway app (port 3000)
- **Authentication** handled via Bearer tokens
- **Business logic** accessed via `/api/core/*` proxy endpoints
- **Real-time docs** available at Swagger endpoints

### 3. Essential URLs
- **Gateway API**: http://localhost:3000/api/docs
- **Core API**: http://localhost:3001/api/docs
- **Gateway Health**: http://localhost:3000/api/health
- **Core Health**: http://localhost:3001/api/health

## 🔑 Authentication Flow

```typescript
// 1. Login
POST /api/auth/login
{
  "email": "<EMAIL>",
  "password": "password123"
}

// 2. Response
{
  "accessToken": "jwt_token",
  "refreshToken": "refresh_token",
  "user": { ... },
  "expiresIn": 900
}

// 3. Authenticated requests
GET /api/core/admin/users
Authorization: Bearer jwt_token
```

## 📊 API Endpoints Summary

### Gateway App (Port 3000)
```
GET  /api                    # Status check
GET  /api/health            # Health check
POST /api/auth/login        # User login
POST /api/auth/register     # User registration
POST /api/auth/refresh      # Token refresh
POST /api/auth/logout       # User logout
GET  /api/auth/verify       # Token verification
ALL  /api/core/*           # Proxy to Core app
```

### Core App (Port 3001) - via Gateway Proxy
```
# Admin Module
GET    /api/core/admin/users           # List users
POST   /api/core/admin/users           # Create user
PUT    /api/core/admin/users/:id       # Update user
DELETE /api/core/admin/users/:id       # Delete user
GET    /api/core/admin/tenants         # List organizations
POST   /api/core/admin/tenants         # Create organization
GET    /api/core/admin/permissions     # List permissions
GET    /api/core/admin/audit-logs      # View audit logs

# Coder Module (CMS)
GET    /api/core/coder/content         # List content
POST   /api/core/coder/content         # Create content
PUT    /api/core/coder/content/:id     # Update content
DELETE /api/core/coder/content/:id     # Delete content
GET    /api/core/coder/forms           # List forms
POST   /api/core/coder/forms           # Create form
GET    /api/core/coder/themes          # List themes
POST   /api/core/coder/themes          # Create theme
GET    /api/core/coder/newsletter      # List newsletters
POST   /api/core/coder/newsletter      # Create newsletter
```

## 🛠️ Development Setup

### Backend Services
```bash
# Start Core app
npm run start:dev:core

# Start Gateway app  
npm run start:dev:gateway

# Verify services
curl http://localhost:3000/api/health
curl http://localhost:3001/api/health
```

### Next.js Integration
```typescript
// Environment variables
NEXT_PUBLIC_API_URL=http://localhost:3000

// API client setup
const apiClient = {
  baseURL: process.env.NEXT_PUBLIC_API_URL,
  headers: { 'Content-Type': 'application/json' }
};

// Authentication
const login = async (credentials) => {
  const response = await fetch(`${apiClient.baseURL}/api/auth/login`, {
    method: 'POST',
    headers: apiClient.headers,
    body: JSON.stringify(credentials)
  });
  return response.json();
};
```

## 🔒 Security Features

- **Bearer Token Authentication** with refresh mechanism
- **Rate Limiting** (100 requests/minute by default)
- **CORS Protection** with configurable origins
- **Helmet Security Headers** (CSP, X-Frame-Options, etc.)
- **IP Allowlisting** for admin access
- **Device Tracking** (max 2 devices per user)
- **TOTP MFA Support** for enhanced security

## 📝 Data Models

### User
```typescript
interface User {
  id: string;
  email: string;
  username?: string;
  firstName?: string;
  lastName?: string;
  roles: string[];
  organizationId?: string;
  isActive: boolean;
  isEmailVerified: boolean;
  createdAt: string;
  updatedAt: string;
}
```

### Content
```typescript
interface Content {
  id: string;
  title: string;
  slug: string;
  body: string;
  status: 'draft' | 'published' | 'archived';
  type: 'page' | 'post' | 'article';
  authorId: string;
  tags: string[];
  metadata: Record<string, any>;
  createdAt: string;
  updatedAt: string;
}
```

## 🧪 Testing

### API Testing
```bash
# Test authentication
curl -X POST http://localhost:3000/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","password":"password123"}'

# Test protected endpoint
curl http://localhost:3000/api/core/admin/users \
  -H "Authorization: Bearer YOUR_TOKEN"
```

### Frontend Testing
```typescript
// Test API integration
const testAuth = async () => {
  const response = await fetch('http://localhost:3000/api/auth/login', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({ email: '<EMAIL>', password: 'password123' })
  });
  
  const result = await response.json();
  console.log('Auth result:', result);
};
```

## 🎯 Best Practices for AI Agents

1. **Always use Gateway endpoints** (port 3000) for frontend integration
2. **Implement proper error handling** with token refresh logic
3. **Use TypeScript definitions** for type safety
4. **Follow authentication patterns** consistently
5. **Handle loading states** for better UX
6. **Validate user permissions** before showing UI elements
7. **Test API endpoints** before implementing UI components
8. **Use environment variables** for configuration
9. **Implement proper logout** with token cleanup
10. **Follow RESTful conventions** for API design

## 📞 Support

For questions or issues:
1. Check the Swagger documentation at runtime
2. Review the comprehensive examples in each documentation file
3. Test API endpoints using the provided curl commands
4. Verify authentication flow with the step-by-step guides

## 🔄 Updates

This documentation is maintained alongside the codebase. When the API changes:
1. Swagger documentation updates automatically
2. Type definitions should be updated accordingly
3. Integration examples should reflect new patterns
4. Testing procedures should include new endpoints

---

**Note**: This documentation is designed specifically for AI agents building Next.js applications. It provides precise, actionable information for accurate API integration and UI development.
