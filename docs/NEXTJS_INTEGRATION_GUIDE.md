# Next.js Integration Guide for Legalyard Backend

## Overview

This guide provides detailed instructions for integrating the Legalyard Backend APIs with Next.js applications. The backend consists of two NestJS applications working together to provide a complete API solution.

## Architecture Integration

```
Next.js App → Gateway (8000) → Core (8001) → MongoDB
```

### Key Integration Points:
1. **All API calls** should go through the Gateway app (port 8000)
2. **Authentication** is handled by Gateway and forwarded to Core
3. **Business logic** endpoints are accessed via Gateway proxy (`/api/core/*`)
4. **Real-time features** can be added later via WebSocket connections

## Environment Setup

### Next.js Environment Variables
```bash
# .env.local
NEXT_PUBLIC_API_URL=http://localhost:8000
NEXT_PUBLIC_APP_NAME=Legalyard
NEXT_PUBLIC_APP_VERSION=1.0.0

# For production
NEXT_PUBLIC_API_URL=https://api.yourdomain.com
```

## API Client Setup

### 1. Create API Client Utility
```typescript
// lib/api-client.ts
interface ApiResponse<T = any> {
  data?: T;
  error?: string;
  message?: string;
  statusCode?: number;
}

class ApiClient {
  private baseURL: string;
  private token: string | null = null;

  constructor(baseURL: string) {
    this.baseURL = baseURL;
  }

  setToken(token: string) {
    this.token = token;
  }

  clearToken() {
    this.token = null;
  }

  private async request<T>(
    endpoint: string,
    options: RequestInit = {}
  ): Promise<ApiResponse<T>> {
    const url = `${this.baseURL}${endpoint}`;
    
    const headers: HeadersInit = {
      'Content-Type': 'application/json',
      ...options.headers,
    };

    if (this.token) {
      headers.Authorization = `Bearer ${this.token}`;
    }

    try {
      const response = await fetch(url, {
        ...options,
        headers,
      });

      const data = await response.json();

      if (!response.ok) {
        return {
          error: data.message || 'An error occurred',
          statusCode: response.status,
        };
      }

      return { data };
    } catch (error) {
      return {
        error: 'Network error occurred',
        statusCode: 500,
      };
    }
  }

  // HTTP Methods
  async get<T>(endpoint: string): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, { method: 'GET' });
  }

  async post<T>(endpoint: string, body?: any): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, {
      method: 'POST',
      body: body ? JSON.stringify(body) : undefined,
    });
  }

  async put<T>(endpoint: string, body?: any): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, {
      method: 'PUT',
      body: body ? JSON.stringify(body) : undefined,
    });
  }

  async delete<T>(endpoint: string): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, { method: 'DELETE' });
  }
}

export const apiClient = new ApiClient(
  process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000'
);
```

### 2. Authentication Service
```typescript
// lib/auth-service.ts
import { apiClient } from './api-client';

export interface LoginCredentials {
  email: string;
  password: string;
  totpCode?: string;
}

export interface RegisterData {
  email: string;
  password: string;
  username?: string;
  firstName?: string;
  lastName?: string;
}

export interface User {
  id: string;
  email: string;
  username?: string;
  roles: string[];
  organizationId?: string;
}

export interface AuthResponse {
  accessToken: string;
  refreshToken: string;
  user: User;
  expiresIn: number;
}

export class AuthService {
  async login(credentials: LoginCredentials): Promise<ApiResponse<AuthResponse>> {
    const response = await apiClient.post<AuthResponse>('/api/auth/login', credentials);
    
    if (response.data) {
      // Store tokens
      localStorage.setItem('accessToken', response.data.accessToken);
      localStorage.setItem('refreshToken', response.data.refreshToken);
      localStorage.setItem('user', JSON.stringify(response.data.user));
      
      // Set token in API client
      apiClient.setToken(response.data.accessToken);
    }
    
    return response;
  }

  async register(userData: RegisterData): Promise<ApiResponse<AuthResponse>> {
    return apiClient.post<AuthResponse>('/api/auth/register', userData);
  }

  async logout(): Promise<void> {
    await apiClient.post('/api/auth/logout');
    
    // Clear local storage
    localStorage.removeItem('accessToken');
    localStorage.removeItem('refreshToken');
    localStorage.removeItem('user');
    
    // Clear token from API client
    apiClient.clearToken();
  }

  async refreshToken(): Promise<ApiResponse<AuthResponse>> {
    const refreshToken = localStorage.getItem('refreshToken');
    
    if (!refreshToken) {
      return { error: 'No refresh token available' };
    }

    const response = await apiClient.post<AuthResponse>('/api/auth/refresh', {
      refreshToken,
    });

    if (response.data) {
      localStorage.setItem('accessToken', response.data.accessToken);
      apiClient.setToken(response.data.accessToken);
    }

    return response;
  }

  getCurrentUser(): User | null {
    const userStr = localStorage.getItem('user');
    return userStr ? JSON.parse(userStr) : null;
  }

  getAccessToken(): string | null {
    return localStorage.getItem('accessToken');
  }

  isAuthenticated(): boolean {
    return !!this.getAccessToken();
  }

  initializeAuth(): void {
    const token = this.getAccessToken();
    if (token) {
      apiClient.setToken(token);
    }
  }
}

export const authService = new AuthService();
```

### 3. Content Management Service
```typescript
// lib/content-service.ts
import { apiClient } from './api-client';

export interface Content {
  id: string;
  title: string;
  slug: string;
  body: string;
  excerpt?: string;
  status: 'draft' | 'published' | 'archived';
  type: 'page' | 'post' | 'article';
  authorId: string;
  tags: string[];
  metadata: Record<string, any>;
  publishedAt?: string;
  createdAt: string;
  updatedAt: string;
}

export interface CreateContentData {
  title: string;
  body: string;
  excerpt?: string;
  type: 'page' | 'post' | 'article';
  status?: 'draft' | 'published' | 'archived';
  tags?: string[];
  metadata?: Record<string, any>;
}

export class ContentService {
  async getContent(): Promise<ApiResponse<Content[]>> {
    return apiClient.get<Content[]>('/api/core/coder/content');
  }

  async getContentById(id: string): Promise<ApiResponse<Content>> {
    return apiClient.get<Content>(`/api/core/coder/content/${id}`);
  }

  async createContent(data: CreateContentData): Promise<ApiResponse<Content>> {
    return apiClient.post<Content>('/api/core/coder/content', data);
  }

  async updateContent(id: string, data: Partial<CreateContentData>): Promise<ApiResponse<Content>> {
    return apiClient.put<Content>(`/api/core/coder/content/${id}`, data);
  }

  async deleteContent(id: string): Promise<ApiResponse<void>> {
    return apiClient.delete<void>(`/api/core/coder/content/${id}`);
  }

  async getContentBySlug(slug: string): Promise<ApiResponse<Content>> {
    return apiClient.get<Content>(`/api/core/coder/content/slug/${slug}`);
  }
}

export const contentService = new ContentService();
```

### 4. User Management Service
```typescript
// lib/user-service.ts
import { apiClient } from './api-client';

export interface User {
  id: string;
  email: string;
  username?: string;
  firstName?: string;
  lastName?: string;
  roles: string[];
  organizationId?: string;
  isActive: boolean;
  isEmailVerified: boolean;
  lastLoginAt?: string;
  createdAt: string;
  updatedAt: string;
}

export interface CreateUserData {
  email: string;
  password: string;
  username?: string;
  firstName?: string;
  lastName?: string;
  roles?: string[];
  organizationId?: string;
}

export class UserService {
  async getUsers(): Promise<ApiResponse<User[]>> {
    return apiClient.get<User[]>('/api/core/admin/users');
  }

  async getUserById(id: string): Promise<ApiResponse<User>> {
    return apiClient.get<User>(`/api/core/admin/users/${id}`);
  }

  async createUser(data: CreateUserData): Promise<ApiResponse<User>> {
    return apiClient.post<User>('/api/core/admin/users', data);
  }

  async updateUser(id: string, data: Partial<CreateUserData>): Promise<ApiResponse<User>> {
    return apiClient.put<User>(`/api/core/admin/users/${id}`, data);
  }

  async deleteUser(id: string): Promise<ApiResponse<void>> {
    return apiClient.delete<void>(`/api/core/admin/users/${id}`);
  }
}

export const userService = new UserService();
```

## React Hooks for API Integration

### 1. Authentication Hook
```typescript
// hooks/useAuth.ts
import { useState, useEffect, useContext, createContext } from 'react';
import { authService, User } from '../lib/auth-service';

interface AuthContextType {
  user: User | null;
  isLoading: boolean;
  isAuthenticated: boolean;
  login: (email: string, password: string) => Promise<boolean>;
  logout: () => Promise<void>;
  register: (userData: RegisterData) => Promise<boolean>;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export function AuthProvider({ children }: { children: React.ReactNode }) {
  const [user, setUser] = useState<User | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    // Initialize auth on app start
    authService.initializeAuth();
    const currentUser = authService.getCurrentUser();
    setUser(currentUser);
    setIsLoading(false);
  }, []);

  const login = async (email: string, password: string): Promise<boolean> => {
    setIsLoading(true);
    const response = await authService.login({ email, password });
    
    if (response.data) {
      setUser(response.data.user);
      setIsLoading(false);
      return true;
    }
    
    setIsLoading(false);
    return false;
  };

  const logout = async (): Promise<void> => {
    setIsLoading(true);
    await authService.logout();
    setUser(null);
    setIsLoading(false);
  };

  const register = async (userData: RegisterData): Promise<boolean> => {
    setIsLoading(true);
    const response = await authService.register(userData);
    setIsLoading(false);
    return !!response.data;
  };

  return (
    <AuthContext.Provider
      value={{
        user,
        isLoading,
        isAuthenticated: !!user,
        login,
        logout,
        register,
      }}
    >
      {children}
    </AuthContext.Provider>
  );
}

export function useAuth() {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
}
```

### 2. Content Management Hook
```typescript
// hooks/useContent.ts
import { useState, useEffect } from 'react';
import { contentService, Content } from '../lib/content-service';

export function useContent() {
  const [content, setContent] = useState<Content[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const fetchContent = async () => {
    setIsLoading(true);
    setError(null);
    
    const response = await contentService.getContent();
    
    if (response.data) {
      setContent(response.data);
    } else {
      setError(response.error || 'Failed to fetch content');
    }
    
    setIsLoading(false);
  };

  const createContent = async (data: CreateContentData): Promise<boolean> => {
    setIsLoading(true);
    const response = await contentService.createContent(data);
    
    if (response.data) {
      await fetchContent(); // Refresh list
      setIsLoading(false);
      return true;
    }
    
    setError(response.error || 'Failed to create content');
    setIsLoading(false);
    return false;
  };

  useEffect(() => {
    fetchContent();
  }, []);

  return {
    content,
    isLoading,
    error,
    fetchContent,
    createContent,
  };
}
```

## Page Examples

### 1. Login Page
```typescript
// pages/login.tsx
import { useState } from 'react';
import { useRouter } from 'next/router';
import { useAuth } from '../hooks/useAuth';

export default function LoginPage() {
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [error, setError] = useState('');
  const { login, isLoading } = useAuth();
  const router = useRouter();

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError('');

    const success = await login(email, password);
    
    if (success) {
      router.push('/dashboard');
    } else {
      setError('Invalid credentials');
    }
  };

  return (
    <form onSubmit={handleSubmit}>
      <input
        type="email"
        value={email}
        onChange={(e) => setEmail(e.target.value)}
        placeholder="Email"
        required
      />
      <input
        type="password"
        value={password}
        onChange={(e) => setPassword(e.target.value)}
        placeholder="Password"
        required
      />
      {error && <p style={{ color: 'red' }}>{error}</p>}
      <button type="submit" disabled={isLoading}>
        {isLoading ? 'Logging in...' : 'Login'}
      </button>
    </form>
  );
}
```

This guide provides comprehensive integration patterns for building Next.js applications with the Legalyard Backend APIs.
