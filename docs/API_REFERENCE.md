# Legalyard Backend API Reference

## Base URLs
- **Gateway**: `http://localhost:3000` (Production: `https://api.yourdomain.com`)
- **Core**: `http://localhost:3001` (Internal use only)

## Authentication

All API requests (except login/register) require authentication via Bearer token:

```http
Authorization: Bearer <access_token>
```

## Gateway API Endpoints

### Health & Status

#### GET /api
Returns Gateway status information.

**Response:**
```json
"Gateway API is running!"
```

#### GET /api/health
Returns detailed Gateway health information.

**Response:**
```json
{
  "status": "ok",
  "timestamp": "2025-07-12T16:30:00.000Z",
  "service": "gateway"
}
```

### Authentication Endpoints

#### POST /api/auth/login
Authenticate user and receive access tokens.

**Request Body:**
```json
{
  "email": "<EMAIL>",
  "password": "password123",
  "totpCode": "123456"  // Optional, required if MFA enabled
}
```

**Response (200):**
```json
{
  "accessToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "refreshToken": "refresh_token_string",
  "user": {
    "id": "user_id",
    "email": "<EMAIL>",
    "username": "johndoe",
    "roles": ["user"],
    "organizationId": "org_id"
  },
  "expiresIn": 900
}
```

**Error Response (401):**
```json
{
  "statusCode": 401,
  "message": "Invalid credentials",
  "error": "Unauthorized"
}
```

#### POST /api/auth/register
Register a new user account.

**Request Body:**
```json
{
  "email": "<EMAIL>",
  "password": "securepassword123",
  "username": "newuser",        // Optional
  "firstName": "John",          // Optional
  "lastName": "Doe"             // Optional
}
```

**Response (201):**
```json
{
  "accessToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "refreshToken": "refresh_token_string",
  "user": {
    "id": "new_user_id",
    "email": "<EMAIL>",
    "username": "newuser",
    "roles": ["user"],
    "isActive": true,
    "isEmailVerified": false
  },
  "expiresIn": 900
}
```

#### POST /api/auth/refresh
Refresh access token using refresh token.

**Request Body:**
```json
{
  "refreshToken": "refresh_token_string"
}
```

**Response (200):**
```json
{
  "accessToken": "new_access_token",
  "refreshToken": "new_refresh_token",
  "expiresIn": 900
}
```

#### POST /api/auth/logout
Logout user and invalidate tokens.

**Headers:**
```http
Authorization: Bearer <access_token>
```

**Response (200):**
```json
{
  "message": "Logout successful"
}
```

#### GET /api/auth/verify
Verify current access token and get user info.

**Headers:**
```http
Authorization: Bearer <access_token>
```

**Response (200):**
```json
{
  "user": {
    "id": "user_id",
    "email": "<EMAIL>",
    "username": "johndoe",
    "roles": ["user"],
    "organizationId": "org_id"
  },
  "tokenValid": true
}
```

### Proxy Endpoints

#### ALL /api/core/*
Proxy all requests to Core service. Replace `/api/core/` with `/api/` when forwarding.

**Example:**
- `GET /api/core/admin/users` → `GET http://localhost:3001/api/admin/users`
- `POST /api/core/coder/content` → `POST http://localhost:3001/api/coder/content`

**Headers Added by Gateway:**
```http
X-User-ID: user_id
X-User-Role: user_role
X-User-Org: organization_id
```

## Core API Endpoints (via Gateway Proxy)

### Admin Module

#### GET /api/core/admin/users
List all users (Admin only).

**Headers:**
```http
Authorization: Bearer <access_token>
```

**Response (200):**
```json
[
  {
    "id": "user_id",
    "email": "<EMAIL>",
    "username": "johndoe",
    "firstName": "John",
    "lastName": "Doe",
    "roles": ["user"],
    "organizationId": "org_id",
    "isActive": true,
    "isEmailVerified": true,
    "lastLoginAt": "2025-07-12T15:30:00.000Z",
    "createdAt": "2025-07-01T10:00:00.000Z",
    "updatedAt": "2025-07-12T15:30:00.000Z"
  }
]
```

#### POST /api/core/admin/users
Create a new user (Admin only).

**Request Body:**
```json
{
  "email": "<EMAIL>",
  "password": "securepassword123",
  "username": "adminuser",
  "firstName": "Admin",
  "lastName": "User",
  "roles": ["admin"],
  "organizationId": "org_id"
}
```

**Response (201):**
```json
{
  "id": "new_user_id",
  "email": "<EMAIL>",
  "username": "adminuser",
  "firstName": "Admin",
  "lastName": "User",
  "roles": ["admin"],
  "organizationId": "org_id",
  "isActive": true,
  "isEmailVerified": false,
  "createdAt": "2025-07-12T16:30:00.000Z",
  "updatedAt": "2025-07-12T16:30:00.000Z"
}
```

#### PUT /api/core/admin/users/:id
Update user information (Admin only).

**Request Body:**
```json
{
  "firstName": "Updated",
  "lastName": "Name",
  "roles": ["user", "moderator"],
  "isActive": true
}
```

**Response (200):**
```json
{
  "id": "user_id",
  "email": "<EMAIL>",
  "username": "johndoe",
  "firstName": "Updated",
  "lastName": "Name",
  "roles": ["user", "moderator"],
  "isActive": true,
  "updatedAt": "2025-07-12T16:35:00.000Z"
}
```

#### DELETE /api/core/admin/users/:id
Delete a user (Admin only).

**Response (200):**
```json
{
  "message": "User deleted successfully"
}
```

#### GET /api/core/admin/tenants
List all organizations/tenants (Admin only).

**Response (200):**
```json
[
  {
    "id": "org_id",
    "name": "Example Organization",
    "domain": "example.com",
    "settings": {
      "allowedDomains": ["example.com"],
      "maxUsers": 100,
      "features": ["cms", "analytics"]
    },
    "isActive": true,
    "subscriptionPlan": "premium",
    "subscriptionExpires": "2025-12-31T23:59:59.000Z",
    "createdAt": "2025-01-01T00:00:00.000Z",
    "updatedAt": "2025-07-12T16:30:00.000Z"
  }
]
```

#### POST /api/core/admin/tenants
Create a new organization (Admin only).

**Request Body:**
```json
{
  "name": "New Organization",
  "domain": "neworg.com",
  "settings": {
    "allowedDomains": ["neworg.com"],
    "maxUsers": 50,
    "features": ["cms"]
  }
}
```

#### GET /api/core/admin/permissions
List all available permissions (Admin only).

**Response (200):**
```json
[
  {
    "id": "perm_id",
    "name": "users.read",
    "resource": "users",
    "action": "read",
    "description": "Read user information"
  },
  {
    "id": "perm_id_2",
    "name": "content.write",
    "resource": "content",
    "action": "write",
    "description": "Create and edit content"
  }
]
```

#### GET /api/core/admin/audit-logs
View system audit logs (Admin only).

**Query Parameters:**
- `page`: Page number (default: 1)
- `limit`: Items per page (default: 20)
- `userId`: Filter by user ID
- `action`: Filter by action type
- `resource`: Filter by resource type

**Response (200):**
```json
{
  "data": [
    {
      "id": "log_id",
      "userId": "user_id",
      "action": "user.login",
      "resource": "auth",
      "resourceId": "user_id",
      "details": {
        "ip": "***********",
        "userAgent": "Mozilla/5.0..."
      },
      "ip": "***********",
      "userAgent": "Mozilla/5.0...",
      "timestamp": "2025-07-12T16:30:00.000Z"
    }
  ],
  "pagination": {
    "page": 1,
    "limit": 20,
    "total": 150,
    "pages": 8
  }
}
```

### Coder Module (CMS)

#### GET /api/core/coder/content
List all content items.

**Query Parameters:**
- `page`: Page number (default: 1)
- `limit`: Items per page (default: 20)
- `status`: Filter by status (draft, published, archived)
- `type`: Filter by type (page, post, article)
- `search`: Search in title and body

**Response (200):**
```json
{
  "data": [
    {
      "id": "content_id",
      "title": "Sample Article",
      "slug": "sample-article",
      "body": "This is the article content...",
      "excerpt": "Brief description",
      "status": "published",
      "type": "article",
      "authorId": "user_id",
      "tags": ["technology", "tutorial"],
      "metadata": {
        "seoTitle": "Sample Article - Tech Tutorial",
        "seoDescription": "Learn about technology"
      },
      "publishedAt": "2025-07-12T16:00:00.000Z",
      "createdAt": "2025-07-12T15:30:00.000Z",
      "updatedAt": "2025-07-12T16:00:00.000Z"
    }
  ],
  "pagination": {
    "page": 1,
    "limit": 20,
    "total": 45,
    "pages": 3
  }
}
```

#### POST /api/core/coder/content
Create new content.

**Request Body:**
```json
{
  "title": "New Article",
  "body": "This is the content body...",
  "excerpt": "Brief description",
  "type": "article",
  "status": "draft",
  "tags": ["news", "update"],
  "metadata": {
    "seoTitle": "New Article - Latest News",
    "seoDescription": "Stay updated with latest news"
  }
}
```

**Response (201):**
```json
{
  "id": "new_content_id",
  "title": "New Article",
  "slug": "new-article",
  "body": "This is the content body...",
  "excerpt": "Brief description",
  "status": "draft",
  "type": "article",
  "authorId": "current_user_id",
  "tags": ["news", "update"],
  "metadata": {
    "seoTitle": "New Article - Latest News",
    "seoDescription": "Stay updated with latest news"
  },
  "createdAt": "2025-07-12T16:30:00.000Z",
  "updatedAt": "2025-07-12T16:30:00.000Z"
}
```

#### GET /api/core/coder/content/:id
Get content by ID.

**Response (200):**
```json
{
  "id": "content_id",
  "title": "Sample Article",
  "slug": "sample-article",
  "body": "Full article content...",
  "excerpt": "Brief description",
  "status": "published",
  "type": "article",
  "authorId": "user_id",
  "tags": ["technology", "tutorial"],
  "metadata": {
    "seoTitle": "Sample Article - Tech Tutorial",
    "seoDescription": "Learn about technology"
  },
  "publishedAt": "2025-07-12T16:00:00.000Z",
  "createdAt": "2025-07-12T15:30:00.000Z",
  "updatedAt": "2025-07-12T16:00:00.000Z"
}
```

#### PUT /api/core/coder/content/:id
Update existing content.

**Request Body:**
```json
{
  "title": "Updated Article Title",
  "body": "Updated content body...",
  "status": "published",
  "publishedAt": "2025-07-12T17:00:00.000Z"
}
```

#### DELETE /api/core/coder/content/:id
Delete content.

**Response (200):**
```json
{
  "message": "Content deleted successfully"
}
```

This reference provides detailed information about all available API endpoints, request/response formats, and authentication requirements.
