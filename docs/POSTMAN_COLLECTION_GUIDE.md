# Postman Collection Guide for Legalyard Backend API

## 🎯 Overview

The Legalyard Backend provides a comprehensive Postman collection that includes all API endpoints with pre-configured authentication, examples, and automatic token management. This guide explains how to download, import, and use the collection.

## 📥 Download Methods

### Method 1: Download Page (Recommended)
Visit the beautiful download page with instructions:
```
http://localhost:8000/api/core/docs/postman-download
```

This page provides:
- Visual download interface
- Complete setup instructions
- Collection overview and features
- Direct download button

### Method 2: Direct API Download
Download the JSON file directly:
```
http://localhost:8000/api/core/docs/postman-collection
```

This endpoint:
- Returns the latest Postman collection JSON
- Sets proper download headers
- Generates a unique collection ID
- Always provides the most current API definitions

## 📦 What's Included

### Collection Structure
The Postman collection includes **50+ endpoints** organized in folders:

#### 🏠 **System Health & Status**
- Gateway Health Check
- Core Health Check  
- Gateway Status

#### 🔐 **Authentication**
- Login (with automatic token storage)
- Register
- Refresh Token (with automatic token update)
- Verify Token
- Logout (with automatic token cleanup)

#### 👥 **Admin Management**
- List Users (with pagination)
- Create User
- Update User
- Delete User
- List Organizations
- Create Organization
- List Permissions
- View Audit Logs

#### 📝 **Content Management (CMS)**
- List Content (with filtering)
- Create Content
- Get Content by ID
- Update Content
- Delete Content
- List Forms
- Create Form
- List Themes
- List Newsletters

#### 📚 **AI Documentation**
- AI Agent Complete Info (master endpoint)
- AI Onboarding Overview
- System Architecture
- Authentication Documentation
- API Reference
- Integration Patterns
- TypeScript Definitions
- Code Examples
- System Requirements
- Testing Guidelines
- Development Workflow
- Data Schemas
- OpenAPI Specification
- Documentation Health
- Download Postman Collection

## 🔧 Pre-Configured Features

### Automatic Authentication
The collection includes sophisticated authentication management:

```javascript
// Pre-request script automatically refreshes expired tokens
if (pm.globals.get("accessToken") && pm.globals.get("refreshToken")) {
    const tokenExpiry = pm.globals.get("tokenExpiry");
    if (tokenExpiry && new Date() > new Date(tokenExpiry)) {
        // Automatically refresh token before request
    }
}
```

### Environment Variables
Pre-configured variables for easy environment switching:
- `{{gatewayUrl}}` - Gateway base URL (http://localhost:8000)
- `{{coreUrl}}` - Core base URL (http://localhost:8001)
- `{{accessToken}}` - Current access token
- `{{refreshToken}}` - Current refresh token

### Test Scripts
Automatic token management in authentication requests:
- Login: Stores tokens and expiry time
- Refresh: Updates tokens automatically
- Logout: Clears all stored tokens

## 🚀 Setup Instructions

### Step 1: Download Collection
1. Visit: http://localhost:8000/api/core/docs/postman-download
2. Click "📥 Download Postman Collection" button
3. Save the `Legalyard-Backend-API.postman_collection.json` file

### Step 2: Import to Postman
1. Open Postman application
2. Click "Import" in the top left corner
3. Select "Upload Files" or drag and drop the JSON file
4. Click "Import" to add the collection

### Step 3: Configure Environment (Optional)
The collection works with default localhost URLs, but you can customize:
1. Create a new environment in Postman
2. Add variables:
   - `gatewayUrl`: `http://localhost:8000`
   - `coreUrl`: `http://localhost:8001`
3. Select the environment before making requests

### Step 4: Authenticate
1. Open the "🔐 Authentication" folder
2. Run the "Login" request with valid credentials:
   ```json
   {
     "email": "<EMAIL>",
     "password": "password123"
   }
   ```
3. The collection will automatically store the authentication tokens

### Step 5: Start Making Requests
All other requests will now use the stored authentication token automatically.

## 💡 Usage Tips

### Authentication Flow
1. **First Time**: Use "Login" to authenticate
2. **Automatic**: All subsequent requests use stored tokens
3. **Refresh**: Tokens refresh automatically when expired
4. **Logout**: Use "Logout" to clear tokens when done

### Request Organization
- **Health**: Test system status first
- **Authentication**: Login before accessing protected endpoints
- **Admin**: Requires admin role for user management
- **Content**: Requires coder or admin role for CMS operations
- **Documentation**: Public endpoints for AI integration info

### Environment Switching
- **Development**: Use localhost URLs (default)
- **Staging**: Update environment variables to staging URLs
- **Production**: Update environment variables to production URLs

### Testing Workflows
1. **Health Check**: Verify system is running
2. **Authentication**: Login and verify token
3. **User Management**: Test admin operations
4. **Content Operations**: Test CMS functionality
5. **Documentation**: Explore AI integration endpoints

## 🔄 Auto-Updates

### Dynamic Collection
The Postman collection is generated dynamically, ensuring:
- **Always Current**: Reflects latest API changes
- **Accurate Endpoints**: All URLs and parameters up-to-date
- **Latest Examples**: Sample data matches current schemas
- **Version Tracking**: Unique collection IDs for each download

### Re-downloading
To get the latest version:
1. Visit the download page again
2. Download the new collection file
3. Re-import to Postman (it will update the existing collection)

## 🛠️ Advanced Features

### Custom Scripts
The collection includes advanced Postman scripts for:
- Automatic token refresh
- Error handling
- Response validation
- Environment variable management

### Request Templates
Each request includes:
- Proper HTTP methods
- Required headers
- Sample request bodies
- Parameter descriptions
- Response examples

### Folder Organization
Logical grouping makes it easy to:
- Find specific endpoints
- Understand API structure
- Follow authentication flows
- Test complete workflows

## 📊 Collection Statistics

- **Total Endpoints**: 50+
- **Authentication Endpoints**: 5
- **Admin Endpoints**: 8
- **Content Management Endpoints**: 9
- **Documentation Endpoints**: 14
- **Health Check Endpoints**: 3
- **Pre-configured Variables**: 4
- **Automatic Scripts**: 3

## 🔗 Related Resources

- **Swagger Documentation**: http://localhost:8000/api/docs
- **AI Agent Master Endpoint**: http://localhost:8000/api/core/docs/ai-agent-complete
- **System Health**: http://localhost:8000/api/health
- **Download Page**: http://localhost:8000/api/core/docs/postman-download

## 🎉 Benefits

### For Developers
- **Quick Setup**: Import and start testing immediately
- **Complete Coverage**: All endpoints included
- **Authentication Handled**: No manual token management
- **Examples Included**: Sample data for all requests

### For Teams
- **Consistent Testing**: Same collection for all team members
- **Environment Flexibility**: Easy switching between environments
- **Documentation**: Self-documenting API collection
- **Collaboration**: Share collection with team members

### For AI Agents
- **Programmatic Access**: Download collection via API
- **Always Current**: Latest API definitions
- **Complete Reference**: All endpoints and schemas
- **Integration Ready**: Use with Postman API or Newman

---

**Download Now**: [http://localhost:8000/api/core/docs/postman-download](http://localhost:8000/api/core/docs/postman-download)

**Last Updated**: 2025-07-12
