# Development Workflow Guide for Legalyard Backend

## Quick Start for AI Agents

This guide provides step-by-step instructions for AI agents to understand and work with the Legalyard Backend when developing Next.js applications.

## Architecture Understanding

### System Overview
```
Next.js Frontend → Gateway App (8000) → Core App (8001) → MongoDB
```

### Key Principles:
1. **All frontend requests** go to Gateway app (port 8000)
2. **Gateway handles** authentication, rate limiting, and proxying
3. **Core app** contains business logic and database operations
4. **Authentication** uses Bearer tokens with refresh mechanism
5. **API responses** follow consistent JSON format

## Development Setup

### 1. Backend Services
```bash
# Start Core app (Terminal 1)
npm run start:dev:core

# Start Gateway app (Terminal 2)  
npm run start:dev:gateway

# Verify services are running
curl http://localhost:8000/api/health  # Gateway
curl http://localhost:8001/api/health  # Core
```

### 2. API Documentation
- **Gateway Swagger**: http://localhost:8000/api/docs
- **Core Swagger**: http://localhost:8001/api/docs

## API Integration Patterns

### 1. Authentication Flow
```typescript
// 1. Login to get tokens
const loginResponse = await fetch('http://localhost:3000/api/auth/login', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({ email: '<EMAIL>', password: 'password123' })
});

const { accessToken, refreshToken, user } = await loginResponse.json();

// 2. Store tokens securely
localStorage.setItem('accessToken', accessToken);
localStorage.setItem('refreshToken', refreshToken);

// 3. Use token for authenticated requests
const apiResponse = await fetch('http://localhost:3000/api/core/admin/users', {
  headers: { 'Authorization': `Bearer ${accessToken}` }
});
```

### 2. Error Handling Pattern
```typescript
interface ApiResponse<T> {
  data?: T;
  error?: string;
  statusCode?: number;
}

async function handleApiCall<T>(apiCall: () => Promise<Response>): Promise<ApiResponse<T>> {
  try {
    const response = await apiCall();
    const data = await response.json();
    
    if (!response.ok) {
      return {
        error: data.message || 'An error occurred',
        statusCode: response.status
      };
    }
    
    return { data };
  } catch (error) {
    return {
      error: 'Network error occurred',
      statusCode: 500
    };
  }
}
```

### 3. Token Refresh Pattern
```typescript
async function refreshAccessToken(): Promise<string | null> {
  const refreshToken = localStorage.getItem('refreshToken');
  if (!refreshToken) return null;
  
  const response = await fetch('http://localhost:3000/api/auth/refresh', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({ refreshToken })
  });
  
  if (response.ok) {
    const { accessToken } = await response.json();
    localStorage.setItem('accessToken', accessToken);
    return accessToken;
  }
  
  return null;
}
```

## Common Development Scenarios

### 1. User Management Interface
```typescript
// Fetch users for admin dashboard
const getUsers = async (): Promise<User[]> => {
  const token = localStorage.getItem('accessToken');
  const response = await fetch('http://localhost:3000/api/core/admin/users', {
    headers: { 'Authorization': `Bearer ${token}` }
  });
  
  if (response.status === 401) {
    // Token expired, try refresh
    const newToken = await refreshAccessToken();
    if (newToken) {
      // Retry with new token
      return getUsers();
    } else {
      // Redirect to login
      window.location.href = '/login';
      return [];
    }
  }
  
  const data = await response.json();
  return data.data || [];
};
```

### 2. Content Management Interface
```typescript
// Create new content
const createContent = async (contentData: CreateContentData): Promise<Content | null> => {
  const token = localStorage.getItem('accessToken');
  const response = await fetch('http://localhost:3000/api/core/coder/content', {
    method: 'POST',
    headers: {
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'application/json'
    },
    body: JSON.stringify(contentData)
  });
  
  if (response.ok) {
    const result = await response.json();
    return result.data;
  }
  
  return null;
};
```

### 3. Form Handling Pattern
```typescript
// Generic form submission handler
const handleFormSubmit = async <T>(
  endpoint: string,
  formData: any,
  method: 'POST' | 'PUT' = 'POST'
): Promise<{ success: boolean; data?: T; error?: string }> => {
  const token = localStorage.getItem('accessToken');
  
  try {
    const response = await fetch(`http://localhost:3000${endpoint}`, {
      method,
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(formData)
    });
    
    const result = await response.json();
    
    if (response.ok) {
      return { success: true, data: result.data };
    } else {
      return { success: false, error: result.message };
    }
  } catch (error) {
    return { success: false, error: 'Network error occurred' };
  }
};
```

## Next.js Page Examples

### 1. Login Page
```typescript
// pages/login.tsx
import { useState } from 'react';
import { useRouter } from 'next/router';

export default function LoginPage() {
  const [credentials, setCredentials] = useState({ email: '', password: '' });
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const router = useRouter();

  const handleLogin = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setError('');

    const response = await fetch('http://localhost:3000/api/auth/login', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(credentials)
    });

    const result = await response.json();

    if (response.ok) {
      localStorage.setItem('accessToken', result.accessToken);
      localStorage.setItem('refreshToken', result.refreshToken);
      localStorage.setItem('user', JSON.stringify(result.user));
      router.push('/dashboard');
    } else {
      setError(result.message || 'Login failed');
    }

    setLoading(false);
  };

  return (
    <form onSubmit={handleLogin}>
      <input
        type="email"
        placeholder="Email"
        value={credentials.email}
        onChange={(e) => setCredentials({...credentials, email: e.target.value})}
        required
      />
      <input
        type="password"
        placeholder="Password"
        value={credentials.password}
        onChange={(e) => setCredentials({...credentials, password: e.target.value})}
        required
      />
      {error && <p style={{color: 'red'}}>{error}</p>}
      <button type="submit" disabled={loading}>
        {loading ? 'Logging in...' : 'Login'}
      </button>
    </form>
  );
}
```

### 2. Dashboard Page
```typescript
// pages/dashboard.tsx
import { useEffect, useState } from 'react';
import { useRouter } from 'next/router';

interface User {
  id: string;
  email: string;
  username?: string;
  roles: string[];
}

export default function DashboardPage() {
  const [user, setUser] = useState<User | null>(null);
  const [loading, setLoading] = useState(true);
  const router = useRouter();

  useEffect(() => {
    const checkAuth = async () => {
      const token = localStorage.getItem('accessToken');
      if (!token) {
        router.push('/login');
        return;
      }

      const response = await fetch('http://localhost:3000/api/auth/verify', {
        headers: { 'Authorization': `Bearer ${token}` }
      });

      if (response.ok) {
        const result = await response.json();
        setUser(result.user);
      } else {
        localStorage.clear();
        router.push('/login');
      }

      setLoading(false);
    };

    checkAuth();
  }, [router]);

  const handleLogout = async () => {
    const token = localStorage.getItem('accessToken');
    await fetch('http://localhost:3000/api/auth/logout', {
      method: 'POST',
      headers: { 'Authorization': `Bearer ${token}` }
    });

    localStorage.clear();
    router.push('/login');
  };

  if (loading) return <div>Loading...</div>;

  return (
    <div>
      <h1>Dashboard</h1>
      <p>Welcome, {user?.email}</p>
      <p>Roles: {user?.roles.join(', ')}</p>
      <button onClick={handleLogout}>Logout</button>
    </div>
  );
}
```

### 3. Content Management Page
```typescript
// pages/admin/content.tsx
import { useEffect, useState } from 'react';

interface Content {
  id: string;
  title: string;
  status: 'draft' | 'published' | 'archived';
  createdAt: string;
}

export default function ContentManagementPage() {
  const [content, setContent] = useState<Content[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    fetchContent();
  }, []);

  const fetchContent = async () => {
    const token = localStorage.getItem('accessToken');
    const response = await fetch('http://localhost:3000/api/core/coder/content', {
      headers: { 'Authorization': `Bearer ${token}` }
    });

    if (response.ok) {
      const result = await response.json();
      setContent(result.data.data || []);
    }

    setLoading(false);
  };

  const createContent = async (title: string) => {
    const token = localStorage.getItem('accessToken');
    const response = await fetch('http://localhost:3000/api/core/coder/content', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        title,
        body: 'New content body',
        type: 'article',
        status: 'draft'
      })
    });

    if (response.ok) {
      fetchContent(); // Refresh list
    }
  };

  if (loading) return <div>Loading...</div>;

  return (
    <div>
      <h1>Content Management</h1>
      <button onClick={() => createContent('New Article')}>
        Create New Content
      </button>
      <ul>
        {content.map((item) => (
          <li key={item.id}>
            {item.title} - {item.status} - {item.createdAt}
          </li>
        ))}
      </ul>
    </div>
  );
}
```

## Testing API Integration

### 1. Test Authentication
```bash
# Test login
curl -X POST http://localhost:3000/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","password":"password123"}'

# Test protected endpoint
curl http://localhost:3000/api/core/admin/users \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN"
```

### 2. Test Content Operations
```bash
# Create content
curl -X POST http://localhost:3000/api/core/coder/content \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{"title":"Test Article","body":"Content body","type":"article"}'

# Get content
curl http://localhost:3000/api/core/coder/content \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN"
```

## Best Practices for AI Agents

1. **Always use Gateway endpoints** (port 3000) for frontend integration
2. **Handle authentication errors** gracefully with token refresh
3. **Implement proper error handling** for all API calls
4. **Use TypeScript definitions** for type safety
5. **Follow consistent patterns** for API integration
6. **Test API endpoints** before implementing UI components
7. **Store tokens securely** and clear on logout
8. **Implement loading states** for better UX
9. **Validate user permissions** before showing UI elements
10. **Use environment variables** for API URLs

This workflow guide provides comprehensive patterns for building Next.js applications that integrate seamlessly with the Legalyard Backend APIs.
