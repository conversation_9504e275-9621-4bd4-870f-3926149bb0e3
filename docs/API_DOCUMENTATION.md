# Legalyard Backend API Documentation

## Architecture Overview

This is a **NestJS monorepo with 2 applications** designed for microservice-ready architecture:

### 1. Gateway App (Port 3000) - API Proxy & Security Layer
- **Purpose**: Central entry point for all client applications
- **Responsibilities**: Authentication, rate limiting, request proxying, security
- **Technology**: NestJS with Express, Swagger documentation
- **Base URL**: `http://localhost:3000`

### 2. Core App (Port 3001) - Business Logic Engine  
- **Purpose**: Contains all business logic and data operations
- **Responsibilities**: Authentication logic, admin operations, CMS functionality, database operations
- **Technology**: NestJS with Express, MongoDB, Swagger documentation
- **Base URL**: `http://localhost:3001`

## API Flow Architecture

```
Client (Next.js) → Gateway (3000) → Core (3001) → Database (MongoDB)
```

### Request Flow:
1. **Client** sends requests to Gateway app (port 3000)
2. **Gateway** validates, authenticates, and proxies requests to Core app (port 3001)
3. **Core** processes business logic and database operations
4. **Response** flows back through Gateway to Client

## Authentication System

### Token-Based Authentication (Opaque + Refresh Pattern)
- **Access Token**: Short-lived (15 minutes), opaque token for API access
- **Refresh Token**: Long-lived (7 days), used to obtain new access tokens
- **Device Tracking**: Maximum 2 devices per user
- **MFA Support**: TOTP-based multi-factor authentication
- **IP Allowlist**: Admin system access restricted by IP

### Authentication Headers
```http
Authorization: Bearer <access_token>
Content-Type: application/json
```

### Authentication Flow
1. **Login**: `POST /api/auth/login` → Returns access + refresh tokens
2. **API Calls**: Include `Authorization: Bearer <access_token>` header
3. **Token Refresh**: `POST /api/auth/refresh` when access token expires
4. **Logout**: `POST /api/auth/logout` to invalidate tokens

## Gateway App API Endpoints

### Base URL: `http://localhost:3000`

#### Health & Status
```http
GET /api                    # Gateway status check
GET /api/health            # Gateway health information
```

#### Authentication (Forwarded to Core)
```http
POST /api/auth/login       # User login
POST /api/auth/register    # User registration  
POST /api/auth/refresh     # Refresh access token
POST /api/auth/logout      # User logout
GET  /api/auth/verify      # Verify current token
```

#### Proxy to Core Services
```http
ALL /api/core/*           # All requests proxied to Core app
```

**Example Proxy Usage:**
- `GET /api/core/admin/users` → Proxied to `GET http://localhost:3001/api/admin/users`
- `POST /api/core/coder/content` → Proxied to `POST http://localhost:3001/api/coder/content`

## Core App API Endpoints

### Base URL: `http://localhost:3001`

#### Health & Status
```http
GET /api                    # Core status check
GET /api/health            # Core health information
```

#### Authentication Module
```http
POST /api/auth/login       # User authentication
POST /api/auth/register    # User registration
POST /api/auth/refresh     # Token refresh
POST /api/auth/logout      # User logout
POST /api/auth/validate    # Token validation (internal)
GET  /api/auth/verify      # Token verification
```

#### Admin Module
```http
# User Management
GET    /api/admin/users           # List all users
POST   /api/admin/users           # Create new user
PUT    /api/admin/users/:id       # Update user
DELETE /api/admin/users/:id       # Delete user

# Organization Management  
GET    /api/admin/tenants         # List organizations
POST   /api/admin/tenants         # Create organization

# Permissions & Roles
GET    /api/admin/permissions     # List permissions
GET    /api/admin/audit-logs      # View audit logs
```

#### Coder Module (CMS)
```http
# Content Management
GET    /api/coder/content         # List content
POST   /api/coder/content         # Create content
PUT    /api/coder/content/:id     # Update content
DELETE /api/coder/content/:id     # Delete content

# Filters & Search
GET    /api/coder/filters         # List filters
POST   /api/coder/filters         # Create filter

# Forms Management
GET    /api/coder/forms           # List forms
POST   /api/coder/forms           # Create form

# Theme Management
GET    /api/coder/themes          # List themes
POST   /api/coder/themes          # Create theme

# Newsletter Management
GET    /api/coder/newsletter      # List newsletters
POST   /api/coder/newsletter      # Create newsletter
```

## Data Models & Schemas

### User Schema
```typescript
interface User {
  id: string;
  email: string;              // Unique, required
  username?: string;          // Optional, unique
  firstName?: string;
  lastName?: string;
  roles: string[];           // Default: ['user']
  organizationId?: string;   // Reference to Organization
  isActive: boolean;         // Default: true
  isEmailVerified: boolean;  // Default: false
  lastLoginAt?: Date;
  createdAt: Date;
  updatedAt: Date;
}
```

### Organization Schema
```typescript
interface Organization {
  id: string;
  name: string;              // Required
  domain?: string;           // Optional, unique
  settings: {
    allowedDomains?: string[];
    maxUsers?: number;       // Default: 100
    features: string[];      // Default: []
  };
  isActive: boolean;         // Default: true
  subscriptionPlan?: string;
  subscriptionExpires?: Date;
  createdAt: Date;
  updatedAt: Date;
}
```

### Content Schema
```typescript
interface Content {
  id: string;
  title: string;             // Required
  slug: string;              // Auto-generated from title
  body: string;              // Required
  excerpt?: string;
  status: 'draft' | 'published' | 'archived';
  type: 'page' | 'post' | 'article';
  authorId: string;          // Reference to User
  tags: string[];
  metadata: Record<string, any>;
  publishedAt?: Date;
  createdAt: Date;
  updatedAt: Date;
}
```

## API Request/Response Examples

### Authentication

#### Login Request
```http
POST /api/auth/login
Content-Type: application/json

{
  "email": "<EMAIL>",
  "password": "password123",
  "totpCode": "123456"  // Optional, required if MFA enabled
}
```

#### Login Response
```json
{
  "accessToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "refreshToken": "refresh_token_here",
  "user": {
    "id": "user_id",
    "email": "<EMAIL>",
    "username": "johndoe",
    "roles": ["user"],
    "organizationId": "org_id"
  },
  "expiresIn": 900  // 15 minutes in seconds
}
```

#### Registration Request
```http
POST /api/auth/register
Content-Type: application/json

{
  "email": "<EMAIL>",
  "password": "securepassword123",
  "username": "newuser",
  "firstName": "John",
  "lastName": "Doe"
}
```

### Content Management

#### Create Content Request
```http
POST /api/coder/content
Authorization: Bearer <access_token>
Content-Type: application/json

{
  "title": "Sample Article",
  "body": "This is the article content...",
  "excerpt": "Brief description",
  "type": "article",
  "status": "draft",
  "tags": ["technology", "tutorial"],
  "metadata": {
    "seoTitle": "Sample Article - Tech Tutorial",
    "seoDescription": "Learn about technology"
  }
}
```

#### Content Response
```json
{
  "id": "content_id",
  "title": "Sample Article",
  "slug": "sample-article",
  "body": "This is the article content...",
  "excerpt": "Brief description",
  "status": "draft",
  "type": "article",
  "authorId": "user_id",
  "tags": ["technology", "tutorial"],
  "metadata": {
    "seoTitle": "Sample Article - Tech Tutorial",
    "seoDescription": "Learn about technology"
  },
  "createdAt": "2025-07-12T16:30:00.000Z",
  "updatedAt": "2025-07-12T16:30:00.000Z"
}
```

## Error Handling

### Standard Error Response Format
```json
{
  "statusCode": 400,
  "message": "Validation failed",
  "error": "Bad Request",
  "details": [
    {
      "field": "email",
      "message": "Email is required"
    }
  ]
}
```

### Common HTTP Status Codes
- `200` - Success
- `201` - Created
- `400` - Bad Request (validation errors)
- `401` - Unauthorized (invalid/missing token)
- `403` - Forbidden (insufficient permissions)
- `404` - Not Found
- `429` - Too Many Requests (rate limited)
- `500` - Internal Server Error

## Rate Limiting

### Gateway Rate Limits
- **Default**: 100 requests per minute per IP
- **Headers Returned**:
  ```http
  X-RateLimit-Limit: 100
  X-RateLimit-Remaining: 95
  X-RateLimit-Reset: 1625097600
  ```

## Security Features

### CORS Configuration
- **Allowed Origins**: Configurable via `CORS_ORIGIN` environment variable
- **Credentials**: Enabled for cookie-based authentication
- **Methods**: GET, POST, PUT, DELETE, PATCH, OPTIONS

### Security Headers (Helmet)
- Content Security Policy
- X-Frame-Options: DENY
- X-Content-Type-Options: nosniff
- Referrer-Policy: same-origin

## Environment Configuration

### Required Environment Variables
```bash
# Service Configuration
GATEWAY_PORT=3000
CORE_PORT=3001
CORE_SERVICE_URL=http://localhost:3001

# Database
MONGO_URI=mongodb://localhost:27017/legalyard

# Authentication
ACCESS_TOKEN_SECRET=your-secret-key
REFRESH_TOKEN_SECRET=your-refresh-secret
ACCESS_TOKEN_EXPIRY=15m
REFRESH_TOKEN_EXPIRY=7d

# Security
ALLOWED_IPS=127.0.0.1,::1
CORS_ORIGIN=http://localhost:3000

# Rate Limiting
THROTTLE_TTL=60000
THROTTLE_LIMIT=100
```

## Development URLs

### Swagger Documentation
- **Gateway API**: http://localhost:3000/api/docs
- **Core API**: http://localhost:3001/api/docs

### Health Checks
- **Gateway Health**: http://localhost:3000/api/health
- **Core Health**: http://localhost:3001/api/health

## Next.js Integration Guidelines

### Recommended API Client Setup
```typescript
// lib/api.ts
const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3000';

export const apiClient = {
  baseURL: API_BASE_URL,
  headers: {
    'Content-Type': 'application/json',
  },
};
```

### Authentication Integration
```typescript
// Use Gateway endpoints for all API calls
const loginUser = async (credentials) => {
  const response = await fetch(`${API_BASE_URL}/api/auth/login`, {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify(credentials),
  });
  return response.json();
};
```

### Proxy Usage for Core Services
```typescript
// Access Core services through Gateway proxy
const getUsers = async (token) => {
  const response = await fetch(`${API_BASE_URL}/api/core/admin/users`, {
    headers: { 'Authorization': `Bearer ${token}` },
  });
  return response.json();
};
```

## Testing & Validation

### API Testing Commands
```bash
# Test Gateway health
curl http://localhost:3000/api/health

# Test Core health
curl http://localhost:3001/api/health

# Test authentication
curl -X POST http://localhost:3000/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","password":"password123"}'

# Test authenticated endpoint
curl http://localhost:3000/api/core/admin/users \
  -H "Authorization: Bearer <access_token>"
```

### Validation Rules
- **Email**: Must be valid email format, unique across system
- **Password**: Minimum 8 characters, must contain letters and numbers
- **Username**: Optional, 3-50 characters, alphanumeric + underscore
- **Content Title**: Required, 1-200 characters
- **Content Body**: Required, minimum 10 characters

This documentation provides comprehensive information for AI agents to understand the API structure and build accurate Next.js integrations.
